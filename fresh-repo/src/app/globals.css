@tailwind base;
@tailwind components;
@tailwind utilities;

/* Performance optimizations */
@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-feature-settings: 'rlig' 1, 'calt' 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Loading states */
@layer components {
  .loading-skeleton {
    @apply animate-pulse bg-gray-200 rounded;
  }

  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-blue-600;
  }
}
