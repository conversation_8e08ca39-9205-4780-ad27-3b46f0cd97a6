{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'\nimport { NextResponse } from 'next/server'\nimport type { NextRequest } from 'next/server'\n\nexport async function middleware(req: NextRequest) {\n  const res = NextResponse.next()\n  const supabase = createMiddlewareClient({ req, res })\n\n  const {\n    data: { user },\n  } = await supabase.auth.getUser()\n\n  // If user is not signed in and the current path is not /auth/login, redirect to login\n  if (!user && !req.nextUrl.pathname.startsWith('/auth')) {\n    return NextResponse.redirect(new URL('/auth/login', req.url))\n  }\n\n  // If user is signed in and trying to access login page, redirect to dashboard\n  if (user && req.nextUrl.pathname.startsWith('/auth/login')) {\n    return NextResponse.redirect(new URL('/', req.url))\n  }\n\n  return res\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */\n    '/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;AAGO,eAAe,WAAW,GAAgB;IAC/C,MAAM,MAAM,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC7B,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,yBAAsB,AAAD,EAAE;QAAE;QAAK;IAAI;IAEnD,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,sFAAsF;IACtF,IAAI,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU;QACtD,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,eAAe,IAAI,GAAG;IAC7D;IAEA,8EAA8E;IAC9E,IAAI,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,gBAAgB;QAC1D,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,IAAI,GAAG;IACnD;IAEA,OAAO;AACT;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}