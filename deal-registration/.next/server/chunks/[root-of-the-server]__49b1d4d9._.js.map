{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient, createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const createClientComponentClient = () => {\n  return createBrowserClient(supabaseUrl, supabaseAnonKey)\n}\n\n// Server-side Supabase client (simplified for development)\nexport const createServerComponentClient = () => {\n  return createClient(supabaseUrl, supabaseAnonKey)\n}\n\n// Admin client with service role key\nexport const createAdminClient = () => {\n  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n  return createClient(supabaseUrl, serviceRoleKey, {\n    auth: {\n      autoRefreshToken: false,\n      persistSession: false\n    }\n  })\n}\n\n// Database types\nexport type Database = {\n  public: {\n    Tables: {\n      resellers: {\n        Row: {\n          id: string\n          name: string\n          email: string\n          territory: string\n          tier: 'gold' | 'silver' | 'bronze'\n          status: 'active' | 'inactive'\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          name: string\n          email: string\n          territory: string\n          tier: 'gold' | 'silver' | 'bronze'\n          status?: 'active' | 'inactive'\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          name?: string\n          email?: string\n          territory?: string\n          tier?: 'gold' | 'silver' | 'bronze'\n          status?: 'active' | 'inactive'\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      end_users: {\n        Row: {\n          id: string\n          company_name: string\n          contact_name: string\n          contact_email: string\n          territory: string\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          company_name: string\n          contact_name: string\n          contact_email: string\n          territory: string\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          company_name?: string\n          contact_name?: string\n          contact_email?: string\n          territory?: string\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      products: {\n        Row: {\n          id: string\n          name: string\n          category: string\n          list_price: number\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          name: string\n          category: string\n          list_price: number\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          name?: string\n          category?: string\n          list_price?: number\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      deals: {\n        Row: {\n          id: string\n          reseller_id: string\n          end_user_id: string\n          assigned_reseller_id: string | null\n          status: 'pending' | 'assigned' | 'disputed' | 'approved' | 'rejected'\n          total_value: number\n          submission_date: string\n          assignment_date: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          reseller_id: string\n          end_user_id: string\n          assigned_reseller_id?: string | null\n          status?: 'pending' | 'assigned' | 'disputed' | 'approved' | 'rejected'\n          total_value: number\n          submission_date?: string\n          assignment_date?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          reseller_id?: string\n          end_user_id?: string\n          assigned_reseller_id?: string | null\n          status?: 'pending' | 'assigned' | 'disputed' | 'approved' | 'rejected'\n          total_value?: number\n          submission_date?: string\n          assignment_date?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      deal_products: {\n        Row: {\n          id: string\n          deal_id: string\n          product_id: string\n          quantity: number\n          price: number\n          created_at: string\n        }\n        Insert: {\n          id?: string\n          deal_id: string\n          product_id: string\n          quantity: number\n          price: number\n          created_at?: string\n        }\n        Update: {\n          id?: string\n          deal_id?: string\n          product_id?: string\n          quantity?: number\n          price?: number\n          created_at?: string\n        }\n      }\n      deal_conflicts: {\n        Row: {\n          id: string\n          deal_id: string\n          competing_deal_id: string\n          conflict_type: 'duplicate_end_user' | 'territory_overlap' | 'timing_conflict'\n          resolution_status: 'pending' | 'resolved' | 'dismissed'\n          assigned_to_staff: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          deal_id: string\n          competing_deal_id: string\n          conflict_type: 'duplicate_end_user' | 'territory_overlap' | 'timing_conflict'\n          resolution_status?: 'pending' | 'resolved' | 'dismissed'\n          assigned_to_staff?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          deal_id?: string\n          competing_deal_id?: string\n          conflict_type?: 'duplicate_end_user' | 'territory_overlap' | 'timing_conflict'\n          resolution_status?: 'pending' | 'resolved' | 'dismissed'\n          assigned_to_staff?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      staff_users: {\n        Row: {\n          id: string\n          email: string\n          name: string\n          role: 'admin' | 'manager' | 'staff'\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          email: string\n          name: string\n          role?: 'admin' | 'manager' | 'staff'\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          email?: string\n          name?: string\n          role?: 'admin' | 'manager' | 'staff'\n          created_at?: string\n          updated_at?: string\n        }\n      }\n    }\n    Views: {\n      [_ in never]: never\n    }\n    Functions: {\n      [_ in never]: never\n    }\n    Enums: {\n      [_ in never]: never\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;;;AAGA,MAAM;AACN,MAAM;AAGC,MAAM,8BAA8B;IACzC,OAAO,CAAA,GAAA,4KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;AAC1C;AAGO,MAAM,8BAA8B;IACzC,OAAO,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AACnC;AAGO,MAAM,oBAAoB;IAC/B,MAAM,iBAAiB,QAAQ,GAAG,CAAC,yBAAyB;IAC5D,OAAO,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,gBAAgB;QAC/C,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/lib/types.ts"], "sourcesContent": ["import { z } from 'zod'\n\n// Enums\nexport const ResellerTier = z.enum(['gold', 'silver', 'bronze'])\nexport const UserStatus = z.enum(['active', 'inactive'])\nexport const DealStatus = z.enum(['pending', 'assigned', 'disputed', 'approved', 'rejected'])\nexport const ConflictType = z.enum(['duplicate_end_user', 'territory_overlap', 'timing_conflict'])\nexport const ResolutionStatus = z.enum(['pending', 'resolved', 'dismissed'])\nexport const StaffRole = z.enum(['admin', 'manager', 'staff'])\n\n// Base schemas\nexport const ResellerSchema = z.object({\n  id: z.string().uuid().optional(),\n  name: z.string().min(1, 'Reseller name is required'),\n  email: z.string().email('Valid email is required'),\n  territory: z.string().min(1, 'Territory is required'),\n  tier: ResellerTier,\n  status: UserStatus.default('active'),\n  created_at: z.string().optional(),\n  updated_at: z.string().optional(),\n})\n\nexport const EndUserSchema = z.object({\n  id: z.string().uuid().optional(),\n  company_name: z.string().min(1, 'Company name is required'),\n  contact_name: z.string().min(1, 'Contact name is required'),\n  contact_email: z.string().email('Valid email is required'),\n  territory: z.string().min(1, 'Territory is required'),\n  created_at: z.string().optional(),\n  updated_at: z.string().optional(),\n})\n\nexport const ProductSchema = z.object({\n  id: z.string().uuid().optional(),\n  name: z.string().min(1, 'Product name is required'),\n  category: z.string().min(1, 'Category is required'),\n  list_price: z.number().positive('Price must be positive'),\n  created_at: z.string().optional(),\n  updated_at: z.string().optional(),\n})\n\nexport const DealProductSchema = z.object({\n  id: z.string().uuid().optional(),\n  deal_id: z.string().uuid().optional(),\n  product_id: z.string().uuid(),\n  quantity: z.number().int().positive('Quantity must be positive'),\n  price: z.number().positive('Price must be positive'),\n  created_at: z.string().optional(),\n})\n\nexport const DealSchema = z.object({\n  id: z.string().uuid().optional(),\n  reseller_id: z.string().uuid(),\n  end_user_id: z.string().uuid(),\n  assigned_reseller_id: z.string().uuid().nullable().optional(),\n  status: DealStatus.default('pending'),\n  total_value: z.number().positive('Total value must be positive'),\n  submission_date: z.string().optional(),\n  assignment_date: z.string().nullable().optional(),\n  created_at: z.string().optional(),\n  updated_at: z.string().optional(),\n})\n\nexport const DealConflictSchema = z.object({\n  id: z.string().uuid().optional(),\n  deal_id: z.string().uuid(),\n  competing_deal_id: z.string().uuid(),\n  conflict_type: ConflictType,\n  resolution_status: ResolutionStatus.default('pending'),\n  assigned_to_staff: z.string().uuid().nullable().optional(),\n  created_at: z.string().optional(),\n  updated_at: z.string().optional(),\n})\n\nexport const StaffUserSchema = z.object({\n  id: z.string().uuid().optional(),\n  email: z.string().email('Valid email is required'),\n  name: z.string().min(1, 'Name is required'),\n  role: StaffRole.default('staff'),\n  created_at: z.string().optional(),\n  updated_at: z.string().optional(),\n})\n\n// Form schemas for creating/updating\nexport const CreateDealSchema = z.object({\n  reseller_id: z.string().uuid('Please select a reseller'),\n  end_user: z.object({\n    id: z.string().uuid().optional(),\n    company_name: z.string().min(1, 'Company name is required'),\n    contact_name: z.string().min(1, 'Contact name is required'),\n    contact_email: z.string().email('Valid email is required'),\n    territory: z.string().min(1, 'Territory is required'),\n  }),\n  products: z.array(z.object({\n    product_id: z.string().uuid('Please select a product'),\n    quantity: z.number().int().positive('Quantity must be positive'),\n    price: z.number().positive('Price must be positive'),\n  })).min(1, 'At least one product is required'),\n})\n\nexport const AssignDealSchema = z.object({\n  deal_id: z.string().uuid(),\n  assigned_reseller_id: z.string().uuid(),\n  reason: z.string().optional(),\n})\n\n// Type exports\nexport type Reseller = z.infer<typeof ResellerSchema>\nexport type EndUser = z.infer<typeof EndUserSchema>\nexport type Product = z.infer<typeof ProductSchema>\nexport type Deal = z.infer<typeof DealSchema>\nexport type DealProduct = z.infer<typeof DealProductSchema>\nexport type DealConflict = z.infer<typeof DealConflictSchema>\nexport type StaffUser = z.infer<typeof StaffUserSchema>\nexport type CreateDeal = z.infer<typeof CreateDealSchema>\nexport type AssignDeal = z.infer<typeof AssignDealSchema>\n\n// Extended types with relationships\nexport type DealWithRelations = Deal & {\n  reseller: Reseller\n  end_user: EndUser\n  assigned_reseller?: Reseller | null\n  products: (DealProduct & { product: Product })[]\n  conflicts: (DealConflict & { competing_deal: Deal })[]\n}\n\nexport type ConflictWithRelations = DealConflict & {\n  deal: DealWithRelations\n  competing_deal: DealWithRelations\n  assigned_staff?: StaffUser | null\n}\n\n// API Response types\nexport type ApiResponse<T> = {\n  data: T | null\n  error: string | null\n  success: boolean\n}\n\nexport type PaginatedResponse<T> = ApiResponse<{\n  items: T[]\n  total: number\n  page: number\n  limit: number\n  totalPages: number\n}>\n\n// Filter and search types\nexport type DealFilters = {\n  status?: string[]\n  reseller_id?: string\n  territory?: string\n  date_from?: string\n  date_to?: string\n  has_conflicts?: boolean\n}\n\nexport type ConflictFilters = {\n  resolution_status?: string[]\n  conflict_type?: string[]\n  assigned_to_staff?: string\n}\n\n// Dashboard metrics\nexport type DashboardMetrics = {\n  total_deals: number\n  pending_deals: number\n  disputed_deals: number\n  assigned_deals: number\n  total_conflicts: number\n  pending_conflicts: number\n  total_value: number\n  avg_resolution_time: number\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;;AAGO,MAAM,eAAe,+KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAAC;IAAQ;IAAU;CAAS;AACxD,MAAM,aAAa,+KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAAC;IAAU;CAAW;AAChD,MAAM,aAAa,+KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAAC;IAAW;IAAY;IAAY;IAAY;CAAW;AACrF,MAAM,eAAe,+KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAAC;IAAsB;IAAqB;CAAkB;AAC1F,MAAM,mBAAmB,+KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAAC;IAAW;IAAY;CAAY;AACpE,MAAM,YAAY,+KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAAC;IAAS;IAAW;CAAQ;AAGtD,MAAM,iBAAiB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,IAAI,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IAC9B,MAAM,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,WAAW,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,MAAM;IACN,QAAQ,WAAW,OAAO,CAAC;IAC3B,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACjC;AAEO,MAAM,gBAAgB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,IAAI,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IAC9B,cAAc,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAChC,cAAc,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAChC,eAAe,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IAChC,WAAW,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACjC;AAEO,MAAM,gBAAgB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,IAAI,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IAC9B,MAAM,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAChC,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACjC;AAEO,MAAM,oBAAoB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,IAAI,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IAC9B,SAAS,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IACnC,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI;IAC3B,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC;IACpC,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC3B,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACjC;AAEO,MAAM,aAAa,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,IAAI,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IAC9B,aAAa,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI;IAC5B,aAAa,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI;IAC5B,sBAAsB,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ,GAAG,QAAQ;IAC3D,QAAQ,WAAW,OAAO,CAAC;IAC3B,aAAa,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC,iBAAiB,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACpC,iBAAiB,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC/C,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACjC;AAEO,MAAM,qBAAqB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,IAAI,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IAC9B,SAAS,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI;IACxB,mBAAmB,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI;IAClC,eAAe;IACf,mBAAmB,iBAAiB,OAAO,CAAC;IAC5C,mBAAmB,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ,GAAG,QAAQ;IACxD,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACjC;AAEO,MAAM,kBAAkB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,IAAI,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IAC9B,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,MAAM,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,MAAM,UAAU,OAAO,CAAC;IACxB,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACjC;AAGO,MAAM,mBAAmB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,aAAa,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC;IAC7B,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACjB,IAAI,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;QAC9B,cAAc,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAChC,cAAc,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAChC,eAAe,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;QAChC,WAAW,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B;IACA,UAAU,+KAAA,CAAA,IAAC,CAAC,KAAK,CAAC,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACzB,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC;QAC5B,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC;QACpC,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC7B,IAAI,GAAG,CAAC,GAAG;AACb;AAEO,MAAM,mBAAmB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,SAAS,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI;IACxB,sBAAsB,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI;IACrC,QAAQ,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC7B", "debugId": null}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/app/api/products/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createServerComponentClient } from '@/lib/supabase'\nimport { ProductSchema } from '@/lib/types'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const supabase = createServerComponentClient()\n    const { searchParams } = new URL(request.url)\n    \n    // Parse query parameters\n    const page = parseInt(searchParams.get('page') || '1')\n    const limit = parseInt(searchParams.get('limit') || '50')\n    const category = searchParams.get('category')\n    const search = searchParams.get('search')\n    \n    const offset = (page - 1) * limit\n    \n    // Build query\n    let query = supabase\n      .from('products')\n      .select('*')\n      .order('name', { ascending: true })\n      .range(offset, offset + limit - 1)\n    \n    // Apply filters\n    if (category) {\n      query = query.eq('category', category)\n    }\n    \n    if (search) {\n      query = query.ilike('name', `%${search}%`)\n    }\n    \n    const { data: products, error, count } = await query\n    \n    if (error) {\n      console.error('Error fetching products:', error)\n      return NextResponse.json(\n        { error: 'Failed to fetch products', details: error.message },\n        { status: 500 }\n      )\n    }\n    \n    // Get total count for pagination\n    const { count: totalCount } = await supabase\n      .from('products')\n      .select('*', { count: 'exact', head: true })\n    \n    return NextResponse.json({\n      data: {\n        items: products || [],\n        total: totalCount || 0,\n        page,\n        limit,\n        totalPages: Math.ceil((totalCount || 0) / limit)\n      },\n      success: true,\n      error: null\n    })\n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const supabase = createServerComponentClient()\n    const body = await request.json()\n    \n    // Validate request body\n    const validation = ProductSchema.safeParse(body)\n    if (!validation.success) {\n      return NextResponse.json(\n        { \n          error: 'Invalid request data', \n          details: validation.error.issues \n        },\n        { status: 400 }\n      )\n    }\n    \n    const productData = validation.data\n    \n    const { data: insertedProduct, error } = await supabase\n      .from('products')\n      .insert(productData)\n      .select()\n      .single()\n    \n    if (error) {\n      console.error('Error creating product:', error)\n      return NextResponse.json(\n        { error: 'Failed to create product', details: error.message },\n        { status: 500 }\n      )\n    }\n    \n    return NextResponse.json({\n      data: insertedProduct,\n      success: true,\n      error: null\n    }, { status: 201 })\n    \n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,wHAAA,CAAA,8BAA2B,AAAD;QAC3C,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAE5C,yBAAyB;QACzB,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;QAE5B,cAAc;QACd,IAAI,QAAQ,SACT,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,QAAQ;YAAE,WAAW;QAAK,GAChC,KAAK,CAAC,QAAQ,SAAS,QAAQ;QAElC,gBAAgB;QAChB,IAAI,UAAU;YACZ,QAAQ,MAAM,EAAE,CAAC,YAAY;QAC/B;QAEA,IAAI,QAAQ;YACV,QAAQ,MAAM,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QAC3C;QAEA,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;QAE/C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAA4B,SAAS,MAAM,OAAO;YAAC,GAC5D;gBAAE,QAAQ;YAAI;QAElB;QAEA,iCAAiC;QACjC,MAAM,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,YACL,MAAM,CAAC,KAAK;YAAE,OAAO;YAAS,MAAM;QAAK;QAE5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,MAAM;gBACJ,OAAO,YAAY,EAAE;gBACrB,OAAO,cAAc;gBACrB;gBACA;gBACA,YAAY,KAAK,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI;YAC5C;YACA,SAAS;YACT,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,wHAAA,CAAA,8BAA2B,AAAD;QAC3C,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,wBAAwB;QACxB,MAAM,aAAa,qHAAA,CAAA,gBAAa,CAAC,SAAS,CAAC;QAC3C,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,SAAS,WAAW,KAAK,CAAC,MAAM;YAClC,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,cAAc,WAAW,IAAI;QAEnC,MAAM,EAAE,MAAM,eAAe,EAAE,KAAK,EAAE,GAAG,MAAM,SAC5C,IAAI,CAAC,YACL,MAAM,CAAC,aACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAA4B,SAAS,MAAM,OAAO;YAAC,GAC5D;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,MAAM;YACN,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}