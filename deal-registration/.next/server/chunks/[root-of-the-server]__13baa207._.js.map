{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient, createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const createClientComponentClient = () => {\n  return createBrowserClient(supabaseUrl, supabaseAnonKey)\n}\n\n// Server-side Supabase client (simplified for development)\nexport const createServerComponentClient = () => {\n  return createClient(supabaseUrl, supabaseAnonKey)\n}\n\n// Admin client with service role key\nexport const createAdminClient = () => {\n  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n  return createClient(supabaseUrl, serviceRoleKey, {\n    auth: {\n      autoRefreshToken: false,\n      persistSession: false\n    }\n  })\n}\n\n// Database types\nexport type Database = {\n  public: {\n    Tables: {\n      resellers: {\n        Row: {\n          id: string\n          name: string\n          email: string\n          territory: string\n          tier: 'gold' | 'silver' | 'bronze'\n          status: 'active' | 'inactive'\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          name: string\n          email: string\n          territory: string\n          tier: 'gold' | 'silver' | 'bronze'\n          status?: 'active' | 'inactive'\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          name?: string\n          email?: string\n          territory?: string\n          tier?: 'gold' | 'silver' | 'bronze'\n          status?: 'active' | 'inactive'\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      end_users: {\n        Row: {\n          id: string\n          company_name: string\n          contact_name: string\n          contact_email: string\n          territory: string\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          company_name: string\n          contact_name: string\n          contact_email: string\n          territory: string\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          company_name?: string\n          contact_name?: string\n          contact_email?: string\n          territory?: string\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      products: {\n        Row: {\n          id: string\n          name: string\n          category: string\n          list_price: number\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          name: string\n          category: string\n          list_price: number\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          name?: string\n          category?: string\n          list_price?: number\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      deals: {\n        Row: {\n          id: string\n          reseller_id: string\n          end_user_id: string\n          assigned_reseller_id: string | null\n          status: 'pending' | 'assigned' | 'disputed' | 'approved' | 'rejected'\n          total_value: number\n          submission_date: string\n          assignment_date: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          reseller_id: string\n          end_user_id: string\n          assigned_reseller_id?: string | null\n          status?: 'pending' | 'assigned' | 'disputed' | 'approved' | 'rejected'\n          total_value: number\n          submission_date?: string\n          assignment_date?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          reseller_id?: string\n          end_user_id?: string\n          assigned_reseller_id?: string | null\n          status?: 'pending' | 'assigned' | 'disputed' | 'approved' | 'rejected'\n          total_value?: number\n          submission_date?: string\n          assignment_date?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      deal_products: {\n        Row: {\n          id: string\n          deal_id: string\n          product_id: string\n          quantity: number\n          price: number\n          created_at: string\n        }\n        Insert: {\n          id?: string\n          deal_id: string\n          product_id: string\n          quantity: number\n          price: number\n          created_at?: string\n        }\n        Update: {\n          id?: string\n          deal_id?: string\n          product_id?: string\n          quantity?: number\n          price?: number\n          created_at?: string\n        }\n      }\n      deal_conflicts: {\n        Row: {\n          id: string\n          deal_id: string\n          competing_deal_id: string\n          conflict_type: 'duplicate_end_user' | 'territory_overlap' | 'timing_conflict'\n          resolution_status: 'pending' | 'resolved' | 'dismissed'\n          assigned_to_staff: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          deal_id: string\n          competing_deal_id: string\n          conflict_type: 'duplicate_end_user' | 'territory_overlap' | 'timing_conflict'\n          resolution_status?: 'pending' | 'resolved' | 'dismissed'\n          assigned_to_staff?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          deal_id?: string\n          competing_deal_id?: string\n          conflict_type?: 'duplicate_end_user' | 'territory_overlap' | 'timing_conflict'\n          resolution_status?: 'pending' | 'resolved' | 'dismissed'\n          assigned_to_staff?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      staff_users: {\n        Row: {\n          id: string\n          email: string\n          name: string\n          role: 'admin' | 'manager' | 'staff'\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          email: string\n          name: string\n          role?: 'admin' | 'manager' | 'staff'\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          email?: string\n          name?: string\n          role?: 'admin' | 'manager' | 'staff'\n          created_at?: string\n          updated_at?: string\n        }\n      }\n    }\n    Views: {\n      [_ in never]: never\n    }\n    Functions: {\n      [_ in never]: never\n    }\n    Enums: {\n      [_ in never]: never\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;;;AAGA,MAAM;AACN,MAAM;AAGC,MAAM,8BAA8B;IACzC,OAAO,CAAA,GAAA,4KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;AAC1C;AAGO,MAAM,8BAA8B;IACzC,OAAO,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AACnC;AAGO,MAAM,oBAAoB;IAC/B,MAAM,iBAAiB,QAAQ,GAAG,CAAC,yBAAyB;IAC5D,OAAO,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,gBAAgB;QAC/C,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/app/api/end-users/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createServerComponentClient } from '@/lib/supabase'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const supabase = createServerComponentClient()\n    const { searchParams } = new URL(request.url)\n    \n    // Parse query parameters\n    const page = parseInt(searchParams.get('page') || '1')\n    const limit = parseInt(searchParams.get('limit') || '50')\n    const search = searchParams.get('search') || ''\n    const territory = searchParams.get('territory') || ''\n    \n    const offset = (page - 1) * limit\n    \n    // Build query\n    let query = supabase\n      .from('end_users')\n      .select('*', { count: 'exact' })\n      .order('created_at', { ascending: false })\n      .range(offset, offset + limit - 1)\n    \n    // Apply filters\n    if (search) {\n      query = query.or(`company_name.ilike.%${search}%,contact_name.ilike.%${search}%,contact_email.ilike.%${search}%`)\n    }\n    \n    if (territory) {\n      query = query.eq('territory', territory)\n    }\n    \n    const { data: endUsers, error, count } = await query\n    \n    if (error) {\n      console.error('Error fetching end users:', error)\n      return NextResponse.json({\n        success: false,\n        error: 'Failed to fetch end users',\n        details: error\n      }, { status: 500 })\n    }\n    \n    const totalPages = Math.ceil((count || 0) / limit)\n    \n    return NextResponse.json({\n      success: true,\n      data: {\n        items: endUsers || [],\n        total: count || 0,\n        page,\n        limit,\n        totalPages\n      },\n      error: null\n    })\n    \n  } catch (error) {\n    console.error('Unexpected error in end-users API:', error)\n    return NextResponse.json({\n      success: false,\n      error: 'Internal server error',\n      details: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 })\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const supabase = createServerComponentClient()\n    const body = await request.json()\n    \n    // Validate required fields\n    const { company_name, contact_name, contact_email, territory } = body\n    \n    if (!company_name || !contact_name || !contact_email || !territory) {\n      return NextResponse.json({\n        success: false,\n        error: 'Missing required fields: company_name, contact_name, contact_email, territory'\n      }, { status: 400 })\n    }\n    \n    // Check for duplicate company in same territory\n    const { data: existing } = await supabase\n      .from('end_users')\n      .select('id')\n      .eq('company_name', company_name)\n      .eq('territory', territory)\n      .single()\n    \n    if (existing) {\n      return NextResponse.json({\n        success: false,\n        error: 'End user with this company name already exists in this territory'\n      }, { status: 409 })\n    }\n    \n    // Create new end user\n    const { data: endUser, error } = await supabase\n      .from('end_users')\n      .insert([{\n        company_name,\n        contact_name,\n        contact_email,\n        territory\n      }])\n      .select()\n      .single()\n    \n    if (error) {\n      console.error('Error creating end user:', error)\n      return NextResponse.json({\n        success: false,\n        error: 'Failed to create end user',\n        details: error\n      }, { status: 500 })\n    }\n    \n    return NextResponse.json({\n      success: true,\n      data: endUser,\n      error: null\n    }, { status: 201 })\n    \n  } catch (error) {\n    console.error('Unexpected error in end-users POST:', error)\n    return NextResponse.json({\n      success: false,\n      error: 'Internal server error',\n      details: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,wHAAA,CAAA,8BAA2B,AAAD;QAC3C,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAE5C,yBAAyB;QACzB,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,YAAY,aAAa,GAAG,CAAC,gBAAgB;QAEnD,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;QAE5B,cAAc;QACd,IAAI,QAAQ,SACT,IAAI,CAAC,aACL,MAAM,CAAC,KAAK;YAAE,OAAO;QAAQ,GAC7B,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC,QAAQ,SAAS,QAAQ;QAElC,gBAAgB;QAChB,IAAI,QAAQ;YACV,QAAQ,MAAM,EAAE,CAAC,CAAC,oBAAoB,EAAE,OAAO,sBAAsB,EAAE,OAAO,uBAAuB,EAAE,OAAO,CAAC,CAAC;QAClH;QAEA,IAAI,WAAW;YACb,QAAQ,MAAM,EAAE,CAAC,aAAa;QAChC;QAEA,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;QAE/C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;gBACP,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,aAAa,KAAK,IAAI,CAAC,CAAC,SAAS,CAAC,IAAI;QAE5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,OAAO,YAAY,EAAE;gBACrB,OAAO,SAAS;gBAChB;gBACA;gBACA;YACF;YACA,OAAO;QACT;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,wHAAA,CAAA,8BAA2B,AAAD;QAC3C,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,2BAA2B;QAC3B,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG;QAEjE,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,WAAW;YAClE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,gDAAgD;QAChD,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,aACL,MAAM,CAAC,MACP,EAAE,CAAC,gBAAgB,cACnB,EAAE,CAAC,aAAa,WAChB,MAAM;QAET,IAAI,UAAU;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,sBAAsB;QACtB,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,aACL,MAAM,CAAC;YAAC;gBACP;gBACA;gBACA;gBACA;YACF;SAAE,EACD,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;gBACP,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}