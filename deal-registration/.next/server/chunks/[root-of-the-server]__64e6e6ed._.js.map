{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/app/api/deals/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { mockDeals } from '@/lib/mock-data'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const supabase = createServerComponentClient()\n    const { searchParams } = new URL(request.url)\n    \n    // Parse query parameters\n    const page = parseInt(searchParams.get('page') || '1')\n    const limit = parseInt(searchParams.get('limit') || '10')\n    const status = searchParams.get('status')\n    const reseller_id = searchParams.get('reseller_id')\n    const territory = searchParams.get('territory')\n    const has_conflicts = searchParams.get('has_conflicts')\n    \n    const offset = (page - 1) * limit\n    \n    // Build query\n    let query = supabase\n      .from('deals')\n      .select(`\n        *,\n        reseller:resellers(*),\n        end_user:end_users(*),\n        assigned_reseller:resellers!deals_assigned_reseller_id_fkey(*),\n        products:deal_products(\n          *,\n          product:products(*)\n        ),\n        conflicts:deal_conflicts(\n          *,\n          competing_deal:deals!deal_conflicts_competing_deal_id_fkey(\n            *,\n            reseller:resellers(*),\n            end_user:end_users(*)\n          )\n        )\n      `)\n      .order('created_at', { ascending: false })\n      .range(offset, offset + limit - 1)\n    \n    // Apply filters\n    if (status) {\n      query = query.eq('status', status)\n    }\n    \n    if (reseller_id) {\n      query = query.eq('reseller_id', reseller_id)\n    }\n    \n    if (territory) {\n      query = query.eq('end_users.territory', territory)\n    }\n    \n    if (has_conflicts === 'true') {\n      query = query.not('conflicts', 'is', null)\n    }\n    \n    const { data: deals, error, count } = await query\n    \n    if (error) {\n      console.error('Error fetching deals:', error)\n      return NextResponse.json(\n        { error: 'Failed to fetch deals', details: error.message },\n        { status: 500 }\n      )\n    }\n    \n    // Get total count for pagination\n    const { count: totalCount } = await supabase\n      .from('deals')\n      .select('*', { count: 'exact', head: true })\n    \n    return NextResponse.json({\n      data: {\n        items: deals || [],\n        total: totalCount || 0,\n        page,\n        limit,\n        totalPages: Math.ceil((totalCount || 0) / limit)\n      },\n      success: true,\n      error: null\n    })\n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const supabase = createServerComponentClient()\n    const body = await request.json()\n    \n    // Validate request body\n    const validation = CreateDealSchema.safeParse(body)\n    if (!validation.success) {\n      return NextResponse.json(\n        { \n          error: 'Invalid request data', \n          details: validation.error.issues \n        },\n        { status: 400 }\n      )\n    }\n    \n    const dealData = validation.data\n    \n    // Start transaction\n    const { data: insertedEndUser, error: endUserError } = await supabase\n      .from('end_users')\n      .upsert({\n        id: dealData.end_user.id,\n        company_name: dealData.end_user.company_name,\n        contact_name: dealData.end_user.contact_name,\n        contact_email: dealData.end_user.contact_email,\n        territory: dealData.end_user.territory\n      })\n      .select()\n      .single()\n    \n    if (endUserError) {\n      console.error('Error creating/updating end user:', endUserError)\n      return NextResponse.json(\n        { error: 'Failed to create end user', details: endUserError.message },\n        { status: 500 }\n      )\n    }\n    \n    // Calculate total value\n    const totalValue = dealData.products.reduce(\n      (sum, product) => sum + (product.quantity * product.price), \n      0\n    )\n    \n    // Create deal\n    const { data: insertedDeal, error: dealError } = await supabase\n      .from('deals')\n      .insert({\n        reseller_id: dealData.reseller_id,\n        end_user_id: insertedEndUser.id,\n        total_value: totalValue,\n        status: 'pending'\n      })\n      .select()\n      .single()\n    \n    if (dealError) {\n      console.error('Error creating deal:', dealError)\n      return NextResponse.json(\n        { error: 'Failed to create deal', details: dealError.message },\n        { status: 500 }\n      )\n    }\n    \n    // Create deal products\n    const dealProducts = dealData.products.map(product => ({\n      deal_id: insertedDeal.id,\n      product_id: product.product_id,\n      quantity: product.quantity,\n      price: product.price\n    }))\n    \n    const { error: productsError } = await supabase\n      .from('deal_products')\n      .insert(dealProducts)\n    \n    if (productsError) {\n      console.error('Error creating deal products:', productsError)\n      // Rollback deal creation\n      await supabase.from('deals').delete().eq('id', insertedDeal.id)\n      return NextResponse.json(\n        { error: 'Failed to create deal products', details: productsError.message },\n        { status: 500 }\n      )\n    }\n    \n    // Detect conflicts\n    const conflictEngine = new ConflictDetectionEngine()\n    const conflictResult = await conflictEngine.detectConflicts({\n      end_user: insertedEndUser,\n      reseller_id: dealData.reseller_id,\n      total_value: totalValue,\n      submission_date: insertedDeal.created_at\n    })\n    \n    // Create conflict records if any\n    if (conflictResult.hasConflicts) {\n      await conflictEngine.createConflictRecords(insertedDeal.id, conflictResult.conflicts)\n      \n      // Update deal status to disputed if high-severity conflicts\n      const hasHighSeverityConflicts = conflictResult.conflicts.some(c => c.severity === 'high')\n      if (hasHighSeverityConflicts) {\n        await supabase\n          .from('deals')\n          .update({ status: 'disputed' })\n          .eq('id', insertedDeal.id)\n      }\n    }\n    \n    // Fetch the complete deal with relationships\n    const { data: completeDeal, error: fetchError } = await supabase\n      .from('deals')\n      .select(`\n        *,\n        reseller:resellers(*),\n        end_user:end_users(*),\n        products:deal_products(\n          *,\n          product:products(*)\n        ),\n        conflicts:deal_conflicts(\n          *,\n          competing_deal:deals!deal_conflicts_competing_deal_id_fkey(\n            *,\n            reseller:resellers(*),\n            end_user:end_users(*)\n          )\n        )\n      `)\n      .eq('id', insertedDeal.id)\n      .single()\n    \n    if (fetchError) {\n      console.error('Error fetching complete deal:', fetchError)\n      return NextResponse.json(\n        { error: 'Deal created but failed to fetch details', details: fetchError.message },\n        { status: 500 }\n      )\n    }\n    \n    return NextResponse.json({\n      data: {\n        deal: completeDeal,\n        conflicts: conflictResult\n      },\n      success: true,\n      error: null\n    }, { status: 201 })\n    \n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,WAAW;QACjB,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAE5C,yBAAyB;QACzB,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,cAAc,aAAa,GAAG,CAAC;QACrC,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,gBAAgB,aAAa,GAAG,CAAC;QAEvC,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;QAE5B,cAAc;QACd,IAAI,QAAQ,SACT,IAAI,CAAC,SACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;MAiBT,CAAC,EACA,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC,QAAQ,SAAS,QAAQ;QAElC,gBAAgB;QAChB,IAAI,QAAQ;YACV,QAAQ,MAAM,EAAE,CAAC,UAAU;QAC7B;QAEA,IAAI,aAAa;YACf,QAAQ,MAAM,EAAE,CAAC,eAAe;QAClC;QAEA,IAAI,WAAW;YACb,QAAQ,MAAM,EAAE,CAAC,uBAAuB;QAC1C;QAEA,IAAI,kBAAkB,QAAQ;YAC5B,QAAQ,MAAM,GAAG,CAAC,aAAa,MAAM;QACvC;QAEA,MAAM,EAAE,MAAM,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;QAE5C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAyB,SAAS,MAAM,OAAO;YAAC,GACzD;gBAAE,QAAQ;YAAI;QAElB;QAEA,iCAAiC;QACjC,MAAM,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,SACL,MAAM,CAAC,KAAK;YAAE,OAAO;YAAS,MAAM;QAAK;QAE5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,MAAM;gBACJ,OAAO,SAAS,EAAE;gBAClB,OAAO,cAAc;gBACrB;gBACA;gBACA,YAAY,KAAK,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI;YAC5C;YACA,SAAS;YACT,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW;QACjB,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,wBAAwB;QACxB,MAAM,aAAa,iBAAiB,SAAS,CAAC;QAC9C,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,SAAS,WAAW,KAAK,CAAC,MAAM;YAClC,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,WAAW,WAAW,IAAI;QAEhC,oBAAoB;QACpB,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAC1D,IAAI,CAAC,aACL,MAAM,CAAC;YACN,IAAI,SAAS,QAAQ,CAAC,EAAE;YACxB,cAAc,SAAS,QAAQ,CAAC,YAAY;YAC5C,cAAc,SAAS,QAAQ,CAAC,YAAY;YAC5C,eAAe,SAAS,QAAQ,CAAC,aAAa;YAC9C,WAAW,SAAS,QAAQ,CAAC,SAAS;QACxC,GACC,MAAM,GACN,MAAM;QAET,IAAI,cAAc;YAChB,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAA6B,SAAS,aAAa,OAAO;YAAC,GACpE;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,MAAM,aAAa,SAAS,QAAQ,CAAC,MAAM,CACzC,CAAC,KAAK,UAAY,MAAO,QAAQ,QAAQ,GAAG,QAAQ,KAAK,EACzD;QAGF,cAAc;QACd,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,SACL,MAAM,CAAC;YACN,aAAa,SAAS,WAAW;YACjC,aAAa,gBAAgB,EAAE;YAC/B,aAAa;YACb,QAAQ;QACV,GACC,MAAM,GACN,MAAM;QAET,IAAI,WAAW;YACb,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAyB,SAAS,UAAU,OAAO;YAAC,GAC7D;gBAAE,QAAQ;YAAI;QAElB;QAEA,uBAAuB;QACvB,MAAM,eAAe,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAA,UAAW,CAAC;gBACrD,SAAS,aAAa,EAAE;gBACxB,YAAY,QAAQ,UAAU;gBAC9B,UAAU,QAAQ,QAAQ;gBAC1B,OAAO,QAAQ,KAAK;YACtB,CAAC;QAED,MAAM,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,iBACL,MAAM,CAAC;QAEV,IAAI,eAAe;YACjB,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,yBAAyB;YACzB,MAAM,SAAS,IAAI,CAAC,SAAS,MAAM,GAAG,EAAE,CAAC,MAAM,aAAa,EAAE;YAC9D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAkC,SAAS,cAAc,OAAO;YAAC,GAC1E;gBAAE,QAAQ;YAAI;QAElB;QAEA,mBAAmB;QACnB,MAAM,iBAAiB,IAAI;QAC3B,MAAM,iBAAiB,MAAM,eAAe,eAAe,CAAC;YAC1D,UAAU;YACV,aAAa,SAAS,WAAW;YACjC,aAAa;YACb,iBAAiB,aAAa,UAAU;QAC1C;QAEA,iCAAiC;QACjC,IAAI,eAAe,YAAY,EAAE;YAC/B,MAAM,eAAe,qBAAqB,CAAC,aAAa,EAAE,EAAE,eAAe,SAAS;YAEpF,4DAA4D;YAC5D,MAAM,2BAA2B,eAAe,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;YACnF,IAAI,0BAA0B;gBAC5B,MAAM,SACH,IAAI,CAAC,SACL,MAAM,CAAC;oBAAE,QAAQ;gBAAW,GAC5B,EAAE,CAAC,MAAM,aAAa,EAAE;YAC7B;QACF;QAEA,6CAA6C;QAC7C,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACrD,IAAI,CAAC,SACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;MAgBT,CAAC,EACA,EAAE,CAAC,MAAM,aAAa,EAAE,EACxB,MAAM;QAET,IAAI,YAAY;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAA4C,SAAS,WAAW,OAAO;YAAC,GACjF;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,MAAM;gBACJ,MAAM;gBACN,WAAW;YACb;YACA,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}