{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/app/auth/callback/route.ts"], "sourcesContent": ["import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'\nimport { cookies } from 'next/headers'\nimport { NextRequest, NextResponse } from 'next/server'\n\nexport async function GET(request: NextRequest) {\n  const requestUrl = new URL(request.url)\n  const code = requestUrl.searchParams.get('code')\n\n  if (code) {\n    const cookieStore = cookies()\n    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })\n    \n    try {\n      await supabase.auth.exchangeCodeForSession(code)\n    } catch (error) {\n      console.error('Error exchanging code for session:', error)\n      return NextResponse.redirect(`${requestUrl.origin}/auth/login?error=auth_error`)\n    }\n  }\n\n  // URL to redirect to after sign in process completes\n  return NextResponse.redirect(`${requestUrl.origin}/`)\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,MAAM,aAAa,IAAI,IAAI,QAAQ,GAAG;IACtC,MAAM,OAAO,WAAW,YAAY,CAAC,GAAG,CAAC;IAEzC,IAAI,MAAM;QACR,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QAC1B,MAAM,WAAW,CAAA,GAAA,0KAAA,CAAA,2BAAwB,AAAD,EAAE;YAAE,SAAS,IAAM;QAAY;QAEvE,IAAI;YACF,MAAM,SAAS,IAAI,CAAC,sBAAsB,CAAC;QAC7C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO,gIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,GAAG,WAAW,MAAM,CAAC,4BAA4B,CAAC;QACjF;IACF;IAEA,qDAAqD;IACrD,OAAO,gIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,GAAG,WAAW,MAAM,CAAC,CAAC,CAAC;AACtD", "debugId": null}}]}