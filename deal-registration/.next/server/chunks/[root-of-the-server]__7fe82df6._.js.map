{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient, createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const createClientComponentClient = () => {\n  return createBrowserClient(supabaseUrl, supabaseAnonKey)\n}\n\n// Server-side Supabase client\nexport const createServerComponentClient = () => {\n  const cookieStore = cookies()\n  \n  return createServerClient(supabaseUrl, supabaseAnonKey, {\n    cookies: {\n      get(name: string) {\n        return cookieStore.get(name)?.value\n      },\n    },\n  })\n}\n\n// Admin client with service role key\nexport const createAdminClient = () => {\n  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n  return createClient(supabaseUrl, serviceRoleKey, {\n    auth: {\n      autoRefreshToken: false,\n      persistSession: false\n    }\n  })\n}\n\n// Database types\nexport type Database = {\n  public: {\n    Tables: {\n      resellers: {\n        Row: {\n          id: string\n          name: string\n          email: string\n          territory: string\n          tier: 'gold' | 'silver' | 'bronze'\n          status: 'active' | 'inactive'\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          name: string\n          email: string\n          territory: string\n          tier: 'gold' | 'silver' | 'bronze'\n          status?: 'active' | 'inactive'\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          name?: string\n          email?: string\n          territory?: string\n          tier?: 'gold' | 'silver' | 'bronze'\n          status?: 'active' | 'inactive'\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      end_users: {\n        Row: {\n          id: string\n          company_name: string\n          contact_name: string\n          contact_email: string\n          territory: string\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          company_name: string\n          contact_name: string\n          contact_email: string\n          territory: string\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          company_name?: string\n          contact_name?: string\n          contact_email?: string\n          territory?: string\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      products: {\n        Row: {\n          id: string\n          name: string\n          category: string\n          list_price: number\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          name: string\n          category: string\n          list_price: number\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          name?: string\n          category?: string\n          list_price?: number\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      deals: {\n        Row: {\n          id: string\n          reseller_id: string\n          end_user_id: string\n          assigned_reseller_id: string | null\n          status: 'pending' | 'assigned' | 'disputed' | 'approved' | 'rejected'\n          total_value: number\n          submission_date: string\n          assignment_date: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          reseller_id: string\n          end_user_id: string\n          assigned_reseller_id?: string | null\n          status?: 'pending' | 'assigned' | 'disputed' | 'approved' | 'rejected'\n          total_value: number\n          submission_date?: string\n          assignment_date?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          reseller_id?: string\n          end_user_id?: string\n          assigned_reseller_id?: string | null\n          status?: 'pending' | 'assigned' | 'disputed' | 'approved' | 'rejected'\n          total_value?: number\n          submission_date?: string\n          assignment_date?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      deal_products: {\n        Row: {\n          id: string\n          deal_id: string\n          product_id: string\n          quantity: number\n          price: number\n          created_at: string\n        }\n        Insert: {\n          id?: string\n          deal_id: string\n          product_id: string\n          quantity: number\n          price: number\n          created_at?: string\n        }\n        Update: {\n          id?: string\n          deal_id?: string\n          product_id?: string\n          quantity?: number\n          price?: number\n          created_at?: string\n        }\n      }\n      deal_conflicts: {\n        Row: {\n          id: string\n          deal_id: string\n          competing_deal_id: string\n          conflict_type: 'duplicate_end_user' | 'territory_overlap' | 'timing_conflict'\n          resolution_status: 'pending' | 'resolved' | 'dismissed'\n          assigned_to_staff: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          deal_id: string\n          competing_deal_id: string\n          conflict_type: 'duplicate_end_user' | 'territory_overlap' | 'timing_conflict'\n          resolution_status?: 'pending' | 'resolved' | 'dismissed'\n          assigned_to_staff?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          deal_id?: string\n          competing_deal_id?: string\n          conflict_type?: 'duplicate_end_user' | 'territory_overlap' | 'timing_conflict'\n          resolution_status?: 'pending' | 'resolved' | 'dismissed'\n          assigned_to_staff?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      staff_users: {\n        Row: {\n          id: string\n          email: string\n          name: string\n          role: 'admin' | 'manager' | 'staff'\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          email: string\n          name: string\n          role?: 'admin' | 'manager' | 'staff'\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          email?: string\n          name?: string\n          role?: 'admin' | 'manager' | 'staff'\n          created_at?: string\n          updated_at?: string\n        }\n      }\n    }\n    Views: {\n      [_ in never]: never\n    }\n    Functions: {\n      [_ in never]: never\n    }\n    Enums: {\n      [_ in never]: never\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AACA;;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,8BAA8B;IACzC,OAAO,CAAA,GAAA,4KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;AAC1C;AAGO,MAAM,8BAA8B;IACzC,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAE1B,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,iBAAiB;QACtD,SAAS;YACP,KAAI,IAAY;gBACd,OAAO,YAAY,GAAG,CAAC,OAAO;YAChC;QACF;IACF;AACF;AAGO,MAAM,oBAAoB;IAC/B,MAAM,iBAAiB,QAAQ,GAAG,CAAC,yBAAyB;IAC5D,OAAO,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,gBAAgB;QAC/C,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/lib/types.ts"], "sourcesContent": ["import { z } from 'zod'\n\n// Enums\nexport const ResellerTier = z.enum(['gold', 'silver', 'bronze'])\nexport const UserStatus = z.enum(['active', 'inactive'])\nexport const DealStatus = z.enum(['pending', 'assigned', 'disputed', 'approved', 'rejected'])\nexport const ConflictType = z.enum(['duplicate_end_user', 'territory_overlap', 'timing_conflict'])\nexport const ResolutionStatus = z.enum(['pending', 'resolved', 'dismissed'])\nexport const StaffRole = z.enum(['admin', 'manager', 'staff'])\n\n// Base schemas\nexport const ResellerSchema = z.object({\n  id: z.string().uuid().optional(),\n  name: z.string().min(1, 'Reseller name is required'),\n  email: z.string().email('Valid email is required'),\n  territory: z.string().min(1, 'Territory is required'),\n  tier: ResellerTier,\n  status: UserStatus.default('active'),\n  created_at: z.string().optional(),\n  updated_at: z.string().optional(),\n})\n\nexport const EndUserSchema = z.object({\n  id: z.string().uuid().optional(),\n  company_name: z.string().min(1, 'Company name is required'),\n  contact_name: z.string().min(1, 'Contact name is required'),\n  contact_email: z.string().email('Valid email is required'),\n  territory: z.string().min(1, 'Territory is required'),\n  created_at: z.string().optional(),\n  updated_at: z.string().optional(),\n})\n\nexport const ProductSchema = z.object({\n  id: z.string().uuid().optional(),\n  name: z.string().min(1, 'Product name is required'),\n  category: z.string().min(1, 'Category is required'),\n  list_price: z.number().positive('Price must be positive'),\n  created_at: z.string().optional(),\n  updated_at: z.string().optional(),\n})\n\nexport const DealProductSchema = z.object({\n  id: z.string().uuid().optional(),\n  deal_id: z.string().uuid().optional(),\n  product_id: z.string().uuid(),\n  quantity: z.number().int().positive('Quantity must be positive'),\n  price: z.number().positive('Price must be positive'),\n  created_at: z.string().optional(),\n})\n\nexport const DealSchema = z.object({\n  id: z.string().uuid().optional(),\n  reseller_id: z.string().uuid(),\n  end_user_id: z.string().uuid(),\n  assigned_reseller_id: z.string().uuid().nullable().optional(),\n  status: DealStatus.default('pending'),\n  total_value: z.number().positive('Total value must be positive'),\n  submission_date: z.string().optional(),\n  assignment_date: z.string().nullable().optional(),\n  created_at: z.string().optional(),\n  updated_at: z.string().optional(),\n})\n\nexport const DealConflictSchema = z.object({\n  id: z.string().uuid().optional(),\n  deal_id: z.string().uuid(),\n  competing_deal_id: z.string().uuid(),\n  conflict_type: ConflictType,\n  resolution_status: ResolutionStatus.default('pending'),\n  assigned_to_staff: z.string().uuid().nullable().optional(),\n  created_at: z.string().optional(),\n  updated_at: z.string().optional(),\n})\n\nexport const StaffUserSchema = z.object({\n  id: z.string().uuid().optional(),\n  email: z.string().email('Valid email is required'),\n  name: z.string().min(1, 'Name is required'),\n  role: StaffRole.default('staff'),\n  created_at: z.string().optional(),\n  updated_at: z.string().optional(),\n})\n\n// Form schemas for creating/updating\nexport const CreateDealSchema = z.object({\n  reseller_id: z.string().uuid('Please select a reseller'),\n  end_user: z.object({\n    id: z.string().uuid().optional(),\n    company_name: z.string().min(1, 'Company name is required'),\n    contact_name: z.string().min(1, 'Contact name is required'),\n    contact_email: z.string().email('Valid email is required'),\n    territory: z.string().min(1, 'Territory is required'),\n  }),\n  products: z.array(z.object({\n    product_id: z.string().uuid('Please select a product'),\n    quantity: z.number().int().positive('Quantity must be positive'),\n    price: z.number().positive('Price must be positive'),\n  })).min(1, 'At least one product is required'),\n})\n\nexport const AssignDealSchema = z.object({\n  deal_id: z.string().uuid(),\n  assigned_reseller_id: z.string().uuid(),\n  reason: z.string().optional(),\n})\n\n// Type exports\nexport type Reseller = z.infer<typeof ResellerSchema>\nexport type EndUser = z.infer<typeof EndUserSchema>\nexport type Product = z.infer<typeof ProductSchema>\nexport type Deal = z.infer<typeof DealSchema>\nexport type DealProduct = z.infer<typeof DealProductSchema>\nexport type DealConflict = z.infer<typeof DealConflictSchema>\nexport type StaffUser = z.infer<typeof StaffUserSchema>\nexport type CreateDeal = z.infer<typeof CreateDealSchema>\nexport type AssignDeal = z.infer<typeof AssignDealSchema>\n\n// Extended types with relationships\nexport type DealWithRelations = Deal & {\n  reseller: Reseller\n  end_user: EndUser\n  assigned_reseller?: Reseller | null\n  products: (DealProduct & { product: Product })[]\n  conflicts: (DealConflict & { competing_deal: Deal })[]\n}\n\nexport type ConflictWithRelations = DealConflict & {\n  deal: DealWithRelations\n  competing_deal: DealWithRelations\n  assigned_staff?: StaffUser | null\n}\n\n// API Response types\nexport type ApiResponse<T> = {\n  data: T | null\n  error: string | null\n  success: boolean\n}\n\nexport type PaginatedResponse<T> = ApiResponse<{\n  items: T[]\n  total: number\n  page: number\n  limit: number\n  totalPages: number\n}>\n\n// Filter and search types\nexport type DealFilters = {\n  status?: string[]\n  reseller_id?: string\n  territory?: string\n  date_from?: string\n  date_to?: string\n  has_conflicts?: boolean\n}\n\nexport type ConflictFilters = {\n  resolution_status?: string[]\n  conflict_type?: string[]\n  assigned_to_staff?: string\n}\n\n// Dashboard metrics\nexport type DashboardMetrics = {\n  total_deals: number\n  pending_deals: number\n  disputed_deals: number\n  assigned_deals: number\n  total_conflicts: number\n  pending_conflicts: number\n  total_value: number\n  avg_resolution_time: number\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;;AAGO,MAAM,eAAe,+KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAAC;IAAQ;IAAU;CAAS;AACxD,MAAM,aAAa,+KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAAC;IAAU;CAAW;AAChD,MAAM,aAAa,+KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAAC;IAAW;IAAY;IAAY;IAAY;CAAW;AACrF,MAAM,eAAe,+KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAAC;IAAsB;IAAqB;CAAkB;AAC1F,MAAM,mBAAmB,+KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAAC;IAAW;IAAY;CAAY;AACpE,MAAM,YAAY,+KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAAC;IAAS;IAAW;CAAQ;AAGtD,MAAM,iBAAiB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,IAAI,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IAC9B,MAAM,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,WAAW,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,MAAM;IACN,QAAQ,WAAW,OAAO,CAAC;IAC3B,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACjC;AAEO,MAAM,gBAAgB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,IAAI,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IAC9B,cAAc,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAChC,cAAc,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAChC,eAAe,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IAChC,WAAW,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACjC;AAEO,MAAM,gBAAgB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,IAAI,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IAC9B,MAAM,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAChC,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACjC;AAEO,MAAM,oBAAoB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,IAAI,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IAC9B,SAAS,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IACnC,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI;IAC3B,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC;IACpC,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC3B,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACjC;AAEO,MAAM,aAAa,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,IAAI,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IAC9B,aAAa,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI;IAC5B,aAAa,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI;IAC5B,sBAAsB,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ,GAAG,QAAQ;IAC3D,QAAQ,WAAW,OAAO,CAAC;IAC3B,aAAa,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC,iBAAiB,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACpC,iBAAiB,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC/C,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACjC;AAEO,MAAM,qBAAqB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,IAAI,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IAC9B,SAAS,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI;IACxB,mBAAmB,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI;IAClC,eAAe;IACf,mBAAmB,iBAAiB,OAAO,CAAC;IAC5C,mBAAmB,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ,GAAG,QAAQ;IACxD,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACjC;AAEO,MAAM,kBAAkB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,IAAI,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IAC9B,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,MAAM,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,MAAM,UAAU,OAAO,CAAC;IACxB,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACjC;AAGO,MAAM,mBAAmB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,aAAa,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC;IAC7B,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACjB,IAAI,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;QAC9B,cAAc,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAChC,cAAc,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAChC,eAAe,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;QAChC,WAAW,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B;IACA,UAAU,+KAAA,CAAA,IAAC,CAAC,KAAK,CAAC,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACzB,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC;QAC5B,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC;QACpC,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC7B,IAAI,GAAG,CAAC,GAAG;AACb;AAEO,MAAM,mBAAmB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,SAAS,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI;IACxB,sBAAsB,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI;IACrC,QAAQ,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC7B", "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Date formatting utilities\nexport function formatDate(date: string | Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(new Date(date))\n}\n\nexport function formatDateTime(date: string | Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount)\n}\n\n// String utilities\nexport function capitalizeFirst(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1)\n}\n\nexport function slugify(str: string): string {\n  return str\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '')\n}\n\n// Fuzzy matching for duplicate detection\nexport function calculateSimilarity(str1: string, str2: string): number {\n  const longer = str1.length > str2.length ? str1 : str2\n  const shorter = str1.length > str2.length ? str2 : str1\n  \n  if (longer.length === 0) return 1.0\n  \n  const distance = levenshteinDistance(longer, shorter)\n  return (longer.length - distance) / longer.length\n}\n\nfunction levenshteinDistance(str1: string, str2: string): number {\n  const matrix = []\n  \n  for (let i = 0; i <= str2.length; i++) {\n    matrix[i] = [i]\n  }\n  \n  for (let j = 0; j <= str1.length; j++) {\n    matrix[0][j] = j\n  }\n  \n  for (let i = 1; i <= str2.length; i++) {\n    for (let j = 1; j <= str1.length; j++) {\n      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {\n        matrix[i][j] = matrix[i - 1][j - 1]\n      } else {\n        matrix[i][j] = Math.min(\n          matrix[i - 1][j - 1] + 1,\n          matrix[i][j - 1] + 1,\n          matrix[i - 1][j] + 1\n        )\n      }\n    }\n  }\n  \n  return matrix[str2.length][str1.length]\n}\n\n// Company name normalization for better matching\nexport function normalizeCompanyName(name: string): string {\n  return name\n    .toLowerCase()\n    .replace(/\\b(inc|corp|corporation|ltd|limited|llc|co)\\b\\.?/g, '')\n    .replace(/[^\\w\\s]/g, '')\n    .replace(/\\s+/g, ' ')\n    .trim()\n}\n\n// Territory overlap detection\nexport function checkTerritoryOverlap(territory1: string, territory2: string): boolean {\n  const t1 = territory1.toLowerCase().trim()\n  const t2 = territory2.toLowerCase().trim()\n  \n  // Exact match\n  if (t1 === t2) return true\n  \n  // Check for common territory patterns\n  const patterns = [\n    /^(north|south|east|west)\\s*(america|us|usa|united states)$/,\n    /^(northeast|northwest|southeast|southwest)\\s*(region|territory)?$/,\n    /^(global|worldwide|international)$/,\n    /^(enterprise|commercial|federal)$/,\n  ]\n  \n  for (const pattern of patterns) {\n    if (pattern.test(t1) && pattern.test(t2)) return true\n  }\n  \n  return false\n}\n\n// Deal value comparison with tolerance\nexport function isDealValueSimilar(value1: number, value2: number, tolerance = 0.1): boolean {\n  const diff = Math.abs(value1 - value2)\n  const avg = (value1 + value2) / 2\n  return diff / avg <= tolerance\n}\n\n// Time window checking\nexport function isWithinTimeWindow(date1: string | Date, date2: string | Date, windowDays = 90): boolean {\n  const d1 = new Date(date1)\n  const d2 = new Date(date2)\n  const diffTime = Math.abs(d2.getTime() - d1.getTime())\n  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))\n  return diffDays <= windowDays\n}\n\n// Validation utilities\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function isValidUUID(uuid: string): boolean {\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i\n  return uuidRegex.test(uuid)\n}\n\n// Array utilities\nexport function groupBy<T, K extends keyof any>(array: T[], key: (item: T) => K): Record<K, T[]> {\n  return array.reduce((groups, item) => {\n    const group = key(item)\n    groups[group] = groups[group] || []\n    groups[group].push(item)\n    return groups\n  }, {} as Record<K, T[]>)\n}\n\nexport function sortBy<T>(array: T[], key: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] {\n  return [...array].sort((a, b) => {\n    const aVal = a[key]\n    const bVal = b[key]\n    \n    if (aVal < bVal) return direction === 'asc' ? -1 : 1\n    if (aVal > bVal) return direction === 'asc' ? 1 : -1\n    return 0\n  })\n}\n\n// Debounce utility for search\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null\n  \n  return (...args: Parameters<T>) => {\n    if (timeout) clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\n// Error handling\nexport function getErrorMessage(error: unknown): string {\n  if (error instanceof Error) return error.message\n  if (typeof error === 'string') return error\n  return 'An unknown error occurred'\n}\n\n// Status badge colors\nexport function getStatusColor(status: string): string {\n  const colors: Record<string, string> = {\n    pending: 'bg-yellow-100 text-yellow-800',\n    assigned: 'bg-green-100 text-green-800',\n    disputed: 'bg-red-100 text-red-800',\n    approved: 'bg-blue-100 text-blue-800',\n    rejected: 'bg-gray-100 text-gray-800',\n    active: 'bg-green-100 text-green-800',\n    inactive: 'bg-gray-100 text-gray-800',\n    resolved: 'bg-green-100 text-green-800',\n    dismissed: 'bg-gray-100 text-gray-800',\n  }\n  \n  return colors[status] || 'bg-gray-100 text-gray-800'\n}\n\n// Priority calculation for conflicts\nexport function calculateConflictPriority(\n  dealValue: number,\n  conflictType: string,\n  daysSinceSubmission: number\n): 'high' | 'medium' | 'low' {\n  let score = 0\n  \n  // Deal value weight\n  if (dealValue > 100000) score += 3\n  else if (dealValue > 50000) score += 2\n  else score += 1\n  \n  // Conflict type weight\n  if (conflictType === 'duplicate_end_user') score += 3\n  else if (conflictType === 'territory_overlap') score += 2\n  else score += 1\n  \n  // Time weight\n  if (daysSinceSubmission > 7) score += 2\n  else if (daysSinceSubmission > 3) score += 1\n  \n  if (score >= 7) return 'high'\n  if (score >= 4) return 'medium'\n  return 'low'\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,uIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,eAAe,IAAmB;IAChD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,gBAAgB,GAAW;IACzC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD;AAEO,SAAS,QAAQ,GAAW;IACjC,OAAO,IACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAGO,SAAS,oBAAoB,IAAY,EAAE,IAAY;IAC5D,MAAM,SAAS,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,OAAO;IAClD,MAAM,UAAU,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,OAAO;IAEnD,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;IAEhC,MAAM,WAAW,oBAAoB,QAAQ;IAC7C,OAAO,CAAC,OAAO,MAAM,GAAG,QAAQ,IAAI,OAAO,MAAM;AACnD;AAEA,SAAS,oBAAoB,IAAY,EAAE,IAAY;IACrD,MAAM,SAAS,EAAE;IAEjB,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,MAAM,EAAE,IAAK;QACrC,MAAM,CAAC,EAAE,GAAG;YAAC;SAAE;IACjB;IAEA,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,MAAM,EAAE,IAAK;QACrC,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG;IACjB;IAEA,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,MAAM,EAAE,IAAK;QACrC,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,MAAM,EAAE,IAAK;YACrC,IAAI,KAAK,MAAM,CAAC,IAAI,OAAO,KAAK,MAAM,CAAC,IAAI,IAAI;gBAC7C,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE;YACrC,OAAO;gBACL,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,GAAG,CACrB,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,GAAG,GACvB,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,GACnB,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG;YAEvB;QACF;IACF;IAEA,OAAO,MAAM,CAAC,KAAK,MAAM,CAAC,CAAC,KAAK,MAAM,CAAC;AACzC;AAGO,SAAS,qBAAqB,IAAY;IAC/C,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,qDAAqD,IAC7D,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,QAAQ,KAChB,IAAI;AACT;AAGO,SAAS,sBAAsB,UAAkB,EAAE,UAAkB;IAC1E,MAAM,KAAK,WAAW,WAAW,GAAG,IAAI;IACxC,MAAM,KAAK,WAAW,WAAW,GAAG,IAAI;IAExC,cAAc;IACd,IAAI,OAAO,IAAI,OAAO;IAEtB,sCAAsC;IACtC,MAAM,WAAW;QACf;QACA;QACA;QACA;KACD;IAED,KAAK,MAAM,WAAW,SAAU;QAC9B,IAAI,QAAQ,IAAI,CAAC,OAAO,QAAQ,IAAI,CAAC,KAAK,OAAO;IACnD;IAEA,OAAO;AACT;AAGO,SAAS,mBAAmB,MAAc,EAAE,MAAc,EAAE,YAAY,GAAG;IAChF,MAAM,OAAO,KAAK,GAAG,CAAC,SAAS;IAC/B,MAAM,MAAM,CAAC,SAAS,MAAM,IAAI;IAChC,OAAO,OAAO,OAAO;AACvB;AAGO,SAAS,mBAAmB,KAAoB,EAAE,KAAoB,EAAE,aAAa,EAAE;IAC5F,MAAM,KAAK,IAAI,KAAK;IACpB,MAAM,KAAK,IAAI,KAAK;IACpB,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,OAAO,KAAK,GAAG,OAAO;IACnD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAC1D,OAAO,YAAY;AACrB;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,YAAY,IAAY;IACtC,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB;AAGO,SAAS,QAAgC,KAAU,EAAE,GAAmB;IAC7E,OAAO,MAAM,MAAM,CAAC,CAAC,QAAQ;QAC3B,MAAM,QAAQ,IAAI;QAClB,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,EAAE;QACnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;QACnB,OAAO;IACT,GAAG,CAAC;AACN;AAEO,SAAS,OAAU,KAAU,EAAE,GAAY,EAAE,YAA4B,KAAK;IACnF,OAAO;WAAI;KAAM,CAAC,IAAI,CAAC,CAAC,GAAG;QACzB,MAAM,OAAO,CAAC,CAAC,IAAI;QACnB,MAAM,OAAO,CAAC,CAAC,IAAI;QAEnB,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,CAAC,IAAI;QACnD,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,IAAI,CAAC;QACnD,OAAO;IACT;AACF;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO,CAAC,GAAG;QACT,IAAI,SAAS,aAAa;QAC1B,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,gBAAgB,KAAc;IAC5C,IAAI,iBAAiB,OAAO,OAAO,MAAM,OAAO;IAChD,IAAI,OAAO,UAAU,UAAU,OAAO;IACtC,OAAO;AACT;AAGO,SAAS,eAAe,MAAc;IAC3C,MAAM,SAAiC;QACrC,SAAS;QACT,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;QACV,WAAW;IACb;IAEA,OAAO,MAAM,CAAC,OAAO,IAAI;AAC3B;AAGO,SAAS,0BACd,SAAiB,EACjB,YAAoB,EACpB,mBAA2B;IAE3B,IAAI,QAAQ;IAEZ,oBAAoB;IACpB,IAAI,YAAY,QAAQ,SAAS;SAC5B,IAAI,YAAY,OAAO,SAAS;SAChC,SAAS;IAEd,uBAAuB;IACvB,IAAI,iBAAiB,sBAAsB,SAAS;SAC/C,IAAI,iBAAiB,qBAAqB,SAAS;SACnD,SAAS;IAEd,cAAc;IACd,IAAI,sBAAsB,GAAG,SAAS;SACjC,IAAI,sBAAsB,GAAG,SAAS;IAE3C,IAAI,SAAS,GAAG,OAAO;IACvB,IAAI,SAAS,GAAG,OAAO;IACvB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/lib/conflict-detection.ts"], "sourcesContent": ["import { createAdminClient } from './supabase'\nimport { \n  normalizeCompanyName, \n  calculateSimilarity, \n  checkTerritoryOverlap, \n  isDealValueSimilar, \n  isWithinTimeWindow \n} from './utils'\nimport type { Deal, EndUser, DealConflict, ConflictType } from './types'\n\nexport interface ConflictDetectionResult {\n  hasConflicts: boolean\n  conflicts: DetectedConflict[]\n  suggestions: string[]\n}\n\nexport interface DetectedConflict {\n  type: ConflictType\n  severity: 'high' | 'medium' | 'low'\n  conflictingDeal: Deal & { end_user: EndUser }\n  reason: string\n  similarity?: number\n}\n\nexport class ConflictDetectionEngine {\n  private supabase = createAdminClient()\n\n  async detectConflicts(\n    newDeal: {\n      end_user: EndUser\n      reseller_id: string\n      total_value: number\n      submission_date?: string\n    }\n  ): Promise<ConflictDetectionResult> {\n    const conflicts: DetectedConflict[] = []\n    const suggestions: string[] = []\n\n    try {\n      // Get existing deals for comparison\n      const { data: existingDeals, error } = await this.supabase\n        .from('deals')\n        .select(`\n          *,\n          end_user:end_users(*),\n          reseller:resellers(*)\n        `)\n        .neq('status', 'rejected')\n        .order('created_at', { ascending: false })\n        .limit(1000) // Reasonable limit for performance\n\n      if (error) {\n        console.error('Error fetching existing deals:', error)\n        return { hasConflicts: false, conflicts: [], suggestions: [] }\n      }\n\n      if (!existingDeals) {\n        return { hasConflicts: false, conflicts: [], suggestions: [] }\n      }\n\n      // Check each existing deal for conflicts\n      for (const existingDeal of existingDeals) {\n        const detectedConflicts = await this.checkDealConflicts(newDeal, existingDeal)\n        conflicts.push(...detectedConflicts)\n      }\n\n      // Generate suggestions based on conflicts\n      if (conflicts.length > 0) {\n        suggestions.push(...this.generateSuggestions(conflicts))\n      }\n\n      return {\n        hasConflicts: conflicts.length > 0,\n        conflicts: this.prioritizeConflicts(conflicts),\n        suggestions\n      }\n    } catch (error) {\n      console.error('Conflict detection error:', error)\n      return { hasConflicts: false, conflicts: [], suggestions: [] }\n    }\n  }\n\n  private async checkDealConflicts(\n    newDeal: {\n      end_user: EndUser\n      reseller_id: string\n      total_value: number\n      submission_date?: string\n    },\n    existingDeal: any\n  ): Promise<DetectedConflict[]> {\n    const conflicts: DetectedConflict[] = []\n\n    // 1. Duplicate End User Detection\n    const endUserConflict = this.checkEndUserConflict(newDeal, existingDeal)\n    if (endUserConflict) {\n      conflicts.push(endUserConflict)\n    }\n\n    // 2. Territory Overlap Detection\n    const territoryConflict = this.checkTerritoryConflict(newDeal, existingDeal)\n    if (territoryConflict) {\n      conflicts.push(territoryConflict)\n    }\n\n    // 3. Timing Conflict Detection\n    const timingConflict = this.checkTimingConflict(newDeal, existingDeal)\n    if (timingConflict) {\n      conflicts.push(timingConflict)\n    }\n\n    return conflicts\n  }\n\n  private checkEndUserConflict(newDeal: any, existingDeal: any): DetectedConflict | null {\n    const newCompany = normalizeCompanyName(newDeal.end_user.company_name)\n    const existingCompany = normalizeCompanyName(existingDeal.end_user.company_name)\n    \n    // Calculate similarity\n    const similarity = calculateSimilarity(newCompany, existingCompany)\n    \n    // High similarity threshold for company names\n    if (similarity >= 0.85) {\n      // Check email similarity for additional confirmation\n      const emailSimilarity = calculateSimilarity(\n        newDeal.end_user.contact_email.toLowerCase(),\n        existingDeal.end_user.contact_email.toLowerCase()\n      )\n\n      let severity: 'high' | 'medium' | 'low' = 'medium'\n      let reason = `Similar company name: \"${newDeal.end_user.company_name}\" vs \"${existingDeal.end_user.company_name}\"`\n\n      // Exact match or very high similarity\n      if (similarity >= 0.95 || emailSimilarity >= 0.8) {\n        severity = 'high'\n        reason = `Potential duplicate: ${reason}`\n      }\n      // Same reseller submitting for same end user\n      else if (newDeal.reseller_id === existingDeal.reseller_id) {\n        severity = 'high'\n        reason = `Same reseller submitting for same end user: ${reason}`\n      }\n\n      return {\n        type: 'duplicate_end_user',\n        severity,\n        conflictingDeal: existingDeal,\n        reason,\n        similarity\n      }\n    }\n\n    return null\n  }\n\n  private checkTerritoryConflict(newDeal: any, existingDeal: any): DetectedConflict | null {\n    // Skip if same reseller\n    if (newDeal.reseller_id === existingDeal.reseller_id) {\n      return null\n    }\n\n    const hasOverlap = checkTerritoryOverlap(\n      newDeal.end_user.territory,\n      existingDeal.end_user.territory\n    )\n\n    if (hasOverlap) {\n      // Check if it's the same end user (higher severity)\n      const companysimilarity = calculateSimilarity(\n        normalizeCompanyName(newDeal.end_user.company_name),\n        normalizeCompanyName(existingDeal.end_user.company_name)\n      )\n\n      const severity: 'high' | 'medium' | 'low' = companysimilarity >= 0.7 ? 'high' : 'medium'\n\n      return {\n        type: 'territory_overlap',\n        severity,\n        conflictingDeal: existingDeal,\n        reason: `Territory overlap: \"${newDeal.end_user.territory}\" overlaps with \"${existingDeal.end_user.territory}\"`\n      }\n    }\n\n    return null\n  }\n\n  private checkTimingConflict(newDeal: any, existingDeal: any): DetectedConflict | null {\n    const newDate = newDeal.submission_date || new Date().toISOString()\n    const existingDate = existingDeal.submission_date || existingDeal.created_at\n\n    // Check if within time window (default 90 days)\n    if (!isWithinTimeWindow(newDate, existingDate, 90)) {\n      return null\n    }\n\n    // Check if similar deal value and same end user\n    const companysimilarity = calculateSimilarity(\n      normalizeCompanyName(newDeal.end_user.company_name),\n      normalizeCompanyName(existingDeal.end_user.company_name)\n    )\n\n    const valueSimilarity = isDealValueSimilar(newDeal.total_value, existingDeal.total_value, 0.2)\n\n    if (companysimilarity >= 0.7 && valueSimilarity) {\n      const daysDiff = Math.abs(\n        (new Date(newDate).getTime() - new Date(existingDate).getTime()) / (1000 * 60 * 60 * 24)\n      )\n\n      let severity: 'high' | 'medium' | 'low' = 'low'\n      if (daysDiff <= 7) severity = 'high'\n      else if (daysDiff <= 30) severity = 'medium'\n\n      return {\n        type: 'timing_conflict',\n        severity,\n        conflictingDeal: existingDeal,\n        reason: `Similar deal submitted ${Math.round(daysDiff)} days ago for similar end user and value`\n      }\n    }\n\n    return null\n  }\n\n  private prioritizeConflicts(conflicts: DetectedConflict[]): DetectedConflict[] {\n    return conflicts.sort((a, b) => {\n      // Sort by severity first\n      const severityOrder = { high: 3, medium: 2, low: 1 }\n      const severityDiff = severityOrder[b.severity] - severityOrder[a.severity]\n      if (severityDiff !== 0) return severityDiff\n\n      // Then by conflict type priority\n      const typeOrder = { duplicate_end_user: 3, territory_overlap: 2, timing_conflict: 1 }\n      const typeDiff = typeOrder[b.type] - typeOrder[a.type]\n      if (typeDiff !== 0) return typeDiff\n\n      // Finally by similarity if available\n      const aSim = a.similarity || 0\n      const bSim = b.similarity || 0\n      return bSim - aSim\n    })\n  }\n\n  private generateSuggestions(conflicts: DetectedConflict[]): string[] {\n    const suggestions: string[] = []\n\n    const highSeverityConflicts = conflicts.filter(c => c.severity === 'high')\n    const duplicateConflicts = conflicts.filter(c => c.type === 'duplicate_end_user')\n    const territoryConflicts = conflicts.filter(c => c.type === 'territory_overlap')\n\n    if (highSeverityConflicts.length > 0) {\n      suggestions.push('⚠️ High-priority conflicts detected - requires immediate review')\n    }\n\n    if (duplicateConflicts.length > 0) {\n      suggestions.push('🔍 Verify if this is a duplicate submission for the same end user')\n      suggestions.push('📞 Contact the reseller to confirm deal details')\n    }\n\n    if (territoryConflicts.length > 0) {\n      suggestions.push('🗺️ Review territory assignments and partner agreements')\n      suggestions.push('⚖️ Consider first-come-first-served or partner tier priority')\n    }\n\n    if (conflicts.length > 2) {\n      suggestions.push('📋 Multiple conflicts detected - consider escalating to management')\n    }\n\n    return suggestions\n  }\n\n  async createConflictRecords(dealId: string, conflicts: DetectedConflict[]): Promise<void> {\n    if (conflicts.length === 0) return\n\n    const conflictRecords = conflicts.map(conflict => ({\n      deal_id: dealId,\n      competing_deal_id: conflict.conflictingDeal.id,\n      conflict_type: conflict.type,\n      resolution_status: 'pending' as const,\n    }))\n\n    const { error } = await this.supabase\n      .from('deal_conflicts')\n      .insert(conflictRecords)\n\n    if (error) {\n      console.error('Error creating conflict records:', error)\n      throw error\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAuBO,MAAM;IACH,WAAW,CAAA,GAAA,wHAAA,CAAA,oBAAiB,AAAD,IAAG;IAEtC,MAAM,gBACJ,OAKC,EACiC;QAClC,MAAM,YAAgC,EAAE;QACxC,MAAM,cAAwB,EAAE;QAEhC,IAAI;YACF,oCAAoC;YACpC,MAAM,EAAE,MAAM,aAAa,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACvD,IAAI,CAAC,SACL,MAAM,CAAC,CAAC;;;;QAIT,CAAC,EACA,GAAG,CAAC,UAAU,YACd,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM,GACvC,KAAK,CAAC,MAAM,mCAAmC;;YAElD,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,OAAO;oBAAE,cAAc;oBAAO,WAAW,EAAE;oBAAE,aAAa,EAAE;gBAAC;YAC/D;YAEA,IAAI,CAAC,eAAe;gBAClB,OAAO;oBAAE,cAAc;oBAAO,WAAW,EAAE;oBAAE,aAAa,EAAE;gBAAC;YAC/D;YAEA,yCAAyC;YACzC,KAAK,MAAM,gBAAgB,cAAe;gBACxC,MAAM,oBAAoB,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS;gBACjE,UAAU,IAAI,IAAI;YACpB;YAEA,0CAA0C;YAC1C,IAAI,UAAU,MAAM,GAAG,GAAG;gBACxB,YAAY,IAAI,IAAI,IAAI,CAAC,mBAAmB,CAAC;YAC/C;YAEA,OAAO;gBACL,cAAc,UAAU,MAAM,GAAG;gBACjC,WAAW,IAAI,CAAC,mBAAmB,CAAC;gBACpC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;gBAAE,cAAc;gBAAO,WAAW,EAAE;gBAAE,aAAa,EAAE;YAAC;QAC/D;IACF;IAEA,MAAc,mBACZ,OAKC,EACD,YAAiB,EACY;QAC7B,MAAM,YAAgC,EAAE;QAExC,kCAAkC;QAClC,MAAM,kBAAkB,IAAI,CAAC,oBAAoB,CAAC,SAAS;QAC3D,IAAI,iBAAiB;YACnB,UAAU,IAAI,CAAC;QACjB;QAEA,iCAAiC;QACjC,MAAM,oBAAoB,IAAI,CAAC,sBAAsB,CAAC,SAAS;QAC/D,IAAI,mBAAmB;YACrB,UAAU,IAAI,CAAC;QACjB;QAEA,+BAA+B;QAC/B,MAAM,iBAAiB,IAAI,CAAC,mBAAmB,CAAC,SAAS;QACzD,IAAI,gBAAgB;YAClB,UAAU,IAAI,CAAC;QACjB;QAEA,OAAO;IACT;IAEQ,qBAAqB,OAAY,EAAE,YAAiB,EAA2B;QACrF,MAAM,aAAa,CAAA,GAAA,qHAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ,QAAQ,CAAC,YAAY;QACrE,MAAM,kBAAkB,CAAA,GAAA,qHAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa,QAAQ,CAAC,YAAY;QAE/E,uBAAuB;QACvB,MAAM,aAAa,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD,EAAE,YAAY;QAEnD,8CAA8C;QAC9C,IAAI,cAAc,MAAM;YACtB,qDAAqD;YACrD,MAAM,kBAAkB,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD,EACxC,QAAQ,QAAQ,CAAC,aAAa,CAAC,WAAW,IAC1C,aAAa,QAAQ,CAAC,aAAa,CAAC,WAAW;YAGjD,IAAI,WAAsC;YAC1C,IAAI,SAAS,CAAC,uBAAuB,EAAE,QAAQ,QAAQ,CAAC,YAAY,CAAC,MAAM,EAAE,aAAa,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;YAElH,sCAAsC;YACtC,IAAI,cAAc,QAAQ,mBAAmB,KAAK;gBAChD,WAAW;gBACX,SAAS,CAAC,qBAAqB,EAAE,QAAQ;YAC3C,OAEK,IAAI,QAAQ,WAAW,KAAK,aAAa,WAAW,EAAE;gBACzD,WAAW;gBACX,SAAS,CAAC,4CAA4C,EAAE,QAAQ;YAClE;YAEA,OAAO;gBACL,MAAM;gBACN;gBACA,iBAAiB;gBACjB;gBACA;YACF;QACF;QAEA,OAAO;IACT;IAEQ,uBAAuB,OAAY,EAAE,YAAiB,EAA2B;QACvF,wBAAwB;QACxB,IAAI,QAAQ,WAAW,KAAK,aAAa,WAAW,EAAE;YACpD,OAAO;QACT;QAEA,MAAM,aAAa,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,EACrC,QAAQ,QAAQ,CAAC,SAAS,EAC1B,aAAa,QAAQ,CAAC,SAAS;QAGjC,IAAI,YAAY;YACd,oDAAoD;YACpD,MAAM,oBAAoB,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD,EAC1C,CAAA,GAAA,qHAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ,QAAQ,CAAC,YAAY,GAClD,CAAA,GAAA,qHAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa,QAAQ,CAAC,YAAY;YAGzD,MAAM,WAAsC,qBAAqB,MAAM,SAAS;YAEhF,OAAO;gBACL,MAAM;gBACN;gBACA,iBAAiB;gBACjB,QAAQ,CAAC,oBAAoB,EAAE,QAAQ,QAAQ,CAAC,SAAS,CAAC,iBAAiB,EAAE,aAAa,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;YACjH;QACF;QAEA,OAAO;IACT;IAEQ,oBAAoB,OAAY,EAAE,YAAiB,EAA2B;QACpF,MAAM,UAAU,QAAQ,eAAe,IAAI,IAAI,OAAO,WAAW;QACjE,MAAM,eAAe,aAAa,eAAe,IAAI,aAAa,UAAU;QAE5E,gDAAgD;QAChD,IAAI,CAAC,CAAA,GAAA,qHAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS,cAAc,KAAK;YAClD,OAAO;QACT;QAEA,gDAAgD;QAChD,MAAM,oBAAoB,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD,EAC1C,CAAA,GAAA,qHAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ,QAAQ,CAAC,YAAY,GAClD,CAAA,GAAA,qHAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa,QAAQ,CAAC,YAAY;QAGzD,MAAM,kBAAkB,CAAA,GAAA,qHAAA,CAAA,qBAAkB,AAAD,EAAE,QAAQ,WAAW,EAAE,aAAa,WAAW,EAAE;QAE1F,IAAI,qBAAqB,OAAO,iBAAiB;YAC/C,MAAM,WAAW,KAAK,GAAG,CACvB,CAAC,IAAI,KAAK,SAAS,OAAO,KAAK,IAAI,KAAK,cAAc,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;YAGzF,IAAI,WAAsC;YAC1C,IAAI,YAAY,GAAG,WAAW;iBACzB,IAAI,YAAY,IAAI,WAAW;YAEpC,OAAO;gBACL,MAAM;gBACN;gBACA,iBAAiB;gBACjB,QAAQ,CAAC,uBAAuB,EAAE,KAAK,KAAK,CAAC,UAAU,wCAAwC,CAAC;YAClG;QACF;QAEA,OAAO;IACT;IAEQ,oBAAoB,SAA6B,EAAsB;QAC7E,OAAO,UAAU,IAAI,CAAC,CAAC,GAAG;YACxB,yBAAyB;YACzB,MAAM,gBAAgB;gBAAE,MAAM;gBAAG,QAAQ;gBAAG,KAAK;YAAE;YACnD,MAAM,eAAe,aAAa,CAAC,EAAE,QAAQ,CAAC,GAAG,aAAa,CAAC,EAAE,QAAQ,CAAC;YAC1E,IAAI,iBAAiB,GAAG,OAAO;YAE/B,iCAAiC;YACjC,MAAM,YAAY;gBAAE,oBAAoB;gBAAG,mBAAmB;gBAAG,iBAAiB;YAAE;YACpF,MAAM,WAAW,SAAS,CAAC,EAAE,IAAI,CAAC,GAAG,SAAS,CAAC,EAAE,IAAI,CAAC;YACtD,IAAI,aAAa,GAAG,OAAO;YAE3B,qCAAqC;YACrC,MAAM,OAAO,EAAE,UAAU,IAAI;YAC7B,MAAM,OAAO,EAAE,UAAU,IAAI;YAC7B,OAAO,OAAO;QAChB;IACF;IAEQ,oBAAoB,SAA6B,EAAY;QACnE,MAAM,cAAwB,EAAE;QAEhC,MAAM,wBAAwB,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;QACnE,MAAM,qBAAqB,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QAC5D,MAAM,qBAAqB,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QAE5D,IAAI,sBAAsB,MAAM,GAAG,GAAG;YACpC,YAAY,IAAI,CAAC;QACnB;QAEA,IAAI,mBAAmB,MAAM,GAAG,GAAG;YACjC,YAAY,IAAI,CAAC;YACjB,YAAY,IAAI,CAAC;QACnB;QAEA,IAAI,mBAAmB,MAAM,GAAG,GAAG;YACjC,YAAY,IAAI,CAAC;YACjB,YAAY,IAAI,CAAC;QACnB;QAEA,IAAI,UAAU,MAAM,GAAG,GAAG;YACxB,YAAY,IAAI,CAAC;QACnB;QAEA,OAAO;IACT;IAEA,MAAM,sBAAsB,MAAc,EAAE,SAA6B,EAAiB;QACxF,IAAI,UAAU,MAAM,KAAK,GAAG;QAE5B,MAAM,kBAAkB,UAAU,GAAG,CAAC,CAAA,WAAY,CAAC;gBACjD,SAAS;gBACT,mBAAmB,SAAS,eAAe,CAAC,EAAE;gBAC9C,eAAe,SAAS,IAAI;gBAC5B,mBAAmB;YACrB,CAAC;QAED,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAClC,IAAI,CAAC,kBACL,MAAM,CAAC;QAEV,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 741, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/app/api/deals/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createServerComponentClient } from '@/lib/supabase'\nimport { CreateDealSchema, type CreateDeal } from '@/lib/types'\nimport { ConflictDetectionEngine } from '@/lib/conflict-detection'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const supabase = createServerComponentClient()\n    const { searchParams } = new URL(request.url)\n    \n    // Parse query parameters\n    const page = parseInt(searchParams.get('page') || '1')\n    const limit = parseInt(searchParams.get('limit') || '10')\n    const status = searchParams.get('status')\n    const reseller_id = searchParams.get('reseller_id')\n    const territory = searchParams.get('territory')\n    const has_conflicts = searchParams.get('has_conflicts')\n    \n    const offset = (page - 1) * limit\n    \n    // Build query\n    let query = supabase\n      .from('deals')\n      .select(`\n        *,\n        reseller:resellers(*),\n        end_user:end_users(*),\n        assigned_reseller:resellers!deals_assigned_reseller_id_fkey(*),\n        products:deal_products(\n          *,\n          product:products(*)\n        ),\n        conflicts:deal_conflicts(\n          *,\n          competing_deal:deals!deal_conflicts_competing_deal_id_fkey(\n            *,\n            reseller:resellers(*),\n            end_user:end_users(*)\n          )\n        )\n      `)\n      .order('created_at', { ascending: false })\n      .range(offset, offset + limit - 1)\n    \n    // Apply filters\n    if (status) {\n      query = query.eq('status', status)\n    }\n    \n    if (reseller_id) {\n      query = query.eq('reseller_id', reseller_id)\n    }\n    \n    if (territory) {\n      query = query.eq('end_users.territory', territory)\n    }\n    \n    if (has_conflicts === 'true') {\n      query = query.not('conflicts', 'is', null)\n    }\n    \n    const { data: deals, error, count } = await query\n    \n    if (error) {\n      console.error('Error fetching deals:', error)\n      return NextResponse.json(\n        { error: 'Failed to fetch deals', details: error.message },\n        { status: 500 }\n      )\n    }\n    \n    // Get total count for pagination\n    const { count: totalCount } = await supabase\n      .from('deals')\n      .select('*', { count: 'exact', head: true })\n    \n    return NextResponse.json({\n      data: {\n        items: deals || [],\n        total: totalCount || 0,\n        page,\n        limit,\n        totalPages: Math.ceil((totalCount || 0) / limit)\n      },\n      success: true,\n      error: null\n    })\n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const supabase = createServerComponentClient()\n    const body = await request.json()\n    \n    // Validate request body\n    const validation = CreateDealSchema.safeParse(body)\n    if (!validation.success) {\n      return NextResponse.json(\n        { \n          error: 'Invalid request data', \n          details: validation.error.issues \n        },\n        { status: 400 }\n      )\n    }\n    \n    const dealData = validation.data\n    \n    // Start transaction\n    const { data: insertedEndUser, error: endUserError } = await supabase\n      .from('end_users')\n      .upsert({\n        id: dealData.end_user.id,\n        company_name: dealData.end_user.company_name,\n        contact_name: dealData.end_user.contact_name,\n        contact_email: dealData.end_user.contact_email,\n        territory: dealData.end_user.territory\n      })\n      .select()\n      .single()\n    \n    if (endUserError) {\n      console.error('Error creating/updating end user:', endUserError)\n      return NextResponse.json(\n        { error: 'Failed to create end user', details: endUserError.message },\n        { status: 500 }\n      )\n    }\n    \n    // Calculate total value\n    const totalValue = dealData.products.reduce(\n      (sum, product) => sum + (product.quantity * product.price), \n      0\n    )\n    \n    // Create deal\n    const { data: insertedDeal, error: dealError } = await supabase\n      .from('deals')\n      .insert({\n        reseller_id: dealData.reseller_id,\n        end_user_id: insertedEndUser.id,\n        total_value: totalValue,\n        status: 'pending'\n      })\n      .select()\n      .single()\n    \n    if (dealError) {\n      console.error('Error creating deal:', dealError)\n      return NextResponse.json(\n        { error: 'Failed to create deal', details: dealError.message },\n        { status: 500 }\n      )\n    }\n    \n    // Create deal products\n    const dealProducts = dealData.products.map(product => ({\n      deal_id: insertedDeal.id,\n      product_id: product.product_id,\n      quantity: product.quantity,\n      price: product.price\n    }))\n    \n    const { error: productsError } = await supabase\n      .from('deal_products')\n      .insert(dealProducts)\n    \n    if (productsError) {\n      console.error('Error creating deal products:', productsError)\n      // Rollback deal creation\n      await supabase.from('deals').delete().eq('id', insertedDeal.id)\n      return NextResponse.json(\n        { error: 'Failed to create deal products', details: productsError.message },\n        { status: 500 }\n      )\n    }\n    \n    // Detect conflicts\n    const conflictEngine = new ConflictDetectionEngine()\n    const conflictResult = await conflictEngine.detectConflicts({\n      end_user: insertedEndUser,\n      reseller_id: dealData.reseller_id,\n      total_value: totalValue,\n      submission_date: insertedDeal.created_at\n    })\n    \n    // Create conflict records if any\n    if (conflictResult.hasConflicts) {\n      await conflictEngine.createConflictRecords(insertedDeal.id, conflictResult.conflicts)\n      \n      // Update deal status to disputed if high-severity conflicts\n      const hasHighSeverityConflicts = conflictResult.conflicts.some(c => c.severity === 'high')\n      if (hasHighSeverityConflicts) {\n        await supabase\n          .from('deals')\n          .update({ status: 'disputed' })\n          .eq('id', insertedDeal.id)\n      }\n    }\n    \n    // Fetch the complete deal with relationships\n    const { data: completeDeal, error: fetchError } = await supabase\n      .from('deals')\n      .select(`\n        *,\n        reseller:resellers(*),\n        end_user:end_users(*),\n        products:deal_products(\n          *,\n          product:products(*)\n        ),\n        conflicts:deal_conflicts(\n          *,\n          competing_deal:deals!deal_conflicts_competing_deal_id_fkey(\n            *,\n            reseller:resellers(*),\n            end_user:end_users(*)\n          )\n        )\n      `)\n      .eq('id', insertedDeal.id)\n      .single()\n    \n    if (fetchError) {\n      console.error('Error fetching complete deal:', fetchError)\n      return NextResponse.json(\n        { error: 'Deal created but failed to fetch details', details: fetchError.message },\n        { status: 500 }\n      )\n    }\n    \n    return NextResponse.json({\n      data: {\n        deal: completeDeal,\n        conflicts: conflictResult\n      },\n      success: true,\n      error: null\n    }, { status: 201 })\n    \n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,wHAAA,CAAA,8BAA2B,AAAD;QAC3C,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAE5C,yBAAyB;QACzB,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,cAAc,aAAa,GAAG,CAAC;QACrC,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,gBAAgB,aAAa,GAAG,CAAC;QAEvC,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;QAE5B,cAAc;QACd,IAAI,QAAQ,SACT,IAAI,CAAC,SACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;MAiBT,CAAC,EACA,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC,QAAQ,SAAS,QAAQ;QAElC,gBAAgB;QAChB,IAAI,QAAQ;YACV,QAAQ,MAAM,EAAE,CAAC,UAAU;QAC7B;QAEA,IAAI,aAAa;YACf,QAAQ,MAAM,EAAE,CAAC,eAAe;QAClC;QAEA,IAAI,WAAW;YACb,QAAQ,MAAM,EAAE,CAAC,uBAAuB;QAC1C;QAEA,IAAI,kBAAkB,QAAQ;YAC5B,QAAQ,MAAM,GAAG,CAAC,aAAa,MAAM;QACvC;QAEA,MAAM,EAAE,MAAM,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;QAE5C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAyB,SAAS,MAAM,OAAO;YAAC,GACzD;gBAAE,QAAQ;YAAI;QAElB;QAEA,iCAAiC;QACjC,MAAM,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,SACL,MAAM,CAAC,KAAK;YAAE,OAAO;YAAS,MAAM;QAAK;QAE5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,MAAM;gBACJ,OAAO,SAAS,EAAE;gBAClB,OAAO,cAAc;gBACrB;gBACA;gBACA,YAAY,KAAK,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI;YAC5C;YACA,SAAS;YACT,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,wHAAA,CAAA,8BAA2B,AAAD;QAC3C,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,wBAAwB;QACxB,MAAM,aAAa,qHAAA,CAAA,mBAAgB,CAAC,SAAS,CAAC;QAC9C,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,SAAS,WAAW,KAAK,CAAC,MAAM;YAClC,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,WAAW,WAAW,IAAI;QAEhC,oBAAoB;QACpB,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAC1D,IAAI,CAAC,aACL,MAAM,CAAC;YACN,IAAI,SAAS,QAAQ,CAAC,EAAE;YACxB,cAAc,SAAS,QAAQ,CAAC,YAAY;YAC5C,cAAc,SAAS,QAAQ,CAAC,YAAY;YAC5C,eAAe,SAAS,QAAQ,CAAC,aAAa;YAC9C,WAAW,SAAS,QAAQ,CAAC,SAAS;QACxC,GACC,MAAM,GACN,MAAM;QAET,IAAI,cAAc;YAChB,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAA6B,SAAS,aAAa,OAAO;YAAC,GACpE;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,MAAM,aAAa,SAAS,QAAQ,CAAC,MAAM,CACzC,CAAC,KAAK,UAAY,MAAO,QAAQ,QAAQ,GAAG,QAAQ,KAAK,EACzD;QAGF,cAAc;QACd,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,SACL,MAAM,CAAC;YACN,aAAa,SAAS,WAAW;YACjC,aAAa,gBAAgB,EAAE;YAC/B,aAAa;YACb,QAAQ;QACV,GACC,MAAM,GACN,MAAM;QAET,IAAI,WAAW;YACb,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAyB,SAAS,UAAU,OAAO;YAAC,GAC7D;gBAAE,QAAQ;YAAI;QAElB;QAEA,uBAAuB;QACvB,MAAM,eAAe,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAA,UAAW,CAAC;gBACrD,SAAS,aAAa,EAAE;gBACxB,YAAY,QAAQ,UAAU;gBAC9B,UAAU,QAAQ,QAAQ;gBAC1B,OAAO,QAAQ,KAAK;YACtB,CAAC;QAED,MAAM,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,iBACL,MAAM,CAAC;QAEV,IAAI,eAAe;YACjB,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,yBAAyB;YACzB,MAAM,SAAS,IAAI,CAAC,SAAS,MAAM,GAAG,EAAE,CAAC,MAAM,aAAa,EAAE;YAC9D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAkC,SAAS,cAAc,OAAO;YAAC,GAC1E;gBAAE,QAAQ;YAAI;QAElB;QAEA,mBAAmB;QACnB,MAAM,iBAAiB,IAAI,qIAAA,CAAA,0BAAuB;QAClD,MAAM,iBAAiB,MAAM,eAAe,eAAe,CAAC;YAC1D,UAAU;YACV,aAAa,SAAS,WAAW;YACjC,aAAa;YACb,iBAAiB,aAAa,UAAU;QAC1C;QAEA,iCAAiC;QACjC,IAAI,eAAe,YAAY,EAAE;YAC/B,MAAM,eAAe,qBAAqB,CAAC,aAAa,EAAE,EAAE,eAAe,SAAS;YAEpF,4DAA4D;YAC5D,MAAM,2BAA2B,eAAe,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;YACnF,IAAI,0BAA0B;gBAC5B,MAAM,SACH,IAAI,CAAC,SACL,MAAM,CAAC;oBAAE,QAAQ;gBAAW,GAC5B,EAAE,CAAC,MAAM,aAAa,EAAE;YAC7B;QACF;QAEA,6CAA6C;QAC7C,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACrD,IAAI,CAAC,SACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;MAgBT,CAAC,EACA,EAAE,CAAC,MAAM,aAAa,EAAE,EACxB,MAAM;QAET,IAAI,YAAY;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAA4C,SAAS,WAAW,OAAO;YAAC,GACjF;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,MAAM;gBACJ,MAAM;gBACN,WAAW;YACb;YACA,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}