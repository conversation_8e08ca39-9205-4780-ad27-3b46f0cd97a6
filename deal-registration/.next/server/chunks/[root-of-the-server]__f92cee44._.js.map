{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient, createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const createClientComponentClient = () => {\n  return createBrowserClient(supabaseUrl, supabaseAnonKey)\n}\n\n// Server-side Supabase client\nexport const createServerComponentClient = () => {\n  const cookieStore = cookies()\n  \n  return createServerClient(supabaseUrl, supabaseAnonKey, {\n    cookies: {\n      get(name: string) {\n        return cookieStore.get(name)?.value\n      },\n    },\n  })\n}\n\n// Admin client with service role key\nexport const createAdminClient = () => {\n  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n  return createClient(supabaseUrl, serviceRoleKey, {\n    auth: {\n      autoRefreshToken: false,\n      persistSession: false\n    }\n  })\n}\n\n// Database types\nexport type Database = {\n  public: {\n    Tables: {\n      resellers: {\n        Row: {\n          id: string\n          name: string\n          email: string\n          territory: string\n          tier: 'gold' | 'silver' | 'bronze'\n          status: 'active' | 'inactive'\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          name: string\n          email: string\n          territory: string\n          tier: 'gold' | 'silver' | 'bronze'\n          status?: 'active' | 'inactive'\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          name?: string\n          email?: string\n          territory?: string\n          tier?: 'gold' | 'silver' | 'bronze'\n          status?: 'active' | 'inactive'\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      end_users: {\n        Row: {\n          id: string\n          company_name: string\n          contact_name: string\n          contact_email: string\n          territory: string\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          company_name: string\n          contact_name: string\n          contact_email: string\n          territory: string\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          company_name?: string\n          contact_name?: string\n          contact_email?: string\n          territory?: string\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      products: {\n        Row: {\n          id: string\n          name: string\n          category: string\n          list_price: number\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          name: string\n          category: string\n          list_price: number\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          name?: string\n          category?: string\n          list_price?: number\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      deals: {\n        Row: {\n          id: string\n          reseller_id: string\n          end_user_id: string\n          assigned_reseller_id: string | null\n          status: 'pending' | 'assigned' | 'disputed' | 'approved' | 'rejected'\n          total_value: number\n          submission_date: string\n          assignment_date: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          reseller_id: string\n          end_user_id: string\n          assigned_reseller_id?: string | null\n          status?: 'pending' | 'assigned' | 'disputed' | 'approved' | 'rejected'\n          total_value: number\n          submission_date?: string\n          assignment_date?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          reseller_id?: string\n          end_user_id?: string\n          assigned_reseller_id?: string | null\n          status?: 'pending' | 'assigned' | 'disputed' | 'approved' | 'rejected'\n          total_value?: number\n          submission_date?: string\n          assignment_date?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      deal_products: {\n        Row: {\n          id: string\n          deal_id: string\n          product_id: string\n          quantity: number\n          price: number\n          created_at: string\n        }\n        Insert: {\n          id?: string\n          deal_id: string\n          product_id: string\n          quantity: number\n          price: number\n          created_at?: string\n        }\n        Update: {\n          id?: string\n          deal_id?: string\n          product_id?: string\n          quantity?: number\n          price?: number\n          created_at?: string\n        }\n      }\n      deal_conflicts: {\n        Row: {\n          id: string\n          deal_id: string\n          competing_deal_id: string\n          conflict_type: 'duplicate_end_user' | 'territory_overlap' | 'timing_conflict'\n          resolution_status: 'pending' | 'resolved' | 'dismissed'\n          assigned_to_staff: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          deal_id: string\n          competing_deal_id: string\n          conflict_type: 'duplicate_end_user' | 'territory_overlap' | 'timing_conflict'\n          resolution_status?: 'pending' | 'resolved' | 'dismissed'\n          assigned_to_staff?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          deal_id?: string\n          competing_deal_id?: string\n          conflict_type?: 'duplicate_end_user' | 'territory_overlap' | 'timing_conflict'\n          resolution_status?: 'pending' | 'resolved' | 'dismissed'\n          assigned_to_staff?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      staff_users: {\n        Row: {\n          id: string\n          email: string\n          name: string\n          role: 'admin' | 'manager' | 'staff'\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          email: string\n          name: string\n          role?: 'admin' | 'manager' | 'staff'\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          email?: string\n          name?: string\n          role?: 'admin' | 'manager' | 'staff'\n          created_at?: string\n          updated_at?: string\n        }\n      }\n    }\n    Views: {\n      [_ in never]: never\n    }\n    Functions: {\n      [_ in never]: never\n    }\n    Enums: {\n      [_ in never]: never\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AACA;;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,8BAA8B;IACzC,OAAO,CAAA,GAAA,4KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;AAC1C;AAGO,MAAM,8BAA8B;IACzC,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAE1B,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,iBAAiB;QACtD,SAAS;YACP,KAAI,IAAY;gBACd,OAAO,YAAY,GAAG,CAAC,OAAO;YAChC;QACF;IACF;AACF;AAGO,MAAM,oBAAoB;IAC/B,MAAM,iBAAiB,QAAQ,GAAG,CAAC,yBAAyB;IAC5D,OAAO,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,gBAAgB;QAC/C,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/app/api/conflicts/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createServerComponentClient } from '@/lib/supabase'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const supabase = createServerComponentClient()\n    const { searchParams } = new URL(request.url)\n    \n    // Parse query parameters\n    const page = parseInt(searchParams.get('page') || '1')\n    const limit = parseInt(searchParams.get('limit') || '10')\n    const resolution_status = searchParams.get('resolution_status')\n    const conflict_type = searchParams.get('conflict_type')\n    const assigned_to_staff = searchParams.get('assigned_to_staff')\n    \n    const offset = (page - 1) * limit\n    \n    // Build query\n    let query = supabase\n      .from('deal_conflicts')\n      .select(`\n        *,\n        deal:deals!deal_conflicts_deal_id_fkey(\n          *,\n          reseller:resellers(*),\n          end_user:end_users(*),\n          products:deal_products(\n            *,\n            product:products(*)\n          )\n        ),\n        competing_deal:deals!deal_conflicts_competing_deal_id_fkey(\n          *,\n          reseller:resellers(*),\n          end_user:end_users(*),\n          products:deal_products(\n            *,\n            product:products(*)\n          )\n        ),\n        assigned_staff:staff_users(*)\n      `)\n      .order('created_at', { ascending: false })\n      .range(offset, offset + limit - 1)\n    \n    // Apply filters\n    if (resolution_status) {\n      query = query.eq('resolution_status', resolution_status)\n    }\n    \n    if (conflict_type) {\n      query = query.eq('conflict_type', conflict_type)\n    }\n    \n    if (assigned_to_staff) {\n      query = query.eq('assigned_to_staff', assigned_to_staff)\n    }\n    \n    const { data: conflicts, error, count } = await query\n    \n    if (error) {\n      console.error('Error fetching conflicts:', error)\n      return NextResponse.json(\n        { error: 'Failed to fetch conflicts', details: error.message },\n        { status: 500 }\n      )\n    }\n    \n    // Get total count for pagination\n    const { count: totalCount } = await supabase\n      .from('deal_conflicts')\n      .select('*', { count: 'exact', head: true })\n    \n    return NextResponse.json({\n      data: {\n        items: conflicts || [],\n        total: totalCount || 0,\n        page,\n        limit,\n        totalPages: Math.ceil((totalCount || 0) / limit)\n      },\n      success: true,\n      error: null\n    })\n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function PATCH(request: NextRequest) {\n  try {\n    const supabase = createServerComponentClient()\n    const body = await request.json()\n    \n    const { conflict_id, resolution_status, assigned_to_staff } = body\n    \n    if (!conflict_id) {\n      return NextResponse.json(\n        { error: 'Conflict ID is required' },\n        { status: 400 }\n      )\n    }\n    \n    const updateData: any = {}\n    \n    if (resolution_status) {\n      updateData.resolution_status = resolution_status\n    }\n    \n    if (assigned_to_staff !== undefined) {\n      updateData.assigned_to_staff = assigned_to_staff\n    }\n    \n    updateData.updated_at = new Date().toISOString()\n    \n    const { data: updatedConflict, error } = await supabase\n      .from('deal_conflicts')\n      .update(updateData)\n      .eq('id', conflict_id)\n      .select(`\n        *,\n        deal:deals!deal_conflicts_deal_id_fkey(\n          *,\n          reseller:resellers(*),\n          end_user:end_users(*)\n        ),\n        competing_deal:deals!deal_conflicts_competing_deal_id_fkey(\n          *,\n          reseller:resellers(*),\n          end_user:end_users(*)\n        ),\n        assigned_staff:staff_users(*)\n      `)\n      .single()\n    \n    if (error) {\n      console.error('Error updating conflict:', error)\n      return NextResponse.json(\n        { error: 'Failed to update conflict', details: error.message },\n        { status: 500 }\n      )\n    }\n    \n    return NextResponse.json({\n      data: updatedConflict,\n      success: true,\n      error: null\n    })\n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,wHAAA,CAAA,8BAA2B,AAAD;QAC3C,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAE5C,yBAAyB;QACzB,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,oBAAoB,aAAa,GAAG,CAAC;QAC3C,MAAM,gBAAgB,aAAa,GAAG,CAAC;QACvC,MAAM,oBAAoB,aAAa,GAAG,CAAC;QAE3C,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;QAE5B,cAAc;QACd,IAAI,QAAQ,SACT,IAAI,CAAC,kBACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;MAqBT,CAAC,EACA,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC,QAAQ,SAAS,QAAQ;QAElC,gBAAgB;QAChB,IAAI,mBAAmB;YACrB,QAAQ,MAAM,EAAE,CAAC,qBAAqB;QACxC;QAEA,IAAI,eAAe;YACjB,QAAQ,MAAM,EAAE,CAAC,iBAAiB;QACpC;QAEA,IAAI,mBAAmB;YACrB,QAAQ,MAAM,EAAE,CAAC,qBAAqB;QACxC;QAEA,MAAM,EAAE,MAAM,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;QAEhD,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAA6B,SAAS,MAAM,OAAO;YAAC,GAC7D;gBAAE,QAAQ;YAAI;QAElB;QAEA,iCAAiC;QACjC,MAAM,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,kBACL,MAAM,CAAC,KAAK;YAAE,OAAO;YAAS,MAAM;QAAK;QAE5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,MAAM;gBACJ,OAAO,aAAa,EAAE;gBACtB,OAAO,cAAc;gBACrB;gBACA;gBACA,YAAY,KAAK,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI;YAC5C;YACA,SAAS;YACT,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,MAAM,OAAoB;IAC9C,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,wHAAA,CAAA,8BAA2B,AAAD;QAC3C,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,MAAM,EAAE,WAAW,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,GAAG;QAE9D,IAAI,CAAC,aAAa;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,aAAkB,CAAC;QAEzB,IAAI,mBAAmB;YACrB,WAAW,iBAAiB,GAAG;QACjC;QAEA,IAAI,sBAAsB,WAAW;YACnC,WAAW,iBAAiB,GAAG;QACjC;QAEA,WAAW,UAAU,GAAG,IAAI,OAAO,WAAW;QAE9C,MAAM,EAAE,MAAM,eAAe,EAAE,KAAK,EAAE,GAAG,MAAM,SAC5C,IAAI,CAAC,kBACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,aACT,MAAM,CAAC,CAAC;;;;;;;;;;;;;MAaT,CAAC,EACA,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAA6B,SAAS,MAAM,OAAO;YAAC,GAC7D;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,MAAM;YACN,SAAS;YACT,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}