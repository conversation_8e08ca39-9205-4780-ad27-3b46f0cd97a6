{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_02e56efe._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_19197539.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp|ico|css|js)$).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp|ico|css|js)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "2mXt9Hxrd0xwn8zPRMrSMcfMEWIkC2kmuqHe2RfheiE=", "__NEXT_PREVIEW_MODE_ID": "2814a6a6f2dc2595ed7dd01064c29ec1", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7331cee96d355a90e373a24040d7b36ce8177630dab8c74b35dbd9a91ad12608", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c7cefb55c507bff550565f2eccb0f5b05312a23e657711698185ea145e35fcdc"}}}, "instrumentation": null, "functions": {}}