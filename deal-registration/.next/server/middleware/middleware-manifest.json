{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_02e56efe._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_19197539.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp|ico|css|js)$).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp|ico|css|js)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "2mXt9Hxrd0xwn8zPRMrSMcfMEWIkC2kmuqHe2RfheiE=", "__NEXT_PREVIEW_MODE_ID": "61eb7d5e354e407238424676a5207034", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "75d122dd6c5b37294d72df0d5df75dce527af849decc003ba9ca589f60b6fc5a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "6cba1c37ad8bd78fe4ece298a0222910af80ac5515f27c5a48d476c0a443c657"}}}, "instrumentation": null, "functions": {}}