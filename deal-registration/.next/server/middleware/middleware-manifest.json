{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_02e56efe._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_19197539.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "2mXt9Hxrd0xwn8zPRMrSMcfMEWIkC2kmuqHe2RfheiE=", "__NEXT_PREVIEW_MODE_ID": "90d2b1921127e194dc21c3ca3a5c8723", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9a57a97a6966e89bba664e9e3ac81197520eb7eb7a3167276c097cfa55811d71", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a07c8ef2eb2a0070187f5306f5a9780a95a77e25460d584b1d2a0d0df7474ac4"}}}, "instrumentation": null, "functions": {}}