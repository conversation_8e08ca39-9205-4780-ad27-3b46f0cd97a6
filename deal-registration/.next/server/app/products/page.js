(()=>{var e={};e.id=571,e.ids=[571],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5613:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var s=r(60687),a=r(43210),i=r(7425),l=r(44493),n=r(29523),o=r(89667),d=r(96834),c=r(4780),x=r(99270),u=r(96474),p=r(19080),h=r(63143),m=r(88233);function g(){let[e,t]=(0,a.useState)([]),[r,g]=(0,a.useState)(!0),[f,j]=(0,a.useState)(""),[y,v]=(0,a.useState)(null),b=e.filter(e=>e.name.toLowerCase().includes(f.toLowerCase())||e.category.toLowerCase().includes(f.toLowerCase())),N=e=>{switch(e.toLowerCase()){case"software":return"bg-blue-100 text-blue-800";case"hardware":return"bg-green-100 text-green-800";case"services":return"bg-purple-100 text-purple-800";case"support":return"bg-orange-100 text-orange-800";case"training":return"bg-indigo-100 text-indigo-800";default:return"bg-gray-100 text-gray-800"}};if(r)return(0,s.jsx)(i.O,{title:"Products",subtitle:"Manage your product catalog",children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"text-gray-500",children:"Loading products..."})})});if(y)return(0,s.jsx)(i.O,{title:"Products",subtitle:"Manage your product catalog",children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"text-red-500",children:y})})});let w=[...new Set(e.map(e=>e.category))],P=e.reduce((e,t)=>e+t.list_price,0);return(0,s.jsx)(i.O,{title:"Products",subtitle:"Manage your product catalog",children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"relative w-96",children:[(0,s.jsx)(x.A,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400"}),(0,s.jsx)(o.p,{placeholder:"Search products...",value:f,onChange:e=>j(e.target.value),className:"pl-10"})]}),(0,s.jsxs)(n.$,{children:[(0,s.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Add Product"]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4",children:[(0,s.jsxs)(l.Zp,{children:[(0,s.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(l.ZB,{className:"text-sm font-medium",children:"Total Products"}),(0,s.jsx)(p.A,{className:"h-4 w-4 text-gray-400"})]}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold",children:e.length})})]}),(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,s.jsx)(l.ZB,{className:"text-sm font-medium",children:"Categories"})}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold",children:w.length})})]}),(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,s.jsx)(l.ZB,{className:"text-sm font-medium",children:"Total Catalog Value"})}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold",children:(0,c.vv)(P)})})]}),(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,s.jsx)(l.ZB,{className:"text-sm font-medium",children:"Avg. Price"})}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold",children:(0,c.vv)(e.length>0?P/e.length:0)})})]})]}),(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{children:(0,s.jsx)(l.ZB,{children:"All Products"})}),(0,s.jsxs)(l.Wu,{children:[(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b",children:[(0,s.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Name"}),(0,s.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Category"}),(0,s.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"List Price"}),(0,s.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Created"}),(0,s.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Actions"})]})}),(0,s.jsx)("tbody",{children:b.map(e=>(0,s.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"py-3 px-4 font-medium",children:e.name}),(0,s.jsx)("td",{className:"py-3 px-4",children:(0,s.jsx)(d.E,{className:N(e.category),children:e.category})}),(0,s.jsx)("td",{className:"py-3 px-4 font-medium",children:(0,c.vv)(e.list_price)}),(0,s.jsx)("td",{className:"py-3 px-4 text-gray-600",children:new Date(e.created_at).toLocaleDateString()}),(0,s.jsx)("td",{className:"py-3 px-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,s.jsx)(h.A,{className:"h-4 w-4"})}),(0,s.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,s.jsx)(m.A,{className:"h-4 w-4"})})]})})]},e.id))})]})}),0===b.length&&(0,s.jsx)("div",{className:"text-center py-8 text-gray-500",children:f?"No products found matching your search.":"No products found."})]})]})]})})}},8547:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/deal registration/deal-registration/src/app/products/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/deal registration/deal-registration/src/app/products/page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},17778:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),l=r.n(i),n=r(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let d={children:["",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8547)),"/home/<USER>/deal registration/deal-registration/src/app/products/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/home/<USER>/deal registration/deal-registration/src/app/layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"/home/<USER>/deal registration/deal-registration/src/app/error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"/home/<USER>/deal registration/deal-registration/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"/home/<USER>/deal registration/deal-registration/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/deal registration/deal-registration/src/app/products/page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/products/page",pathname:"/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},65178:(e,t,r)=>{Promise.resolve().then(r.bind(r,5613))},74075:e=>{"use strict";e.exports=require("zlib")},79181:(e,t,r)=>{Promise.resolve().then(r.bind(r,8547))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88233:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96474:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,17,517,375,501],()=>r(17778));module.exports=s})();