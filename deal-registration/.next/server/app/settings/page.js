(()=>{var e={};e.id=662,e.ids=[662],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},51896:(e,s,t)=>{Promise.resolve().then(t.bind(t,74198))},55502:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var r=t(65239),a=t(48088),i=t(88170),n=t.n(i),l=t(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(s,c);let d={children:["",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,74198)),"/home/<USER>/deal registration/deal-registration/src/app/settings/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/home/<USER>/deal registration/deal-registration/src/app/layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"/home/<USER>/deal registration/deal-registration/src/app/error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"/home/<USER>/deal registration/deal-registration/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"/home/<USER>/deal registration/deal-registration/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["/home/<USER>/deal registration/deal-registration/src/app/settings/page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62653:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var r=t(60687),a=t(43210),i=t(7425),n=t(44493),l=t(29523),c=t(89667),d=t(96834),o=t(84027),x=t(41312),m=t(62688);let h=(0,m.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),u=(0,m.A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);var j=t(97051);function p(){let[e,s]=(0,a.useState)("general"),t=[{id:"general",label:"General",icon:o.A},{id:"users",label:"Users & Permissions",icon:x.A},{id:"security",label:"Security",icon:h},{id:"database",label:"Database",icon:u},{id:"notifications",label:"Notifications",icon:j.A}],m=()=>(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsx)(n.ZB,{children:"Application Settings"})}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Application Name"}),(0,r.jsx)(c.p,{defaultValue:"Deal Registration System"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Company Name"}),(0,r.jsx)(c.p,{defaultValue:"Your Company"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Support Email"}),(0,r.jsx)(c.p,{defaultValue:"<EMAIL>",type:"email"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Default Territory"}),(0,r.jsx)(c.p,{defaultValue:"Global"})]})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsx)(n.ZB,{children:"Deal Registration Rules"})}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Auto-assign deals"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Automatically assign deals based on territory rules"})]}),(0,r.jsx)(l.$,{variant:"outline",size:"sm",children:"Configure"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Conflict detection"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Enable automatic conflict detection for overlapping deals"})]}),(0,r.jsx)(d.E,{variant:"success",children:"Enabled"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Approval workflow"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Require manager approval for high-value deals"})]}),(0,r.jsx)(l.$,{variant:"outline",size:"sm",children:"Configure"})]})]})]})]}),p=()=>(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsx)(n.ZB,{children:"Staff Users"})}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"System Admin"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"<EMAIL>"})]}),(0,r.jsx)(d.E,{children:"Admin"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Deal Manager"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"<EMAIL>"})]}),(0,r.jsx)(d.E,{variant:"secondary",children:"Manager"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Deal Staff"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"<EMAIL>"})]}),(0,r.jsx)(d.E,{variant:"outline",children:"Staff"})]})]}),(0,r.jsx)(l.$,{className:"w-full mt-4",children:"Add New User"})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsx)(n.ZB,{children:"Role Permissions"})}),(0,r.jsx)(n.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Admin"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Full system access, user management, settings configuration"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Manager"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Deal assignment, conflict resolution, reporting"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Staff"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"View deals, basic conflict management"})]})]})})]})]}),f=()=>(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsx)(n.ZB,{children:"Authentication"})}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Two-Factor Authentication"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Require 2FA for all users"})]}),(0,r.jsx)(d.E,{variant:"warning",children:"Disabled"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Session Timeout"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Auto-logout after inactivity"})]}),(0,r.jsx)("span",{className:"text-sm",children:"8 hours"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Password Policy"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Minimum password requirements"})]}),(0,r.jsx)(l.$,{variant:"outline",size:"sm",children:"Configure"})]})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsx)(n.ZB,{children:"Data Protection"})}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Row Level Security"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Database-level access control"})]}),(0,r.jsx)(d.E,{variant:"success",children:"Enabled"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Audit Logging"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Track all system changes"})]}),(0,r.jsx)(d.E,{variant:"success",children:"Enabled"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Data Encryption"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Encrypt sensitive data at rest"})]}),(0,r.jsx)(d.E,{variant:"success",children:"Enabled"})]})]})]})]}),g=()=>(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsx)(n.ZB,{children:"Database Status"})}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{children:"Connection Status"}),(0,r.jsx)(d.E,{variant:"success",children:"Connected"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{children:"Database Version"}),(0,r.jsx)("span",{className:"text-sm",children:"PostgreSQL 15.3"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{children:"Total Tables"}),(0,r.jsx)("span",{className:"text-sm",children:"9"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{children:"Last Backup"}),(0,r.jsx)("span",{className:"text-sm",children:"2 hours ago"})]})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsx)(n.ZB,{children:"Maintenance"})}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[(0,r.jsx)(l.$,{variant:"outline",className:"w-full",children:"Run Database Cleanup"}),(0,r.jsx)(l.$,{variant:"outline",className:"w-full",children:"Rebuild Indexes"}),(0,r.jsx)(l.$,{variant:"outline",className:"w-full",children:"Export Data"}),(0,r.jsx)(l.$,{variant:"destructive",className:"w-full",children:"Reset Sample Data"})]})]})]}),v=()=>(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsx)(n.ZB,{children:"Email Notifications"})}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"New Deal Submissions"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Notify when new deals are registered"})]}),(0,r.jsx)(d.E,{variant:"success",children:"Enabled"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Conflict Alerts"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Notify when conflicts are detected"})]}),(0,r.jsx)(d.E,{variant:"success",children:"Enabled"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Assignment Updates"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Notify when deals are assigned"})]}),(0,r.jsx)(d.E,{variant:"warning",children:"Disabled"})]})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsx)(n.ZB,{children:"System Alerts"})}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Database Errors"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Critical system notifications"})]}),(0,r.jsx)(d.E,{variant:"success",children:"Enabled"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Performance Warnings"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"System performance alerts"})]}),(0,r.jsx)(d.E,{variant:"success",children:"Enabled"})]})]})]})]});return(0,r.jsx)(i.O,{title:"Settings",subtitle:"Configure system preferences and options",children:(0,r.jsxs)("div",{className:"flex gap-6",children:[(0,r.jsx)("div",{className:"w-64 space-y-2",children:t.map(t=>{let a=t.icon;return(0,r.jsxs)("button",{onClick:()=>s(t.id),className:`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${e===t.id?"bg-blue-100 text-blue-700":"text-gray-600 hover:bg-gray-100"}`,children:[(0,r.jsx)(a,{className:"h-5 w-5"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:t.label})]},t.id)})}),(0,r.jsx)("div",{className:"flex-1",children:(()=>{switch(e){case"general":default:return m();case"users":return p();case"security":return f();case"database":return g();case"notifications":return v()}})()})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},74198:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/deal registration/deal-registration/src/app/settings/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/deal registration/deal-registration/src/app/settings/page.tsx","default")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},86744:(e,s,t)=>{Promise.resolve().then(t.bind(t,62653))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,17,517,375,501],()=>t(55502));module.exports=r})();