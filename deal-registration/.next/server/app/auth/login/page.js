(()=>{var e={};e.id=859,e.ids=[859],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},15254:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=t(65239),a=t(48088),i=t(88170),l=t.n(i),n=t(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(r,o);let d={children:["",{children:["auth",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,81351)),"/home/<USER>/deal registration/deal-registration/src/app/auth/login/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/home/<USER>/deal registration/deal-registration/src/app/layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"/home/<USER>/deal registration/deal-registration/src/app/error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"/home/<USER>/deal registration/deal-registration/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"/home/<USER>/deal registration/deal-registration/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/deal registration/deal-registration/src/app/auth/login/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/login/page",pathname:"/auth/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44493:(e,r,t)=>{"use strict";t.d(r,{Wu:()=>d,ZB:()=>o,Zp:()=>l,aR:()=>n});var s=t(60687),a=t(43210),i=t(4780);let l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border border-gray-200 bg-white text-gray-900 shadow-sm",e),...r}));l.displayName="Card";let n=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...r}));n.displayName="CardHeader";let o=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));o.displayName="CardTitle",a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-gray-600",e),...r})).displayName="CardDescription";let d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...r}));d.displayName="CardContent",a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"},52828:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>f});var s=t(60687),a=t(43210);t(16391);var i=t(16189),l=t(44493),n=t(29523),o=t(89667),d=t(24224),c=t(4780);let u=(0,d.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),p=a.forwardRef(({className:e,variant:r,...t},a)=>(0,s.jsx)("div",{ref:a,role:"alert",className:(0,c.cn)(u({variant:r}),e),...t}));p.displayName="Alert",a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h5",{ref:t,className:(0,c.cn)("mb-1 font-medium leading-none tracking-tight",e),...r})).displayName="AlertTitle";let x=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,c.cn)("text-sm [&_p]:leading-relaxed",e),...r}));x.displayName="AlertDescription";var m=t(62688);let h=(0,m.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]),g=(0,m.A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]),v=(0,m.A)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]]);function f(){let[e,r]=(0,a.useState)(""),[t,d]=(0,a.useState)(""),[c,u]=(0,a.useState)(!1),[m,f]=(0,a.useState)(null),[y,w]=(0,a.useState)(null),[b,j]=(0,a.useState)(null),N=(0,i.useRouter)(),k=async r=>{if(r.preventDefault(),!b)return void f("Authentication service is not available");u(!0),f(null);try{let{error:r}=await b.auth.signInWithPassword({email:e,password:t});r?f(r.message):(N.push("/"),N.refresh())}catch{f("An unexpected error occurred")}finally{u(!1)}},C=async()=>{if(!b)return void f("Authentication service is not available");u(!0),f(null);try{let{error:e}=await b.auth.signInWithOAuth({provider:"google",options:{redirectTo:`${window.location.origin}/auth/callback`}});e&&(f(e.message),u(!1))}catch{f("An unexpected error occurred"),u(!1)}},q=async()=>{if(!e)return void f("Please enter your email address");if(!b)return void f("Authentication service is not available");u(!0),f(null);try{let{error:r}=await b.auth.signInWithOtp({email:e,options:{emailRedirectTo:`${window.location.origin}/auth/callback`}});r?f(r.message):w("Check your email for the magic link!")}catch{f("An unexpected error occurred")}finally{u(!1)}};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"mt-6 text-3xl font-bold text-gray-900",children:"Deal Registration System"}),(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Sign in to your account"})]}),(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{children:(0,s.jsx)(l.ZB,{className:"text-center",children:"Sign In"})}),(0,s.jsxs)(l.Wu,{className:"space-y-6",children:[m&&(0,s.jsx)(p,{variant:"destructive",children:(0,s.jsx)(x,{children:m})}),y&&(0,s.jsx)(p,{children:(0,s.jsx)(x,{children:y})}),(0,s.jsxs)(n.$,{onClick:C,disabled:c,className:"w-full",variant:"outline",children:[(0,s.jsxs)("svg",{className:"w-5 h-5 mr-2",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Continue with Google"]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,s.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]}),(0,s.jsxs)("form",{onSubmit:k,className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"sr-only",children:"Email address"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(h,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400"}),(0,s.jsx)(o.p,{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"pl-10",placeholder:"Email address",value:e,onChange:e=>r(e.target.value)})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"sr-only",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(g,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400"}),(0,s.jsx)(o.p,{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,className:"pl-10",placeholder:"Password",value:t,onChange:e=>d(e.target.value)})]})]}),(0,s.jsxs)(n.$,{type:"submit",disabled:c,className:"w-full",children:[(0,s.jsx)(v,{className:"w-4 h-4 mr-2"}),c?"Signing in...":"Sign in"]})]}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)(n.$,{variant:"link",onClick:q,disabled:c||!e,className:"text-sm",children:"Send magic link instead"})}),(0,s.jsxs)("div",{className:"text-center text-sm text-gray-600",children:["Don't have an account?"," ",(0,s.jsx)(n.$,{variant:"link",className:"p-0 h-auto text-sm",children:"Contact your administrator"})]})]})]})]})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77411:(e,r,t)=>{Promise.resolve().then(t.bind(t,52828))},78091:(e,r,t)=>{Promise.resolve().then(t.bind(t,81351))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81351:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/deal registration/deal-registration/src/app/auth/login/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/deal registration/deal-registration/src/app/auth/login/page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},89667:(e,r,t)=>{"use strict";t.d(r,{p:()=>l});var s=t(60687),a=t(43210),i=t(4780);let l=a.forwardRef(({className:e,type:r,...t},a)=>(0,s.jsx)("input",{type:r,className:(0,i.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...t}));l.displayName="Input"},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,17,517,375],()=>t(15254));module.exports=s})();