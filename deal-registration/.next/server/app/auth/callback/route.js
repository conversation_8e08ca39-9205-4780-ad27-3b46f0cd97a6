(()=>{var e={};e.id=15,e.ids=[15],e.modules={1106:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>c});var i=t(96559),o=t(48088),a=t(37719),u=t(56621),n=t(32190);async function c(e){let r=new URL(e.url),t=r.searchParams.get("code");if(t)try{let e=(0,u.Wz)();await e.auth.exchangeCodeForSession(t)}catch(e){return console.error("Error exchanging code for session:",e),n.NextResponse.redirect(`${r.origin}/auth/login?error=auth_error`)}return n.NextResponse.redirect(`${r.origin}/`)}let p=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/auth/callback/route",pathname:"/auth/callback",filename:"route",bundlePath:"app/auth/callback/route"},resolvedPagePath:"/home/<USER>/deal registration/deal-registration/src/app/auth/callback/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:x}=p;function h(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,r,t)=>{"use strict";t.d(r,{Wz:()=>a,Zf:()=>u});var s=t(66437);t(98766);let i="https://xnyyanfulgvcsgjiyfyb.supabase.co",o="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhueXlhbmZ1bGd2Y3Nnaml5ZnliIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIzNTgzMTIsImV4cCI6MjA2NzkzNDMxMn0.iOR4d3e3UROwqkKZIOTFHDXuK4vVLs9LaSOj2Thz2WE",a=()=>{if(!i||!o)throw Error("Missing Supabase environment variables");return(0,s.UU)(i,o)},u=()=>{let e=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!i||!e)throw Error("Missing Supabase environment variables");return(0,s.UU)(i,e,{auth:{autoRefreshToken:!1,persistSession:!1}})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580,478],()=>t(1106));module.exports=s})();