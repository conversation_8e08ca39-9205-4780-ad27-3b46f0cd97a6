(()=>{var e={};e.id=81,e.ids=[81],e.modules={1555:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/deal registration/deal-registration/src/app/end-users/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/deal registration/deal-registration/src/app/end-users/page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20350:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=t(65239),a=t(48088),i=t(88170),n=t.n(i),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let d={children:["",{children:["end-users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1555)),"/home/<USER>/deal registration/deal-registration/src/app/end-users/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/home/<USER>/deal registration/deal-registration/src/app/layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"/home/<USER>/deal registration/deal-registration/src/app/error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"/home/<USER>/deal registration/deal-registration/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"/home/<USER>/deal registration/deal-registration/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/deal registration/deal-registration/src/app/end-users/page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/end-users/page",pathname:"/end-users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},53235:(e,s,t)=>{Promise.resolve().then(t.bind(t,1555))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},73609:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u});var r=t(60687),a=t(43210),i=t(7425),n=t(44493),l=t(29523),o=t(89667),d=t(99270),c=t(96474),x=t(17313),p=t(63143),h=t(88233);function u(){let[e,s]=(0,a.useState)([]),[t,u]=(0,a.useState)(!0),[m,f]=(0,a.useState)(""),[j,g]=(0,a.useState)(null),y=e.filter(e=>e.company_name.toLowerCase().includes(m.toLowerCase())||e.contact_name.toLowerCase().includes(m.toLowerCase())||e.contact_email.toLowerCase().includes(m.toLowerCase())||e.territory.toLowerCase().includes(m.toLowerCase()));return t?(0,r.jsx)(i.O,{title:"End Users",subtitle:"Manage customer organizations",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-gray-500",children:"Loading end users..."})})}):j?(0,r.jsx)(i.O,{title:"End Users",subtitle:"Manage customer organizations",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-red-500",children:j})})}):(0,r.jsx)(i.O,{title:"End Users",subtitle:"Manage customer organizations",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"relative w-96",children:[(0,r.jsx)(d.A,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400"}),(0,r.jsx)(o.p,{placeholder:"Search end users...",value:m,onChange:e=>f(e.target.value),className:"pl-10"})]}),(0,r.jsxs)(l.$,{children:[(0,r.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Add End User"]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Total Organizations"}),(0,r.jsx)(x.A,{className:"h-4 w-4 text-gray-400"})]}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold",children:e.length})})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Northeast US"})}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold",children:e.filter(e=>"Northeast US"===e.territory).length})})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"West Coast"})}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold",children:e.filter(e=>"West Coast"===e.territory).length})})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Other Territories"})}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold",children:e.filter(e=>!["Northeast US","West Coast"].includes(e.territory)).length})})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsx)(n.ZB,{children:"All End Users"})}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"border-b",children:[(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Company"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Contact Name"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Email"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Territory"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Created"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Actions"})]})}),(0,r.jsx)("tbody",{children:y.map(e=>(0,r.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"py-3 px-4 font-medium",children:e.company_name}),(0,r.jsx)("td",{className:"py-3 px-4",children:e.contact_name}),(0,r.jsx)("td",{className:"py-3 px-4 text-gray-600",children:e.contact_email}),(0,r.jsx)("td",{className:"py-3 px-4",children:e.territory}),(0,r.jsx)("td",{className:"py-3 px-4 text-gray-600",children:new Date(e.created_at).toLocaleDateString()}),(0,r.jsx)("td",{className:"py-3 px-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(l.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(p.A,{className:"h-4 w-4"})}),(0,r.jsx)(l.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(h.A,{className:"h-4 w-4"})})]})})]},e.id))})]})}),0===y.length&&(0,r.jsx)("div",{className:"text-center py-8 text-gray-500",children:m?"No end users found matching your search.":"No end users found."})]})]})]})})}},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88233:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},90187:(e,s,t)=>{Promise.resolve().then(t.bind(t,73609))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96474:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,17,517,375,501],()=>t(20350));module.exports=r})();