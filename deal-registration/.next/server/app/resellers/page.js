(()=>{var e={};e.id=406,e.ids=[406],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},34900:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/deal registration/deal-registration/src/app/resellers/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/deal registration/deal-registration/src/app/resellers/page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},63382:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>p,tree:()=>o});var t=s(65239),a=s(48088),l=s(88170),i=s.n(l),n=s(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(r,d);let o={children:["",{children:["resellers",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,34900)),"/home/<USER>/deal registration/deal-registration/src/app/resellers/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/home/<USER>/deal registration/deal-registration/src/app/layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"/home/<USER>/deal registration/deal-registration/src/app/error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"/home/<USER>/deal registration/deal-registration/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"/home/<USER>/deal registration/deal-registration/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/deal registration/deal-registration/src/app/resellers/page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/resellers/page",pathname:"/resellers",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},74075:e=>{"use strict";e.exports=require("zlib")},74426:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>u});var t=s(60687),a=s(43210),l=s(7425),i=s(44493),n=s(29523),d=s(89667),o=s(96834),c=s(99270),x=s(96474),p=s(63143),h=s(88233);function u(){let[e,r]=(0,a.useState)([]),[s,u]=(0,a.useState)(!0),[m,g]=(0,a.useState)(""),[f,j]=(0,a.useState)(null),y=e.filter(e=>e.name.toLowerCase().includes(m.toLowerCase())||e.email.toLowerCase().includes(m.toLowerCase())||e.territory.toLowerCase().includes(m.toLowerCase())),v=e=>{switch(e){case"gold":return"bg-yellow-100 text-yellow-800";case"silver":default:return"bg-gray-100 text-gray-800";case"bronze":return"bg-orange-100 text-orange-800"}},b=e=>"active"===e?"bg-green-100 text-green-800":"bg-red-100 text-red-800";return s?(0,t.jsx)(l.O,{title:"Resellers",subtitle:"Manage your partner network",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"text-gray-500",children:"Loading resellers..."})})}):f?(0,t.jsx)(l.O,{title:"Resellers",subtitle:"Manage your partner network",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"text-red-500",children:f})})}):(0,t.jsx)(l.O,{title:"Resellers",subtitle:"Manage your partner network",children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"relative w-96",children:[(0,t.jsx)(c.A,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400"}),(0,t.jsx)(d.p,{placeholder:"Search resellers...",value:m,onChange:e=>g(e.target.value),className:"pl-10"})]}),(0,t.jsxs)(n.$,{children:[(0,t.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Add Reseller"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4",children:[(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,t.jsx)(i.ZB,{className:"text-sm font-medium",children:"Total Resellers"})}),(0,t.jsx)(i.Wu,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:e.length})})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,t.jsx)(i.ZB,{className:"text-sm font-medium",children:"Gold Partners"})}),(0,t.jsx)(i.Wu,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:e.filter(e=>"gold"===e.tier).length})})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,t.jsx)(i.ZB,{className:"text-sm font-medium",children:"Silver Partners"})}),(0,t.jsx)(i.Wu,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:e.filter(e=>"silver"===e.tier).length})})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,t.jsx)(i.ZB,{className:"text-sm font-medium",children:"Active Partners"})}),(0,t.jsx)(i.Wu,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:e.filter(e=>"active"===e.status).length})})]})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsx)(i.ZB,{children:"All Resellers"})}),(0,t.jsxs)(i.Wu,{children:[(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"border-b",children:[(0,t.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Name"}),(0,t.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Email"}),(0,t.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Territory"}),(0,t.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Tier"}),(0,t.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Status"}),(0,t.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Actions"})]})}),(0,t.jsx)("tbody",{children:y.map(e=>(0,t.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,t.jsx)("td",{className:"py-3 px-4 font-medium",children:e.name}),(0,t.jsx)("td",{className:"py-3 px-4 text-gray-600",children:e.email}),(0,t.jsx)("td",{className:"py-3 px-4",children:e.territory}),(0,t.jsx)("td",{className:"py-3 px-4",children:(0,t.jsx)(o.E,{className:v(e.tier),children:e.tier})}),(0,t.jsx)("td",{className:"py-3 px-4",children:(0,t.jsx)(o.E,{className:b(e.status),children:e.status})}),(0,t.jsx)("td",{className:"py-3 px-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,t.jsx)(p.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,t.jsx)(h.A,{className:"h-4 w-4"})})]})})]},e.id))})]})}),0===y.length&&(0,t.jsx)("div",{className:"text-center py-8 text-gray-500",children:m?"No resellers found matching your search.":"No resellers found."})]})]})]})})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82960:(e,r,s)=>{Promise.resolve().then(s.bind(s,34900))},88233:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},91645:e=>{"use strict";e.exports=require("net")},92688:(e,r,s)=>{Promise.resolve().then(s.bind(s,74426))},94735:e=>{"use strict";e.exports=require("events")},96474:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,17,517,375,501],()=>s(63382));module.exports=t})();