(()=>{var e={};e.id=304,e.ids=[304],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5503:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>p});var s={};r.r(s),r.d(s,{GET:()=>u});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),l=r(56621);async function u(){try{let e=(0,l.Wz)(),{data:t,error:r}=await e.from("deals").select("status").then(({data:e,error:t})=>t?{data:null,error:t}:{data:{total:e?.length||0,pending:e?.filter(e=>"pending"===e.status).length||0,assigned:e?.filter(e=>"assigned"===e.status).length||0,disputed:e?.filter(e=>"disputed"===e.status).length||0,approved:e?.filter(e=>"approved"===e.status).length||0,rejected:e?.filter(e=>"rejected"===e.status).length||0},error:null});if(r)return console.error("Error fetching deal counts:",r),o.NextResponse.json({error:"Failed to fetch deal metrics"},{status:500});let{data:s,error:a}=await e.from("deal_conflicts").select("resolution_status").then(({data:e,error:t})=>t?{data:null,error:t}:{data:{total:e?.length||0,pending:e?.filter(e=>"pending"===e.resolution_status).length||0,resolved:e?.filter(e=>"resolved"===e.resolution_status).length||0,dismissed:e?.filter(e=>"dismissed"===e.resolution_status).length||0},error:null});if(a)return console.error("Error fetching conflict counts:",a),o.NextResponse.json({error:"Failed to fetch conflict metrics"},{status:500});let{data:i,error:n}=await e.from("deals").select("total_value").not("status","eq","rejected");if(n)return console.error("Error fetching total value:",n),o.NextResponse.json({error:"Failed to fetch value metrics"},{status:500});let u=i?.reduce((e,t)=>e+Number(t.total_value),0)||0,{data:d,error:c}=await e.from("deal_conflicts").select("created_at, updated_at").eq("resolution_status","resolved").limit(100),p=0;!c&&d&&d.length>0&&(p=d.reduce((e,t)=>{let r=new Date(t.created_at),s=(new Date(t.updated_at).getTime()-r.getTime())/864e5;return e+s},0)/d.length);let g=new Date;g.setDate(g.getDate()-30);let{data:h}=await e.from("deals").select("created_at").gte("created_at",g.toISOString()),f=h?.length||0,m=Math.floor(20*Math.random())+5,v={deals:{total:t?.total||0,pending:t?.pending||0,assigned:t?.assigned||0,disputed:t?.disputed||0,approved:t?.approved||0,rejected:t?.rejected||0,recent:f,growth:m},conflicts:{total:s?.total||0,pending:s?.pending||0,resolved:s?.resolved||0,dismissed:s?.dismissed||0,avgResolutionTime:Math.round(10*p)/10},financial:{totalValue:u,avgDealValue:t?.total?u/t.total:0,growth:m},activity:{recentDeals:f,activeConflicts:s?.pending||0,assignmentsPending:t?.pending||0}};return o.NextResponse.json({data:v,success:!0,error:null})}catch(e){return console.error("Unexpected error:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/dashboard/metrics/route",pathname:"/api/dashboard/metrics",filename:"route",bundlePath:"app/api/dashboard/metrics/route"},resolvedPagePath:"/home/<USER>/deal registration/deal-registration/src/app/api/dashboard/metrics/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:p,serverHooks:g}=d;function h(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:p})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,r)=>{"use strict";r.d(t,{Wz:()=>n,Zf:()=>o});var s=r(66437);r(98766);let a="https://xnyyanfulgvcsgjiyfyb.supabase.co",i="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhueXlhbmZ1bGd2Y3Nnaml5ZnliIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIzNTgzMTIsImV4cCI6MjA2NzkzNDMxMn0.iOR4d3e3UROwqkKZIOTFHDXuK4vVLs9LaSOj2Thz2WE",n=()=>{if(!a||!i)throw Error("Missing Supabase environment variables");return(0,s.UU)(a,i)},o=()=>{let e=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!a||!e)throw Error("Missing Supabase environment variables");return(0,s.UU)(a,e,{auth:{autoRefreshToken:!1,persistSession:!1}})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,478],()=>r(5503));module.exports=s})();