(()=>{var e={};e.id=118,e.ids=[118],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,r,s)=>{"use strict";s.d(r,{Wz:()=>o,Zf:()=>a});var t=s(66437);s(98766);let n="https://xnyyanfulgvcsgjiyfyb.supabase.co",i="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhueXlhbmZ1bGd2Y3Nnaml5ZnliIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIzNTgzMTIsImV4cCI6MjA2NzkzNDMxMn0.iOR4d3e3UROwqkKZIOTFHDXuK4vVLs9LaSOj2Thz2WE",o=()=>{if(!n||!i)throw Error("Missing Supabase environment variables");return(0,t.UU)(n,i)},a=()=>{let e=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!n||!e)throw Error("Missing Supabase environment variables");return(0,t.UU)(n,e,{auth:{autoRefreshToken:!1,persistSession:!1}})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},75350:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>g,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var t={};s.r(t),s.d(t,{GET:()=>c,POST:()=>p});var n=s(96559),i=s(48088),o=s(37719),a=s(32190),u=s(56621);async function c(e){try{let r=(0,u.Wz)(),{searchParams:s}=new URL(e.url),t=parseInt(s.get("page")||"1"),n=parseInt(s.get("limit")||"50"),i=s.get("search")||"",o=s.get("territory")||"",c=(t-1)*n,p=r.from("end_users").select("*",{count:"exact"}).order("created_at",{ascending:!1}).range(c,c+n-1);i&&(p=p.or(`company_name.ilike.%${i}%,contact_name.ilike.%${i}%,contact_email.ilike.%${i}%`)),o&&(p=p.eq("territory",o));let{data:d,error:l,count:x}=await p;if(l)return console.error("Error fetching end users:",l),a.NextResponse.json({success:!1,error:"Failed to fetch end users",details:l},{status:500});let m=Math.ceil((x||0)/n);return a.NextResponse.json({success:!0,data:{items:d||[],total:x||0,page:t,limit:n,totalPages:m},error:null})}catch(e){return console.error("Unexpected error in end-users API:",e),a.NextResponse.json({success:!1,error:"Internal server error",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function p(e){try{let r=(0,u.Wz)(),{company_name:s,contact_name:t,contact_email:n,territory:i}=await e.json();if(!s||!t||!n||!i)return a.NextResponse.json({success:!1,error:"Missing required fields: company_name, contact_name, contact_email, territory"},{status:400});let{data:o}=await r.from("end_users").select("id").eq("company_name",s).eq("territory",i).single();if(o)return a.NextResponse.json({success:!1,error:"End user with this company name already exists in this territory"},{status:409});let{data:c,error:p}=await r.from("end_users").insert([{company_name:s,contact_name:t,contact_email:n,territory:i}]).select().single();if(p)return console.error("Error creating end user:",p),a.NextResponse.json({success:!1,error:"Failed to create end user",details:p},{status:500});return a.NextResponse.json({success:!0,data:c,error:null},{status:201})}catch(e){return console.error("Unexpected error in end-users POST:",e),a.NextResponse.json({success:!1,error:"Internal server error",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/end-users/route",pathname:"/api/end-users",filename:"route",bundlePath:"app/api/end-users/route"},resolvedPagePath:"/home/<USER>/deal registration/deal-registration/src/app/api/end-users/route.ts",nextConfigOutput:"standalone",userland:t}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:m}=d;function g(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,580,478],()=>s(75350));module.exports=t})();