const CHUNK_PUBLIC_PATH = "server/app/api/deals/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_@supabase_node-fetch_lib_index_64219198.js");
runtime.loadChunk("server/chunks/node_modules_next_a61cec7d._.js");
runtime.loadChunk("server/chunks/node_modules_tr46_816df9d9._.js");
runtime.loadChunk("server/chunks/node_modules_ws_daabdc74._.js");
runtime.loadChunk("server/chunks/node_modules_@supabase_auth-js_dist_module_4e4e8dc6._.js");
runtime.loadChunk("server/chunks/node_modules_zod_v4_cdc66567._.js");
runtime.loadChunk("server/chunks/node_modules_tailwind-merge_dist_bundle-mjs_mjs_b7864412._.js");
runtime.loadChunk("server/chunks/node_modules_7c5e4fa4._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__7fe82df6._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/deals/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/deals/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/deals/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
