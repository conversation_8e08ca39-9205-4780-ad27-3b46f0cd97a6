{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_02e56efe._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_19197539.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "2mXt9Hxrd0xwn8zPRMrSMcfMEWIkC2kmuqHe2RfheiE=", "__NEXT_PREVIEW_MODE_ID": "d39c5402e490ea2c23b6246243d13463", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1e2864c008c0bf2904aa7e34f835e1668f1ad5b1573802d59726494f946be248", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ded1d17bf593c216840562bc03b9e95f7bed0be09ff32b47cc26080f001ca869"}}}, "sortedMiddleware": ["/"], "functions": {}}