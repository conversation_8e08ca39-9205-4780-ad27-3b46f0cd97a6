{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_02e56efe._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_19197539.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "2mXt9Hxrd0xwn8zPRMrSMcfMEWIkC2kmuqHe2RfheiE=", "__NEXT_PREVIEW_MODE_ID": "0ce99eff9202d1d446eae51ac865a599", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ab88dc0d1dd19f8b5229f3028f71e68676e8efe615837fbc534e50b31dbb4190", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "967e7e816e0efa49cab61a7666cac2f28866a33b71ab5c6576eadb1821a446ca"}}}, "sortedMiddleware": ["/"], "functions": {}}