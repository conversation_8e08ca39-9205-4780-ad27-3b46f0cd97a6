(()=>{var e={};e.id=867,e.ids=[867],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26079:(e,t,r)=>{Promise.resolve().then(r.bind(r,92027))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44231:(e,t,r)=>{Promise.resolve().then(r.bind(r,58325))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58325:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/deal registration/deal-registration/src/app/deals/new/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/deal registration/deal-registration/src/app/deals/new/page.tsx","default")},58862:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>p,tree:()=>u});var i=r(65239),n=r(48088),a=r(88170),s=r.n(a),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let u={children:["",{children:["deals",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,58325)),"/home/<USER>/deal registration/deal-registration/src/app/deals/new/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/home/<USER>/deal registration/deal-registration/src/app/layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"/home/<USER>/deal registration/deal-registration/src/app/error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"/home/<USER>/deal registration/deal-registration/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"/home/<USER>/deal registration/deal-registration/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/home/<USER>/deal registration/deal-registration/src/app/deals/new/page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new i.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/deals/new/page",pathname:"/deals/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88233:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},91645:e=>{"use strict";e.exports=require("net")},92027:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>np});var i=r(60687),n=r(43210),a=r(16189),s=r(7425),o=r(44493),l=r(29523),u=r(89667),d=r(96834),c=e=>"checkbox"===e.type,p=e=>e instanceof Date,f=e=>null==e;let m=e=>"object"==typeof e;var h=e=>!f(e)&&!Array.isArray(e)&&m(e)&&!p(e),v=e=>h(e)&&e.target?c(e.target)?e.target.checked:e.target.value:e,y=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,_=(e,t)=>e.has(y(t)),g=e=>{let t=e.constructor&&e.constructor.prototype;return h(t)&&t.hasOwnProperty("isPrototypeOf")},b="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function x(e){let t,r=Array.isArray(e),i="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else if(!(!(b&&(e instanceof Blob||i))&&(r||h(e))))return e;else if(t=r?[]:{},r||g(e))for(let r in e)e.hasOwnProperty(r)&&(t[r]=x(e[r]));else t=e;return t}var w=e=>/^\w*$/.test(e),k=e=>void 0===e,z=e=>Array.isArray(e)?e.filter(Boolean):[],A=e=>z(e.replace(/["|']|\]/g,"").split(/\.|\[/)),j=(e,t,r)=>{if(!t||!h(e))return r;let i=(w(t)?[t]:A(t)).reduce((e,t)=>f(e)?e:e[t],e);return k(i)||i===e?k(e[t])?r:e[t]:i},$=e=>"boolean"==typeof e,S=(e,t,r)=>{let i=-1,n=w(t)?[t]:A(t),a=n.length,s=a-1;for(;++i<a;){let t=n[i],a=r;if(i!==s){let r=e[t];a=h(r)||Array.isArray(r)?r:isNaN(+n[i+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=a,e=e[t]}};let E={BLUR:"blur",FOCUS_OUT:"focusout"},F={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},N={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},V=n.createContext(null);V.displayName="HookFormContext";let Z=()=>n.useContext(V);var P=(e,t,r,i=!0)=>{let n={defaultValues:t._defaultValues};for(let a in e)Object.defineProperty(n,a,{get:()=>(t._proxyFormState[a]!==F.all&&(t._proxyFormState[a]=!i||F.all),r&&(r[a]=!0),e[a])});return n};let I="undefined"!=typeof window?n.useLayoutEffect:n.useEffect;var O=e=>"string"==typeof e,T=(e,t,r,i,n)=>O(e)?(i&&t.watch.add(e),j(r,e,n)):Array.isArray(e)?e.map(e=>(i&&t.watch.add(e),j(r,e))):(i&&(t.watchAll=!0),r),C=(e,t,r,i,n)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[i]:n||!0}}:{},D=e=>Array.isArray(e)?e:[e],U=()=>{let e=[];return{get observers(){return e},next:t=>{for(let r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter(e=>e!==t)}}),unsubscribe:()=>{e=[]}}},R=e=>f(e)||!m(e);function M(e,t,r=new WeakSet){if(R(e)||R(t))return e===t;if(p(e)&&p(t))return e.getTime()===t.getTime();let i=Object.keys(e),n=Object.keys(t);if(i.length!==n.length)return!1;if(r.has(e)||r.has(t))return!0;for(let a of(r.add(e),r.add(t),i)){let i=e[a];if(!n.includes(a))return!1;if("ref"!==a){let e=t[a];if(p(i)&&p(e)||h(i)&&h(e)||Array.isArray(i)&&Array.isArray(e)?!M(i,e,r):i!==e)return!1}}return!0}var q=e=>h(e)&&!Object.keys(e).length,L=e=>"file"===e.type,B=e=>"function"==typeof e,W=e=>{if(!b)return!1;let t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},G=e=>"select-multiple"===e.type,J=e=>"radio"===e.type,K=e=>J(e)||c(e),Y=e=>W(e)&&e.isConnected;function H(e,t){let r=Array.isArray(t)?t:w(t)?[t]:A(t),i=1===r.length?e:function(e,t){let r=t.slice(0,-1).length,i=0;for(;i<r;)e=k(e)?i++:e[t[i++]];return e}(e,r),n=r.length-1,a=r[n];return i&&delete i[a],0!==n&&(h(i)&&q(i)||Array.isArray(i)&&function(e){for(let t in e)if(e.hasOwnProperty(t)&&!k(e[t]))return!1;return!0}(i))&&H(e,r.slice(0,-1)),e}var X=e=>{for(let t in e)if(B(e[t]))return!0;return!1};function Q(e,t={}){let r=Array.isArray(e);if(h(e)||r)for(let r in e)Array.isArray(e[r])||h(e[r])&&!X(e[r])?(t[r]=Array.isArray(e[r])?[]:{},Q(e[r],t[r])):f(e[r])||(t[r]=!0);return t}var ee=(e,t)=>(function e(t,r,i){let n=Array.isArray(t);if(h(t)||n)for(let n in t)Array.isArray(t[n])||h(t[n])&&!X(t[n])?k(r)||R(i[n])?i[n]=Array.isArray(t[n])?Q(t[n],[]):{...Q(t[n])}:e(t[n],f(r)?{}:r[n],i[n]):i[n]=!M(t[n],r[n]);return i})(e,t,Q(t));let et={value:!1,isValid:!1},er={value:!0,isValid:!0};var ei=e=>{if(Array.isArray(e)){if(e.length>1){let t=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!k(e[0].attributes.value)?k(e[0].value)||""===e[0].value?er:{value:e[0].value,isValid:!0}:er:et}return et},en=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:i})=>k(e)?e:t?""===e?NaN:e?+e:e:r&&O(e)?new Date(e):i?i(e):e;let ea={isValid:!1,value:null};var es=e=>Array.isArray(e)?e.reduce((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e,ea):ea;function eo(e){let t=e.ref;return L(t)?t.files:J(t)?es(e.refs).value:G(t)?[...t.selectedOptions].map(({value:e})=>e):c(t)?ei(e.refs).value:en(k(t.value)?e.ref.value:t.value,e)}var el=(e,t,r,i)=>{let n={};for(let r of e){let e=j(t,r);e&&S(n,r,e._f)}return{criteriaMode:r,names:[...e],fields:n,shouldUseNativeValidation:i}},eu=e=>e instanceof RegExp,ed=e=>k(e)?e:eu(e)?e.source:h(e)?eu(e.value)?e.value.source:e.value:e,ec=e=>({isOnSubmit:!e||e===F.onSubmit,isOnBlur:e===F.onBlur,isOnChange:e===F.onChange,isOnAll:e===F.all,isOnTouch:e===F.onTouched});let ep="AsyncFunction";var ef=e=>!!e&&!!e.validate&&!!(B(e.validate)&&e.validate.constructor.name===ep||h(e.validate)&&Object.values(e.validate).find(e=>e.constructor.name===ep)),em=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),eh=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length))));let ev=(e,t,r,i)=>{for(let n of r||Object.keys(e)){let r=j(e,n);if(r){let{_f:e,...a}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],n)&&!i)return!0;else if(e.ref&&t(e.ref,e.name)&&!i)return!0;else if(ev(a,t))break}else if(h(a)&&ev(a,t))break}}};function ey(e,t,r){let i=j(e,r);if(i||w(r))return{error:i,name:r};let n=r.split(".");for(;n.length;){let i=n.join("."),a=j(t,i),s=j(e,i);if(a&&!Array.isArray(a)&&r!==i)break;if(s&&s.type)return{name:i,error:s};if(s&&s.root&&s.root.type)return{name:`${i}.root`,error:s.root};n.pop()}return{name:r}}var e_=(e,t,r,i)=>{r(e);let{name:n,...a}=e;return q(a)||Object.keys(a).length>=Object.keys(t).length||Object.keys(a).find(e=>t[e]===(!i||F.all))},eg=(e,t,r)=>!e||!t||e===t||D(e).some(e=>e&&(r?e===t:e.startsWith(t)||t.startsWith(e))),eb=(e,t,r,i,n)=>!n.isOnAll&&(!r&&n.isOnTouch?!(t||e):(r?i.isOnBlur:n.isOnBlur)?!e:(r?!i.isOnChange:!n.isOnChange)||e),ex=(e,t)=>!z(j(e,t)).length&&H(e,t),ew=(e,t,r)=>{let i=D(j(e,r));return S(i,"root",t[r]),S(e,r,i),e},ek=e=>O(e);function ez(e,t,r="validate"){if(ek(e)||Array.isArray(e)&&e.every(ek)||$(e)&&!e)return{type:r,message:ek(e)?e:"",ref:t}}var eA=e=>h(e)&&!eu(e)?e:{value:e,message:""},ej=async(e,t,r,i,n,a)=>{let{ref:s,refs:o,required:l,maxLength:u,minLength:d,min:p,max:m,pattern:v,validate:y,name:_,valueAsNumber:g,mount:b}=e._f,x=j(r,_);if(!b||t.has(_))return{};let w=o?o[0]:s,z=e=>{n&&w.reportValidity&&(w.setCustomValidity($(e)?"":e||""),w.reportValidity())},A={},S=J(s),E=c(s),F=(g||L(s))&&k(s.value)&&k(x)||W(s)&&""===s.value||""===x||Array.isArray(x)&&!x.length,V=C.bind(null,_,i,A),Z=(e,t,r,i=N.maxLength,n=N.minLength)=>{let a=e?t:r;A[_]={type:e?i:n,message:a,ref:s,...V(e?i:n,a)}};if(a?!Array.isArray(x)||!x.length:l&&(!(S||E)&&(F||f(x))||$(x)&&!x||E&&!ei(o).isValid||S&&!es(o).isValid)){let{value:e,message:t}=ek(l)?{value:!!l,message:l}:eA(l);if(e&&(A[_]={type:N.required,message:t,ref:w,...V(N.required,t)},!i))return z(t),A}if(!F&&(!f(p)||!f(m))){let e,t,r=eA(m),n=eA(p);if(f(x)||isNaN(x)){let i=s.valueAsDate||new Date(x),a=e=>new Date(new Date().toDateString()+" "+e),o="time"==s.type,l="week"==s.type;O(r.value)&&x&&(e=o?a(x)>a(r.value):l?x>r.value:i>new Date(r.value)),O(n.value)&&x&&(t=o?a(x)<a(n.value):l?x<n.value:i<new Date(n.value))}else{let i=s.valueAsNumber||(x?+x:x);f(r.value)||(e=i>r.value),f(n.value)||(t=i<n.value)}if((e||t)&&(Z(!!e,r.message,n.message,N.max,N.min),!i))return z(A[_].message),A}if((u||d)&&!F&&(O(x)||a&&Array.isArray(x))){let e=eA(u),t=eA(d),r=!f(e.value)&&x.length>+e.value,n=!f(t.value)&&x.length<+t.value;if((r||n)&&(Z(r,e.message,t.message),!i))return z(A[_].message),A}if(v&&!F&&O(x)){let{value:e,message:t}=eA(v);if(eu(e)&&!x.match(e)&&(A[_]={type:N.pattern,message:t,ref:s,...V(N.pattern,t)},!i))return z(t),A}if(y){if(B(y)){let e=ez(await y(x,r),w);if(e&&(A[_]={...e,...V(N.validate,e.message)},!i))return z(e.message),A}else if(h(y)){let e={};for(let t in y){if(!q(e)&&!i)break;let n=ez(await y[t](x,r),w,t);n&&(e={...n,...V(t,n.message)},z(n.message),i&&(A[_]=e))}if(!q(e)&&(A[_]={ref:w,...e},!i))return A}}return z(!0),A};let e$={mode:F.onSubmit,reValidateMode:F.onChange,shouldFocusError:!0};var eS=()=>{if("undefined"!=typeof crypto&&crypto.randomUUID)return crypto.randomUUID();let e="undefined"==typeof performance?Date.now():1e3*performance.now();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,t=>{let r=(16*Math.random()+e)%16|0;return("x"==t?r:3&r|8).toString(16)})},eE=(e,t,r={})=>r.shouldFocus||k(r.shouldFocus)?r.focusName||`${e}.${k(r.focusIndex)?t:r.focusIndex}.`:"",eF=(e,t)=>[...e,...D(t)],eN=e=>Array.isArray(e)?e.map(()=>void 0):void 0;function eV(e,t,r){return[...e.slice(0,t),...D(r),...e.slice(t)]}var eZ=(e,t,r)=>Array.isArray(e)?(k(e[r])&&(e[r]=void 0),e.splice(r,0,e.splice(t,1)[0]),e):[],eP=(e,t)=>[...D(t),...D(e)],eI=(e,t)=>k(t)?[]:function(e,t){let r=0,i=[...e];for(let e of t)i.splice(e-r,1),r++;return z(i).length?i:[]}(e,D(t).sort((e,t)=>e-t)),eO=(e,t,r)=>{[e[t],e[r]]=[e[r],e[t]]},eT=(e,t,r)=>(e[t]=r,e);let eC=(e,t,r)=>{if(e&&"reportValidity"in e){let i=j(r,t);e.setCustomValidity(i&&i.message||""),e.reportValidity()}},eD=(e,t)=>{for(let r in t.fields){let i=t.fields[r];i&&i.ref&&"reportValidity"in i.ref?eC(i.ref,r,e):i&&i.refs&&i.refs.forEach(t=>eC(t,r,e))}},eU=(e,t)=>{t.shouldUseNativeValidation&&eD(e,t);let r={};for(let i in e){let n=j(t.fields,i),a=Object.assign(e[i]||{},{ref:n&&n.ref});if(eR(t.names||Object.keys(e),i)){let e=Object.assign({},j(r,i));S(e,"root",a),S(r,i,e)}else S(r,i,a)}return r},eR=(e,t)=>{let r=eM(t);return e.some(e=>eM(e).match(`^${r}\\.\\d+`))};function eM(e){return e.replace(/\]|\[/g,"")}function eq(e,t,r){function i(r,i){var n;for(let a in Object.defineProperty(r,"_zod",{value:r._zod??{},enumerable:!1}),(n=r._zod).traits??(n.traits=new Set),r._zod.traits.add(e),t(r,i),s.prototype)a in r||Object.defineProperty(r,a,{value:s.prototype[a].bind(r)});r._zod.constr=s,r._zod.def=i}let n=r?.Parent??Object;class a extends n{}function s(e){var t;let n=r?.Parent?new a:this;for(let r of(i(n,e),(t=n._zod).deferred??(t.deferred=[]),n._zod.deferred))r();return n}return Object.defineProperty(a,"name",{value:e}),Object.defineProperty(s,"init",{value:i}),Object.defineProperty(s,Symbol.hasInstance,{value:t=>!!r?.Parent&&t instanceof r.Parent||t?._zod?.traits?.has(e)}),Object.defineProperty(s,"name",{value:e}),s}Object.freeze({status:"aborted"}),Symbol("zod_brand");class eL extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}let eB={};function eW(e){return e&&Object.assign(eB,e),eB}function eG(e,t){return"bigint"==typeof t?t.toString():t}function eJ(e){return{get value(){{let t=e();return Object.defineProperty(this,"value",{value:t}),t}}}}function eK(e){let t=+!!e.startsWith("^"),r=e.endsWith("$")?e.length-1:e.length;return e.slice(t,r)}function eY(e,t,r){Object.defineProperty(e,t,{get(){{let i=r();return e[t]=i,i}},set(r){Object.defineProperty(e,t,{value:r})},configurable:!0})}function eH(e,t,r){Object.defineProperty(e,t,{value:r,writable:!0,enumerable:!0,configurable:!0})}function eX(e){return JSON.stringify(e)}let eQ=Error.captureStackTrace?Error.captureStackTrace:(...e)=>{};function e0(e){return"object"==typeof e&&null!==e&&!Array.isArray(e)}let e1=eJ(()=>{if("undefined"!=typeof navigator&&navigator?.userAgent?.includes("Cloudflare"))return!1;try{return Function(""),!0}catch(e){return!1}});function e4(e){if(!1===e0(e))return!1;let t=e.constructor;if(void 0===t)return!0;let r=t.prototype;return!1!==e0(r)&&!1!==Object.prototype.hasOwnProperty.call(r,"isPrototypeOf")}let e2=new Set(["string","number","symbol"]);function e9(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function e6(e,t,r){let i=new e._zod.constr(t??e._zod.def);return(!t||r?.parent)&&(i._zod.parent=e),i}function e3(e){if(!e)return{};if("string"==typeof e)return{error:()=>e};if(e?.message!==void 0){if(e?.error!==void 0)throw Error("Cannot specify both `message` and `error` params");e.error=e.message}return(delete e.message,"string"==typeof e.error)?{...e,error:()=>e.error}:e}let e8={safeint:[Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],int32:[-0x80000000,0x7fffffff],uint32:[0,0xffffffff],float32:[-34028234663852886e22,34028234663852886e22],float64:[-Number.MAX_VALUE,Number.MAX_VALUE]};function e5(e,t=0){for(let r=t;r<e.issues.length;r++)if(e.issues[r]?.continue!==!0)return!0;return!1}function e7(e,t){return t.map(t=>(t.path??(t.path=[]),t.path.unshift(e),t))}function te(e){return"string"==typeof e?e:e?.message}function tt(e,t,r){let i={...e,path:e.path??[]};return e.message||(i.message=te(e.inst?._zod.def?.error?.(e))??te(t?.error?.(e))??te(r.customError?.(e))??te(r.localeError?.(e))??"Invalid input"),delete i.inst,delete i.continue,t?.reportInput||delete i.input,i}function tr(e){return Array.isArray(e)?"array":"string"==typeof e?"string":"unknown"}function ti(...e){let[t,r,i]=e;return"string"==typeof t?{message:t,code:"custom",input:r,inst:i}:{...t}}let tn=(e,t)=>{e.name="$ZodError",Object.defineProperty(e,"_zod",{value:e._zod,enumerable:!1}),Object.defineProperty(e,"issues",{value:t,enumerable:!1}),Object.defineProperty(e,"message",{get:()=>JSON.stringify(t,eG,2),enumerable:!0}),Object.defineProperty(e,"toString",{value:()=>e.message,enumerable:!1})},ta=eq("$ZodError",tn),ts=eq("$ZodError",tn,{Parent:Error}),to=e=>(t,r,i,n)=>{let a=i?Object.assign(i,{async:!1}):{async:!1},s=t._zod.run({value:r,issues:[]},a);if(s instanceof Promise)throw new eL;if(s.issues.length){let t=new(n?.Err??e)(s.issues.map(e=>tt(e,a,eW())));throw eQ(t,n?.callee),t}return s.value},tl=to(ts),tu=e=>async(t,r,i,n)=>{let a=i?Object.assign(i,{async:!0}):{async:!0},s=t._zod.run({value:r,issues:[]},a);if(s instanceof Promise&&(s=await s),s.issues.length){let t=new(n?.Err??e)(s.issues.map(e=>tt(e,a,eW())));throw eQ(t,n?.callee),t}return s.value},td=tu(ts),tc=e=>(t,r,i)=>{let n=i?{...i,async:!1}:{async:!1},a=t._zod.run({value:r,issues:[]},n);if(a instanceof Promise)throw new eL;return a.issues.length?{success:!1,error:new(e??ta)(a.issues.map(e=>tt(e,n,eW())))}:{success:!0,data:a.value}},tp=tc(ts),tf=e=>async(t,r,i)=>{let n=i?Object.assign(i,{async:!0}):{async:!0},a=t._zod.run({value:r,issues:[]},n);return a instanceof Promise&&(a=await a),a.issues.length?{success:!1,error:new e(a.issues.map(e=>tt(e,n,eW())))}:{success:!0,data:a.value}},tm=tf(ts);function th(e,t){try{var r=e()}catch(e){return t(e)}return r&&r.then?r.then(void 0,t):r}let tv=/^[cC][^\s-]{8,}$/,ty=/^[0-9a-z]+$/,t_=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,tg=/^[0-9a-vA-V]{20}$/,tb=/^[A-Za-z0-9]{27}$/,tx=/^[a-zA-Z0-9_-]{21}$/,tw=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,tk=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,tz=e=>e?RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${e}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$/,tA=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,tj=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,t$=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/,tS=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,tE=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,tF=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,tN=/^[A-Za-z0-9_-]*$/,tV=/^([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+$/,tZ=/^\+(?:[0-9]){6,14}[0-9]$/,tP="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",tI=RegExp(`^${tP}$`);function tO(e){let t="(?:[01]\\d|2[0-3]):[0-5]\\d";return"number"==typeof e.precision?-1===e.precision?`${t}`:0===e.precision?`${t}:[0-5]\\d`:`${t}:[0-5]\\d\\.\\d{${e.precision}}`:`${t}(?::[0-5]\\d(?:\\.\\d+)?)?`}let tT=e=>{let t=e?`[\\s\\S]{${e?.minimum??0},${e?.maximum??""}}`:"[\\s\\S]*";return RegExp(`^${t}$`)},tC=/^\d+$/,tD=/^-?\d+(?:\.\d+)?/i,tU=/^[^A-Z]*$/,tR=/^[^a-z]*$/,tM=eq("$ZodCheck",(e,t)=>{var r;e._zod??(e._zod={}),e._zod.def=t,(r=e._zod).onattach??(r.onattach=[])}),tq={number:"number",bigint:"bigint",object:"date"},tL=eq("$ZodCheckLessThan",(e,t)=>{tM.init(e,t);let r=tq[typeof t.value];e._zod.onattach.push(e=>{let r=e._zod.bag,i=(t.inclusive?r.maximum:r.exclusiveMaximum)??Number.POSITIVE_INFINITY;t.value<i&&(t.inclusive?r.maximum=t.value:r.exclusiveMaximum=t.value)}),e._zod.check=i=>{(t.inclusive?i.value<=t.value:i.value<t.value)||i.issues.push({origin:r,code:"too_big",maximum:t.value,input:i.value,inclusive:t.inclusive,inst:e,continue:!t.abort})}}),tB=eq("$ZodCheckGreaterThan",(e,t)=>{tM.init(e,t);let r=tq[typeof t.value];e._zod.onattach.push(e=>{let r=e._zod.bag,i=(t.inclusive?r.minimum:r.exclusiveMinimum)??Number.NEGATIVE_INFINITY;t.value>i&&(t.inclusive?r.minimum=t.value:r.exclusiveMinimum=t.value)}),e._zod.check=i=>{(t.inclusive?i.value>=t.value:i.value>t.value)||i.issues.push({origin:r,code:"too_small",minimum:t.value,input:i.value,inclusive:t.inclusive,inst:e,continue:!t.abort})}}),tW=eq("$ZodCheckMultipleOf",(e,t)=>{tM.init(e,t),e._zod.onattach.push(e=>{var r;(r=e._zod.bag).multipleOf??(r.multipleOf=t.value)}),e._zod.check=r=>{if(typeof r.value!=typeof t.value)throw Error("Cannot mix number and bigint in multiple_of check.");("bigint"==typeof r.value?r.value%t.value===BigInt(0):0===function(e,t){let r=(e.toString().split(".")[1]||"").length,i=(t.toString().split(".")[1]||"").length,n=r>i?r:i;return Number.parseInt(e.toFixed(n).replace(".",""))%Number.parseInt(t.toFixed(n).replace(".",""))/10**n}(r.value,t.value))||r.issues.push({origin:typeof r.value,code:"not_multiple_of",divisor:t.value,input:r.value,inst:e,continue:!t.abort})}}),tG=eq("$ZodCheckNumberFormat",(e,t)=>{tM.init(e,t),t.format=t.format||"float64";let r=t.format?.includes("int"),i=r?"int":"number",[n,a]=e8[t.format];e._zod.onattach.push(e=>{let i=e._zod.bag;i.format=t.format,i.minimum=n,i.maximum=a,r&&(i.pattern=tC)}),e._zod.check=s=>{let o=s.value;if(r){if(!Number.isInteger(o))return void s.issues.push({expected:i,format:t.format,code:"invalid_type",input:o,inst:e});if(!Number.isSafeInteger(o))return void(o>0?s.issues.push({input:o,code:"too_big",maximum:Number.MAX_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:e,origin:i,continue:!t.abort}):s.issues.push({input:o,code:"too_small",minimum:Number.MIN_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:e,origin:i,continue:!t.abort}))}o<n&&s.issues.push({origin:"number",input:o,code:"too_small",minimum:n,inclusive:!0,inst:e,continue:!t.abort}),o>a&&s.issues.push({origin:"number",input:o,code:"too_big",maximum:a,inst:e})}}),tJ=eq("$ZodCheckMaxLength",(e,t)=>{var r;tM.init(e,t),(r=e._zod.def).when??(r.when=e=>{let t=e.value;return null!=t&&void 0!==t.length}),e._zod.onattach.push(e=>{let r=e._zod.bag.maximum??Number.POSITIVE_INFINITY;t.maximum<r&&(e._zod.bag.maximum=t.maximum)}),e._zod.check=r=>{let i=r.value;if(i.length<=t.maximum)return;let n=tr(i);r.issues.push({origin:n,code:"too_big",maximum:t.maximum,inclusive:!0,input:i,inst:e,continue:!t.abort})}}),tK=eq("$ZodCheckMinLength",(e,t)=>{var r;tM.init(e,t),(r=e._zod.def).when??(r.when=e=>{let t=e.value;return null!=t&&void 0!==t.length}),e._zod.onattach.push(e=>{let r=e._zod.bag.minimum??Number.NEGATIVE_INFINITY;t.minimum>r&&(e._zod.bag.minimum=t.minimum)}),e._zod.check=r=>{let i=r.value;if(i.length>=t.minimum)return;let n=tr(i);r.issues.push({origin:n,code:"too_small",minimum:t.minimum,inclusive:!0,input:i,inst:e,continue:!t.abort})}}),tY=eq("$ZodCheckLengthEquals",(e,t)=>{var r;tM.init(e,t),(r=e._zod.def).when??(r.when=e=>{let t=e.value;return null!=t&&void 0!==t.length}),e._zod.onattach.push(e=>{let r=e._zod.bag;r.minimum=t.length,r.maximum=t.length,r.length=t.length}),e._zod.check=r=>{let i=r.value,n=i.length;if(n===t.length)return;let a=tr(i),s=n>t.length;r.issues.push({origin:a,...s?{code:"too_big",maximum:t.length}:{code:"too_small",minimum:t.length},inclusive:!0,exact:!0,input:r.value,inst:e,continue:!t.abort})}}),tH=eq("$ZodCheckStringFormat",(e,t)=>{var r,i;tM.init(e,t),e._zod.onattach.push(e=>{let r=e._zod.bag;r.format=t.format,t.pattern&&(r.patterns??(r.patterns=new Set),r.patterns.add(t.pattern))}),t.pattern?(r=e._zod).check??(r.check=r=>{t.pattern.lastIndex=0,t.pattern.test(r.value)||r.issues.push({origin:"string",code:"invalid_format",format:t.format,input:r.value,...t.pattern?{pattern:t.pattern.toString()}:{},inst:e,continue:!t.abort})}):(i=e._zod).check??(i.check=()=>{})}),tX=eq("$ZodCheckRegex",(e,t)=>{tH.init(e,t),e._zod.check=r=>{t.pattern.lastIndex=0,t.pattern.test(r.value)||r.issues.push({origin:"string",code:"invalid_format",format:"regex",input:r.value,pattern:t.pattern.toString(),inst:e,continue:!t.abort})}}),tQ=eq("$ZodCheckLowerCase",(e,t)=>{t.pattern??(t.pattern=tU),tH.init(e,t)}),t0=eq("$ZodCheckUpperCase",(e,t)=>{t.pattern??(t.pattern=tR),tH.init(e,t)}),t1=eq("$ZodCheckIncludes",(e,t)=>{tM.init(e,t);let r=e9(t.includes),i=new RegExp("number"==typeof t.position?`^.{${t.position}}${r}`:r);t.pattern=i,e._zod.onattach.push(e=>{let t=e._zod.bag;t.patterns??(t.patterns=new Set),t.patterns.add(i)}),e._zod.check=r=>{r.value.includes(t.includes,t.position)||r.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:t.includes,input:r.value,inst:e,continue:!t.abort})}}),t4=eq("$ZodCheckStartsWith",(e,t)=>{tM.init(e,t);let r=RegExp(`^${e9(t.prefix)}.*`);t.pattern??(t.pattern=r),e._zod.onattach.push(e=>{let t=e._zod.bag;t.patterns??(t.patterns=new Set),t.patterns.add(r)}),e._zod.check=r=>{r.value.startsWith(t.prefix)||r.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:t.prefix,input:r.value,inst:e,continue:!t.abort})}}),t2=eq("$ZodCheckEndsWith",(e,t)=>{tM.init(e,t);let r=RegExp(`.*${e9(t.suffix)}$`);t.pattern??(t.pattern=r),e._zod.onattach.push(e=>{let t=e._zod.bag;t.patterns??(t.patterns=new Set),t.patterns.add(r)}),e._zod.check=r=>{r.value.endsWith(t.suffix)||r.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:t.suffix,input:r.value,inst:e,continue:!t.abort})}}),t9=eq("$ZodCheckOverwrite",(e,t)=>{tM.init(e,t),e._zod.check=e=>{e.value=t.tx(e.value)}});class t6{constructor(e=[]){this.content=[],this.indent=0,this&&(this.args=e)}indented(e){this.indent+=1,e(this),this.indent-=1}write(e){if("function"==typeof e){e(this,{execution:"sync"}),e(this,{execution:"async"});return}let t=e.split("\n").filter(e=>e),r=Math.min(...t.map(e=>e.length-e.trimStart().length));for(let e of t.map(e=>e.slice(r)).map(e=>" ".repeat(2*this.indent)+e))this.content.push(e)}compile(){return Function(...this?.args,[...(this?.content??[""]).map(e=>`  ${e}`)].join("\n"))}}let t3={major:4,minor:0,patch:5},t8=eq("$ZodType",(e,t)=>{var r;e??(e={}),e._zod.def=t,e._zod.bag=e._zod.bag||{},e._zod.version=t3;let i=[...e._zod.def.checks??[]];for(let t of(e._zod.traits.has("$ZodCheck")&&i.unshift(e),i))for(let r of t._zod.onattach)r(e);if(0===i.length)(r=e._zod).deferred??(r.deferred=[]),e._zod.deferred?.push(()=>{e._zod.run=e._zod.parse});else{let t=(e,t,r)=>{let i,n=e5(e);for(let a of t){if(a._zod.def.when){if(!a._zod.def.when(e))continue}else if(n)continue;let t=e.issues.length,s=a._zod.check(e);if(s instanceof Promise&&r?.async===!1)throw new eL;if(i||s instanceof Promise)i=(i??Promise.resolve()).then(async()=>{await s,e.issues.length!==t&&(n||(n=e5(e,t)))});else{if(e.issues.length===t)continue;n||(n=e5(e,t))}}return i?i.then(()=>e):e};e._zod.run=(r,n)=>{let a=e._zod.parse(r,n);if(a instanceof Promise){if(!1===n.async)throw new eL;return a.then(e=>t(e,i,n))}return t(a,i,n)}}e["~standard"]={validate:t=>{try{let r=tp(e,t);return r.success?{value:r.data}:{issues:r.error?.issues}}catch(r){return tm(e,t).then(e=>e.success?{value:e.data}:{issues:e.error?.issues})}},vendor:"zod",version:1}}),t5=eq("$ZodString",(e,t)=>{t8.init(e,t),e._zod.pattern=[...e?._zod.bag?.patterns??[]].pop()??tT(e._zod.bag),e._zod.parse=(r,i)=>{if(t.coerce)try{r.value=String(r.value)}catch(e){}return"string"==typeof r.value||r.issues.push({expected:"string",code:"invalid_type",input:r.value,inst:e}),r}}),t7=eq("$ZodStringFormat",(e,t)=>{tH.init(e,t),t5.init(e,t)}),re=eq("$ZodGUID",(e,t)=>{t.pattern??(t.pattern=tk),t7.init(e,t)}),rt=eq("$ZodUUID",(e,t)=>{if(t.version){let e={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[t.version];if(void 0===e)throw Error(`Invalid UUID version: "${t.version}"`);t.pattern??(t.pattern=tz(e))}else t.pattern??(t.pattern=tz());t7.init(e,t)}),rr=eq("$ZodEmail",(e,t)=>{t.pattern??(t.pattern=tA),t7.init(e,t)}),ri=eq("$ZodURL",(e,t)=>{t7.init(e,t),e._zod.check=r=>{try{let i=r.value,n=new URL(i),a=n.href;t.hostname&&(t.hostname.lastIndex=0,t.hostname.test(n.hostname)||r.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:tV.source,input:r.value,inst:e,continue:!t.abort})),t.protocol&&(t.protocol.lastIndex=0,t.protocol.test(n.protocol.endsWith(":")?n.protocol.slice(0,-1):n.protocol)||r.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:t.protocol.source,input:r.value,inst:e,continue:!t.abort})),!i.endsWith("/")&&a.endsWith("/")?r.value=a.slice(0,-1):r.value=a;return}catch(i){r.issues.push({code:"invalid_format",format:"url",input:r.value,inst:e,continue:!t.abort})}}}),rn=eq("$ZodEmoji",(e,t)=>{t.pattern??(t.pattern=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),t7.init(e,t)}),ra=eq("$ZodNanoID",(e,t)=>{t.pattern??(t.pattern=tx),t7.init(e,t)}),rs=eq("$ZodCUID",(e,t)=>{t.pattern??(t.pattern=tv),t7.init(e,t)}),ro=eq("$ZodCUID2",(e,t)=>{t.pattern??(t.pattern=ty),t7.init(e,t)}),rl=eq("$ZodULID",(e,t)=>{t.pattern??(t.pattern=t_),t7.init(e,t)}),ru=eq("$ZodXID",(e,t)=>{t.pattern??(t.pattern=tg),t7.init(e,t)}),rd=eq("$ZodKSUID",(e,t)=>{t.pattern??(t.pattern=tb),t7.init(e,t)}),rc=eq("$ZodISODateTime",(e,t)=>{t.pattern??(t.pattern=function(e){let t=tO({precision:e.precision}),r=["Z"];e.local&&r.push(""),e.offset&&r.push("([+-]\\d{2}:\\d{2})");let i=`${t}(?:${r.join("|")})`;return RegExp(`^${tP}T(?:${i})$`)}(t)),t7.init(e,t)}),rp=eq("$ZodISODate",(e,t)=>{t.pattern??(t.pattern=tI),t7.init(e,t)}),rf=eq("$ZodISOTime",(e,t)=>{t.pattern??(t.pattern=RegExp(`^${tO(t)}$`)),t7.init(e,t)}),rm=eq("$ZodISODuration",(e,t)=>{t.pattern??(t.pattern=tw),t7.init(e,t)}),rh=eq("$ZodIPv4",(e,t)=>{t.pattern??(t.pattern=tj),t7.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.format="ipv4"})}),rv=eq("$ZodIPv6",(e,t)=>{t.pattern??(t.pattern=t$),t7.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.format="ipv6"}),e._zod.check=r=>{try{new URL(`http://[${r.value}]`)}catch{r.issues.push({code:"invalid_format",format:"ipv6",input:r.value,inst:e,continue:!t.abort})}}}),ry=eq("$ZodCIDRv4",(e,t)=>{t.pattern??(t.pattern=tS),t7.init(e,t)}),r_=eq("$ZodCIDRv6",(e,t)=>{t.pattern??(t.pattern=tE),t7.init(e,t),e._zod.check=r=>{let[i,n]=r.value.split("/");try{if(!n)throw Error();let e=Number(n);if(`${e}`!==n||e<0||e>128)throw Error();new URL(`http://[${i}]`)}catch{r.issues.push({code:"invalid_format",format:"cidrv6",input:r.value,inst:e,continue:!t.abort})}}});function rg(e){if(""===e)return!0;if(e.length%4!=0)return!1;try{return atob(e),!0}catch{return!1}}let rb=eq("$ZodBase64",(e,t)=>{t.pattern??(t.pattern=tF),t7.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.contentEncoding="base64"}),e._zod.check=r=>{rg(r.value)||r.issues.push({code:"invalid_format",format:"base64",input:r.value,inst:e,continue:!t.abort})}}),rx=eq("$ZodBase64URL",(e,t)=>{t.pattern??(t.pattern=tN),t7.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.contentEncoding="base64url"}),e._zod.check=r=>{!function(e){if(!tN.test(e))return!1;let t=e.replace(/[-_]/g,e=>"-"===e?"+":"/");return rg(t.padEnd(4*Math.ceil(t.length/4),"="))}(r.value)&&r.issues.push({code:"invalid_format",format:"base64url",input:r.value,inst:e,continue:!t.abort})}}),rw=eq("$ZodE164",(e,t)=>{t.pattern??(t.pattern=tZ),t7.init(e,t)}),rk=eq("$ZodJWT",(e,t)=>{t7.init(e,t),e._zod.check=r=>{!function(e,t=null){try{let r=e.split(".");if(3!==r.length)return!1;let[i]=r;if(!i)return!1;let n=JSON.parse(atob(i));if("typ"in n&&n?.typ!=="JWT"||!n.alg||t&&(!("alg"in n)||n.alg!==t))return!1;return!0}catch{return!1}}(r.value,t.alg)&&r.issues.push({code:"invalid_format",format:"jwt",input:r.value,inst:e,continue:!t.abort})}}),rz=eq("$ZodNumber",(e,t)=>{t8.init(e,t),e._zod.pattern=e._zod.bag.pattern??tD,e._zod.parse=(r,i)=>{if(t.coerce)try{r.value=Number(r.value)}catch(e){}let n=r.value;if("number"==typeof n&&!Number.isNaN(n)&&Number.isFinite(n))return r;let a="number"==typeof n?Number.isNaN(n)?"NaN":Number.isFinite(n)?void 0:"Infinity":void 0;return r.issues.push({expected:"number",code:"invalid_type",input:n,inst:e,...a?{received:a}:{}}),r}}),rA=eq("$ZodNumber",(e,t)=>{tG.init(e,t),rz.init(e,t)}),rj=eq("$ZodUnknown",(e,t)=>{t8.init(e,t),e._zod.parse=e=>e}),r$=eq("$ZodNever",(e,t)=>{t8.init(e,t),e._zod.parse=(t,r)=>(t.issues.push({expected:"never",code:"invalid_type",input:t.value,inst:e}),t)});function rS(e,t,r){e.issues.length&&t.issues.push(...e7(r,e.issues)),t.value[r]=e.value}let rE=eq("$ZodArray",(e,t)=>{t8.init(e,t),e._zod.parse=(r,i)=>{let n=r.value;if(!Array.isArray(n))return r.issues.push({expected:"array",code:"invalid_type",input:n,inst:e}),r;r.value=Array(n.length);let a=[];for(let e=0;e<n.length;e++){let s=n[e],o=t.element._zod.run({value:s,issues:[]},i);o instanceof Promise?a.push(o.then(t=>rS(t,r,e))):rS(o,r,e)}return a.length?Promise.all(a).then(()=>r):r}});function rF(e,t,r){e.issues.length&&t.issues.push(...e7(r,e.issues)),t.value[r]=e.value}function rN(e,t,r,i){e.issues.length?void 0===i[r]?r in i?t.value[r]=void 0:t.value[r]=e.value:t.issues.push(...e7(r,e.issues)):void 0===e.value?r in i&&(t.value[r]=void 0):t.value[r]=e.value}let rV=eq("$ZodObject",(e,t)=>{let r,i;t8.init(e,t);let n=eJ(()=>{let e=Object.keys(t.shape);for(let r of e)if(!(t.shape[r]instanceof t8))throw Error(`Invalid element at key "${r}": expected a Zod schema`);let r=function(e){return Object.keys(e).filter(t=>"optional"===e[t]._zod.optin&&"optional"===e[t]._zod.optout)}(t.shape);return{shape:t.shape,keys:e,keySet:new Set(e),numKeys:e.length,optionalKeys:new Set(r)}});eY(e._zod,"propValues",()=>{let e=t.shape,r={};for(let t in e){let i=e[t]._zod;if(i.values)for(let e of(r[t]??(r[t]=new Set),i.values))r[t].add(e)}return r});let a=e=>{let t=new t6(["shape","payload","ctx"]),r=n.value,i=e=>{let t=eX(e);return`shape[${t}]._zod.run({ value: input[${t}], issues: [] }, ctx)`};t.write("const input = payload.value;");let a=Object.create(null),s=0;for(let e of r.keys)a[e]=`key_${s++}`;for(let e of(t.write("const newResult = {}"),r.keys))if(r.optionalKeys.has(e)){let r=a[e];t.write(`const ${r} = ${i(e)};`);let n=eX(e);t.write(`
        if (${r}.issues.length) {
          if (input[${n}] === undefined) {
            if (${n} in input) {
              newResult[${n}] = undefined;
            }
          } else {
            payload.issues = payload.issues.concat(
              ${r}.issues.map((iss) => ({
                ...iss,
                path: iss.path ? [${n}, ...iss.path] : [${n}],
              }))
            );
          }
        } else if (${r}.value === undefined) {
          if (${n} in input) newResult[${n}] = undefined;
        } else {
          newResult[${n}] = ${r}.value;
        }
        `)}else{let r=a[e];t.write(`const ${r} = ${i(e)};`),t.write(`
          if (${r}.issues.length) payload.issues = payload.issues.concat(${r}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${eX(e)}, ...iss.path] : [${eX(e)}]
          })));`),t.write(`newResult[${eX(e)}] = ${r}.value`)}t.write("payload.value = newResult;"),t.write("return payload;");let o=t.compile();return(t,r)=>o(e,t,r)},s=!eB.jitless,o=s&&e1.value,l=t.catchall;e._zod.parse=(u,d)=>{i??(i=n.value);let c=u.value;if(!e0(c))return u.issues.push({expected:"object",code:"invalid_type",input:c,inst:e}),u;let p=[];if(s&&o&&d?.async===!1&&!0!==d.jitless)r||(r=a(t.shape)),u=r(u,d);else{u.value={};let e=i.shape;for(let t of i.keys){let r=e[t],i=r._zod.run({value:c[t],issues:[]},d),n="optional"===r._zod.optin&&"optional"===r._zod.optout;i instanceof Promise?p.push(i.then(e=>n?rN(e,u,t,c):rF(e,u,t))):n?rN(i,u,t,c):rF(i,u,t)}}if(!l)return p.length?Promise.all(p).then(()=>u):u;let f=[],m=i.keySet,h=l._zod,v=h.def.type;for(let e of Object.keys(c)){if(m.has(e))continue;if("never"===v){f.push(e);continue}let t=h.run({value:c[e],issues:[]},d);t instanceof Promise?p.push(t.then(t=>rF(t,u,e))):rF(t,u,e)}return(f.length&&u.issues.push({code:"unrecognized_keys",keys:f,input:c,inst:e}),p.length)?Promise.all(p).then(()=>u):u}});function rZ(e,t,r,i){for(let r of e)if(0===r.issues.length)return t.value=r.value,t;return t.issues.push({code:"invalid_union",input:t.value,inst:r,errors:e.map(e=>e.issues.map(e=>tt(e,i,eW())))}),t}let rP=eq("$ZodUnion",(e,t)=>{t8.init(e,t),eY(e._zod,"optin",()=>t.options.some(e=>"optional"===e._zod.optin)?"optional":void 0),eY(e._zod,"optout",()=>t.options.some(e=>"optional"===e._zod.optout)?"optional":void 0),eY(e._zod,"values",()=>{if(t.options.every(e=>e._zod.values))return new Set(t.options.flatMap(e=>Array.from(e._zod.values)))}),eY(e._zod,"pattern",()=>{if(t.options.every(e=>e._zod.pattern)){let e=t.options.map(e=>e._zod.pattern);return RegExp(`^(${e.map(e=>eK(e.source)).join("|")})$`)}}),e._zod.parse=(r,i)=>{let n=!1,a=[];for(let e of t.options){let t=e._zod.run({value:r.value,issues:[]},i);if(t instanceof Promise)a.push(t),n=!0;else{if(0===t.issues.length)return t;a.push(t)}}return n?Promise.all(a).then(t=>rZ(t,r,e,i)):rZ(a,r,e,i)}}),rI=eq("$ZodIntersection",(e,t)=>{t8.init(e,t),e._zod.parse=(e,r)=>{let i=e.value,n=t.left._zod.run({value:i,issues:[]},r),a=t.right._zod.run({value:i,issues:[]},r);return n instanceof Promise||a instanceof Promise?Promise.all([n,a]).then(([t,r])=>rO(e,t,r)):rO(e,n,a)}});function rO(e,t,r){if(t.issues.length&&e.issues.push(...t.issues),r.issues.length&&e.issues.push(...r.issues),e5(e))return e;let i=function e(t,r){if(t===r||t instanceof Date&&r instanceof Date&&+t==+r)return{valid:!0,data:t};if(e4(t)&&e4(r)){let i=Object.keys(r),n=Object.keys(t).filter(e=>-1!==i.indexOf(e)),a={...t,...r};for(let i of n){let n=e(t[i],r[i]);if(!n.valid)return{valid:!1,mergeErrorPath:[i,...n.mergeErrorPath]};a[i]=n.data}return{valid:!0,data:a}}if(Array.isArray(t)&&Array.isArray(r)){if(t.length!==r.length)return{valid:!1,mergeErrorPath:[]};let i=[];for(let n=0;n<t.length;n++){let a=e(t[n],r[n]);if(!a.valid)return{valid:!1,mergeErrorPath:[n,...a.mergeErrorPath]};i.push(a.data)}return{valid:!0,data:i}}return{valid:!1,mergeErrorPath:[]}}(t.value,r.value);if(!i.valid)throw Error(`Unmergable intersection. Error path: ${JSON.stringify(i.mergeErrorPath)}`);return e.value=i.data,e}let rT=eq("$ZodEnum",(e,t)=>{t8.init(e,t);let r=function(e){let t=Object.values(e).filter(e=>"number"==typeof e);return Object.entries(e).filter(([e,r])=>-1===t.indexOf(+e)).map(([e,t])=>t)}(t.entries);e._zod.values=new Set(r),e._zod.pattern=RegExp(`^(${r.filter(e=>e2.has(typeof e)).map(e=>"string"==typeof e?e9(e):e.toString()).join("|")})$`),e._zod.parse=(t,i)=>{let n=t.value;return e._zod.values.has(n)||t.issues.push({code:"invalid_value",values:r,input:n,inst:e}),t}}),rC=eq("$ZodTransform",(e,t)=>{t8.init(e,t),e._zod.parse=(e,r)=>{let i=t.transform(e.value,e);if(r.async)return(i instanceof Promise?i:Promise.resolve(i)).then(t=>(e.value=t,e));if(i instanceof Promise)throw new eL;return e.value=i,e}}),rD=eq("$ZodOptional",(e,t)=>{t8.init(e,t),e._zod.optin="optional",e._zod.optout="optional",eY(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,void 0]):void 0),eY(e._zod,"pattern",()=>{let e=t.innerType._zod.pattern;return e?RegExp(`^(${eK(e.source)})?$`):void 0}),e._zod.parse=(e,r)=>"optional"===t.innerType._zod.optin?t.innerType._zod.run(e,r):void 0===e.value?e:t.innerType._zod.run(e,r)}),rU=eq("$ZodNullable",(e,t)=>{t8.init(e,t),eY(e._zod,"optin",()=>t.innerType._zod.optin),eY(e._zod,"optout",()=>t.innerType._zod.optout),eY(e._zod,"pattern",()=>{let e=t.innerType._zod.pattern;return e?RegExp(`^(${eK(e.source)}|null)$`):void 0}),eY(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,null]):void 0),e._zod.parse=(e,r)=>null===e.value?e:t.innerType._zod.run(e,r)}),rR=eq("$ZodDefault",(e,t)=>{t8.init(e,t),e._zod.optin="optional",eY(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,r)=>{if(void 0===e.value)return e.value=t.defaultValue,e;let i=t.innerType._zod.run(e,r);return i instanceof Promise?i.then(e=>rM(e,t)):rM(i,t)}});function rM(e,t){return void 0===e.value&&(e.value=t.defaultValue),e}let rq=eq("$ZodPrefault",(e,t)=>{t8.init(e,t),e._zod.optin="optional",eY(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,r)=>(void 0===e.value&&(e.value=t.defaultValue),t.innerType._zod.run(e,r))}),rL=eq("$ZodNonOptional",(e,t)=>{t8.init(e,t),eY(e._zod,"values",()=>{let e=t.innerType._zod.values;return e?new Set([...e].filter(e=>void 0!==e)):void 0}),e._zod.parse=(r,i)=>{let n=t.innerType._zod.run(r,i);return n instanceof Promise?n.then(t=>rB(t,e)):rB(n,e)}});function rB(e,t){return e.issues.length||void 0!==e.value||e.issues.push({code:"invalid_type",expected:"nonoptional",input:e.value,inst:t}),e}let rW=eq("$ZodCatch",(e,t)=>{t8.init(e,t),e._zod.optin="optional",eY(e._zod,"optout",()=>t.innerType._zod.optout),eY(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,r)=>{let i=t.innerType._zod.run(e,r);return i instanceof Promise?i.then(i=>(e.value=i.value,i.issues.length&&(e.value=t.catchValue({...e,error:{issues:i.issues.map(e=>tt(e,r,eW()))},input:e.value}),e.issues=[]),e)):(e.value=i.value,i.issues.length&&(e.value=t.catchValue({...e,error:{issues:i.issues.map(e=>tt(e,r,eW()))},input:e.value}),e.issues=[]),e)}}),rG=eq("$ZodPipe",(e,t)=>{t8.init(e,t),eY(e._zod,"values",()=>t.in._zod.values),eY(e._zod,"optin",()=>t.in._zod.optin),eY(e._zod,"optout",()=>t.out._zod.optout),eY(e._zod,"propValues",()=>t.in._zod.propValues),e._zod.parse=(e,r)=>{let i=t.in._zod.run(e,r);return i instanceof Promise?i.then(e=>rJ(e,t,r)):rJ(i,t,r)}});function rJ(e,t,r){return e5(e)?e:t.out._zod.run({value:e.value,issues:e.issues},r)}let rK=eq("$ZodReadonly",(e,t)=>{t8.init(e,t),eY(e._zod,"propValues",()=>t.innerType._zod.propValues),eY(e._zod,"values",()=>t.innerType._zod.values),eY(e._zod,"optin",()=>t.innerType._zod.optin),eY(e._zod,"optout",()=>t.innerType._zod.optout),e._zod.parse=(e,r)=>{let i=t.innerType._zod.run(e,r);return i instanceof Promise?i.then(rY):rY(i)}});function rY(e){return e.value=Object.freeze(e.value),e}let rH=eq("$ZodCustom",(e,t)=>{tM.init(e,t),t8.init(e,t),e._zod.parse=(e,t)=>e,e._zod.check=r=>{let i=r.value,n=t.fn(i);if(n instanceof Promise)return n.then(t=>rX(t,r,i,e));rX(n,r,i,e)}});function rX(e,t,r,i){if(!e){let e={code:"custom",input:r,inst:i,path:[...i._zod.def.path??[]],continue:!i._zod.def.abort};i._zod.def.params&&(e.params=i._zod.def.params),t.issues.push(ti(e))}}Symbol("ZodOutput"),Symbol("ZodInput");class rQ{constructor(){this._map=new Map,this._idmap=new Map}add(e,...t){let r=t[0];if(this._map.set(e,r),r&&"object"==typeof r&&"id"in r){if(this._idmap.has(r.id))throw Error(`ID ${r.id} already exists in the registry`);this._idmap.set(r.id,e)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(e){let t=this._map.get(e);return t&&"object"==typeof t&&"id"in t&&this._idmap.delete(t.id),this._map.delete(e),this}get(e){let t=e._zod.parent;if(t){let r={...this.get(t)??{}};return delete r.id,{...r,...this._map.get(e)}}return this._map.get(e)}has(e){return this._map.has(e)}}let r0=new rQ;function r1(e,t){return new e({type:"string",format:"guid",check:"string_format",abort:!1,...e3(t)})}function r4(e,t){return new tL({check:"less_than",...e3(t),value:e,inclusive:!1})}function r2(e,t){return new tL({check:"less_than",...e3(t),value:e,inclusive:!0})}function r9(e,t){return new tB({check:"greater_than",...e3(t),value:e,inclusive:!1})}function r6(e,t){return new tB({check:"greater_than",...e3(t),value:e,inclusive:!0})}function r3(e,t){return new tW({check:"multiple_of",...e3(t),value:e})}function r8(e,t){return new tJ({check:"max_length",...e3(t),maximum:e})}function r5(e,t){return new tK({check:"min_length",...e3(t),minimum:e})}function r7(e,t){return new tY({check:"length_equals",...e3(t),length:e})}function ie(e){return new t9({check:"overwrite",tx:e})}let it=eq("ZodISODateTime",(e,t)=>{rc.init(e,t),iy.init(e,t)}),ir=eq("ZodISODate",(e,t)=>{rp.init(e,t),iy.init(e,t)}),ii=eq("ZodISOTime",(e,t)=>{rf.init(e,t),iy.init(e,t)}),ia=eq("ZodISODuration",(e,t)=>{rm.init(e,t),iy.init(e,t)}),is=(e,t)=>{ta.init(e,t),e.name="ZodError",Object.defineProperties(e,{format:{value:t=>(function(e,t){let r=t||function(e){return e.message},i={_errors:[]},n=e=>{for(let t of e.issues)if("invalid_union"===t.code&&t.errors.length)t.errors.map(e=>n({issues:e}));else if("invalid_key"===t.code)n({issues:t.issues});else if("invalid_element"===t.code)n({issues:t.issues});else if(0===t.path.length)i._errors.push(r(t));else{let e=i,n=0;for(;n<t.path.length;){let i=t.path[n];n===t.path.length-1?(e[i]=e[i]||{_errors:[]},e[i]._errors.push(r(t))):e[i]=e[i]||{_errors:[]},e=e[i],n++}}};return n(e),i})(e,t)},flatten:{value:t=>(function(e,t=e=>e.message){let r={},i=[];for(let n of e.issues)n.path.length>0?(r[n.path[0]]=r[n.path[0]]||[],r[n.path[0]].push(t(n))):i.push(t(n));return{formErrors:i,fieldErrors:r}})(e,t)},addIssue:{value:t=>e.issues.push(t)},addIssues:{value:t=>e.issues.push(...t)},isEmpty:{get:()=>0===e.issues.length}})};eq("ZodError",is);let io=eq("ZodError",is,{Parent:Error}),il=to(io),iu=tu(io),id=tc(io),ic=tf(io),ip=eq("ZodType",(e,t)=>(t8.init(e,t),e.def=t,Object.defineProperty(e,"_def",{value:t}),e.check=(...r)=>e.clone({...t,checks:[...t.checks??[],...r.map(e=>"function"==typeof e?{_zod:{check:e,def:{check:"custom"},onattach:[]}}:e)]}),e.clone=(t,r)=>e6(e,t,r),e.brand=()=>e,e.register=(t,r)=>(t.add(e,r),e),e.parse=(t,r)=>il(e,t,r,{callee:e.parse}),e.safeParse=(t,r)=>id(e,t,r),e.parseAsync=async(t,r)=>iu(e,t,r,{callee:e.parseAsync}),e.safeParseAsync=async(t,r)=>ic(e,t,r),e.spa=e.safeParseAsync,e.refine=(t,r)=>e.check(function(e,t={}){return new ne({type:"custom",check:"custom",fn:e,...e3(t)})}(t,r)),e.superRefine=t=>e.check(function(e){let t=function(e){let t=new tM({check:"custom"});return t._zod.check=e,t}(r=>(r.addIssue=e=>{"string"==typeof e?r.issues.push(ti(e,r.value,t._zod.def)):(e.fatal&&(e.continue=!1),e.code??(e.code="custom"),e.input??(e.input=r.value),e.inst??(e.inst=t),e.continue??(e.continue=!t._zod.def.abort),r.issues.push(ti(e)))},e(r.value,r)));return t}(t)),e.overwrite=t=>e.check(ie(t)),e.optional=()=>i0(e),e.nullable=()=>i4(e),e.nullish=()=>i0(i4(e)),e.nonoptional=t=>new i6({type:"nonoptional",innerType:e,...e3(t)}),e.array=()=>iB(e),e.or=t=>(function(e,t){return new iJ({type:"union",options:e,...e3(t)})})([e,t]),e.and=t=>new iK({type:"intersection",left:e,right:t}),e.transform=t=>i5(e,function(e){return new iX({type:"transform",transform:e})}(t)),e.default=t=>(function(e,t){return new i2({type:"default",innerType:e,get defaultValue(){return"function"==typeof t?t():t}})})(e,t),e.prefault=t=>(function(e,t){return new i9({type:"prefault",innerType:e,get defaultValue(){return"function"==typeof t?t():t}})})(e,t),e.catch=t=>(function(e,t){return new i3({type:"catch",innerType:e,catchValue:"function"==typeof t?t:()=>t})})(e,t),e.pipe=t=>i5(e,t),e.readonly=()=>new i7({type:"readonly",innerType:e}),e.describe=t=>{let r=e.clone();return r0.add(r,{description:t}),r},Object.defineProperty(e,"description",{get:()=>r0.get(e)?.description,configurable:!0}),e.meta=(...t)=>{if(0===t.length)return r0.get(e);let r=e.clone();return r0.add(r,t[0]),r},e.isOptional=()=>e.safeParse(void 0).success,e.isNullable=()=>e.safeParse(null).success,e)),im=eq("_ZodString",(e,t)=>{t5.init(e,t),ip.init(e,t);let r=e._zod.bag;e.format=r.format??null,e.minLength=r.minimum??null,e.maxLength=r.maximum??null,e.regex=(...t)=>e.check(function(e,t){return new tX({check:"string_format",format:"regex",...e3(t),pattern:e})}(...t)),e.includes=(...t)=>e.check(function(e,t){return new t1({check:"string_format",format:"includes",...e3(t),includes:e})}(...t)),e.startsWith=(...t)=>e.check(function(e,t){return new t4({check:"string_format",format:"starts_with",...e3(t),prefix:e})}(...t)),e.endsWith=(...t)=>e.check(function(e,t){return new t2({check:"string_format",format:"ends_with",...e3(t),suffix:e})}(...t)),e.min=(...t)=>e.check(r5(...t)),e.max=(...t)=>e.check(r8(...t)),e.length=(...t)=>e.check(r7(...t)),e.nonempty=(...t)=>e.check(r5(1,...t)),e.lowercase=t=>e.check(new tQ({check:"string_format",format:"lowercase",...e3(t)})),e.uppercase=t=>e.check(new t0({check:"string_format",format:"uppercase",...e3(t)})),e.trim=()=>e.check(ie(e=>e.trim())),e.normalize=(...t)=>e.check(function(e){return ie(t=>t.normalize(e))}(...t)),e.toLowerCase=()=>e.check(ie(e=>e.toLowerCase())),e.toUpperCase=()=>e.check(ie(e=>e.toUpperCase()))}),ih=eq("ZodString",(e,t)=>{t5.init(e,t),im.init(e,t),e.email=t=>e.check(new i_({type:"string",format:"email",check:"string_format",abort:!1,...e3(t)})),e.url=t=>e.check(new ix({type:"string",format:"url",check:"string_format",abort:!1,...e3(t)})),e.jwt=t=>e.check(new iO({type:"string",format:"jwt",check:"string_format",abort:!1,...e3(t)})),e.emoji=t=>e.check(new iw({type:"string",format:"emoji",check:"string_format",abort:!1,...e3(t)})),e.guid=t=>e.check(r1(ig,t)),e.uuid=t=>e.check(new ib({type:"string",format:"uuid",check:"string_format",abort:!1,...e3(t)})),e.uuidv4=t=>e.check(new ib({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...e3(t)})),e.uuidv6=t=>e.check(new ib({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...e3(t)})),e.uuidv7=t=>e.check(new ib({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...e3(t)})),e.nanoid=t=>e.check(new ik({type:"string",format:"nanoid",check:"string_format",abort:!1,...e3(t)})),e.guid=t=>e.check(r1(ig,t)),e.cuid=t=>e.check(new iz({type:"string",format:"cuid",check:"string_format",abort:!1,...e3(t)})),e.cuid2=t=>e.check(new iA({type:"string",format:"cuid2",check:"string_format",abort:!1,...e3(t)})),e.ulid=t=>e.check(new ij({type:"string",format:"ulid",check:"string_format",abort:!1,...e3(t)})),e.base64=t=>e.check(new iZ({type:"string",format:"base64",check:"string_format",abort:!1,...e3(t)})),e.base64url=t=>e.check(new iP({type:"string",format:"base64url",check:"string_format",abort:!1,...e3(t)})),e.xid=t=>e.check(new i$({type:"string",format:"xid",check:"string_format",abort:!1,...e3(t)})),e.ksuid=t=>e.check(new iS({type:"string",format:"ksuid",check:"string_format",abort:!1,...e3(t)})),e.ipv4=t=>e.check(new iE({type:"string",format:"ipv4",check:"string_format",abort:!1,...e3(t)})),e.ipv6=t=>e.check(new iF({type:"string",format:"ipv6",check:"string_format",abort:!1,...e3(t)})),e.cidrv4=t=>e.check(new iN({type:"string",format:"cidrv4",check:"string_format",abort:!1,...e3(t)})),e.cidrv6=t=>e.check(new iV({type:"string",format:"cidrv6",check:"string_format",abort:!1,...e3(t)})),e.e164=t=>e.check(new iI({type:"string",format:"e164",check:"string_format",abort:!1,...e3(t)})),e.datetime=t=>e.check(new it({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...e3(t)})),e.date=t=>e.check(new ir({type:"string",format:"date",check:"string_format",...e3(t)})),e.time=t=>e.check(new ii({type:"string",format:"time",check:"string_format",precision:null,...e3(t)})),e.duration=t=>e.check(new ia({type:"string",format:"duration",check:"string_format",...e3(t)}))});function iv(e){return new ih({type:"string",...e3(e)})}let iy=eq("ZodStringFormat",(e,t)=>{t7.init(e,t),im.init(e,t)}),i_=eq("ZodEmail",(e,t)=>{rr.init(e,t),iy.init(e,t)}),ig=eq("ZodGUID",(e,t)=>{re.init(e,t),iy.init(e,t)}),ib=eq("ZodUUID",(e,t)=>{rt.init(e,t),iy.init(e,t)}),ix=eq("ZodURL",(e,t)=>{ri.init(e,t),iy.init(e,t)}),iw=eq("ZodEmoji",(e,t)=>{rn.init(e,t),iy.init(e,t)}),ik=eq("ZodNanoID",(e,t)=>{ra.init(e,t),iy.init(e,t)}),iz=eq("ZodCUID",(e,t)=>{rs.init(e,t),iy.init(e,t)}),iA=eq("ZodCUID2",(e,t)=>{ro.init(e,t),iy.init(e,t)}),ij=eq("ZodULID",(e,t)=>{rl.init(e,t),iy.init(e,t)}),i$=eq("ZodXID",(e,t)=>{ru.init(e,t),iy.init(e,t)}),iS=eq("ZodKSUID",(e,t)=>{rd.init(e,t),iy.init(e,t)}),iE=eq("ZodIPv4",(e,t)=>{rh.init(e,t),iy.init(e,t)}),iF=eq("ZodIPv6",(e,t)=>{rv.init(e,t),iy.init(e,t)}),iN=eq("ZodCIDRv4",(e,t)=>{ry.init(e,t),iy.init(e,t)}),iV=eq("ZodCIDRv6",(e,t)=>{r_.init(e,t),iy.init(e,t)}),iZ=eq("ZodBase64",(e,t)=>{rb.init(e,t),iy.init(e,t)}),iP=eq("ZodBase64URL",(e,t)=>{rx.init(e,t),iy.init(e,t)}),iI=eq("ZodE164",(e,t)=>{rw.init(e,t),iy.init(e,t)}),iO=eq("ZodJWT",(e,t)=>{rk.init(e,t),iy.init(e,t)}),iT=eq("ZodNumber",(e,t)=>{rz.init(e,t),ip.init(e,t),e.gt=(t,r)=>e.check(r9(t,r)),e.gte=(t,r)=>e.check(r6(t,r)),e.min=(t,r)=>e.check(r6(t,r)),e.lt=(t,r)=>e.check(r4(t,r)),e.lte=(t,r)=>e.check(r2(t,r)),e.max=(t,r)=>e.check(r2(t,r)),e.int=t=>e.check(iU(t)),e.safe=t=>e.check(iU(t)),e.positive=t=>e.check(r9(0,t)),e.nonnegative=t=>e.check(r6(0,t)),e.negative=t=>e.check(r4(0,t)),e.nonpositive=t=>e.check(r2(0,t)),e.multipleOf=(t,r)=>e.check(r3(t,r)),e.step=(t,r)=>e.check(r3(t,r)),e.finite=()=>e;let r=e._zod.bag;e.minValue=Math.max(r.minimum??Number.NEGATIVE_INFINITY,r.exclusiveMinimum??Number.NEGATIVE_INFINITY)??null,e.maxValue=Math.min(r.maximum??Number.POSITIVE_INFINITY,r.exclusiveMaximum??Number.POSITIVE_INFINITY)??null,e.isInt=(r.format??"").includes("int")||Number.isSafeInteger(r.multipleOf??.5),e.isFinite=!0,e.format=r.format??null});function iC(e){return new iT({type:"number",checks:[],...e3(e)})}let iD=eq("ZodNumberFormat",(e,t)=>{rA.init(e,t),iT.init(e,t)});function iU(e){return new iD({type:"number",check:"number_format",abort:!1,format:"safeint",...e3(e)})}let iR=eq("ZodUnknown",(e,t)=>{rj.init(e,t),ip.init(e,t)});function iM(){return new iR({type:"unknown"})}let iq=eq("ZodNever",(e,t)=>{r$.init(e,t),ip.init(e,t)}),iL=eq("ZodArray",(e,t)=>{rE.init(e,t),ip.init(e,t),e.element=t.element,e.min=(t,r)=>e.check(r5(t,r)),e.nonempty=t=>e.check(r5(1,t)),e.max=(t,r)=>e.check(r8(t,r)),e.length=(t,r)=>e.check(r7(t,r)),e.unwrap=()=>e.element});function iB(e,t){return new iL({type:"array",element:e,...e3(t)})}let iW=eq("ZodObject",(e,t)=>{rV.init(e,t),ip.init(e,t),eY(e,"shape",()=>t.shape),e.keyof=()=>iH(Object.keys(e._zod.def.shape)),e.catchall=t=>e.clone({...e._zod.def,catchall:t}),e.passthrough=()=>e.clone({...e._zod.def,catchall:iM()}),e.loose=()=>e.clone({...e._zod.def,catchall:iM()}),e.strict=()=>{var t;return e.clone({...e._zod.def,catchall:new iq({type:"never",...e3(void 0)})})},e.strip=()=>e.clone({...e._zod.def,catchall:void 0}),e.extend=t=>(function(e,t){if(!e4(t))throw Error("Invalid input to extend: expected a plain object");let r={...e._zod.def,get shape(){let r={...e._zod.def.shape,...t};return eH(this,"shape",r),r},checks:[]};return e6(e,r)})(e,t),e.merge=t=>(function(e,t){return e6(e,{...e._zod.def,get shape(){let r={...e._zod.def.shape,...t._zod.def.shape};return eH(this,"shape",r),r},catchall:t._zod.def.catchall,checks:[]})})(e,t),e.pick=t=>(function(e,t){let r={},i=e._zod.def;for(let e in t){if(!(e in i.shape))throw Error(`Unrecognized key: "${e}"`);t[e]&&(r[e]=i.shape[e])}return e6(e,{...e._zod.def,shape:r,checks:[]})})(e,t),e.omit=t=>(function(e,t){let r={...e._zod.def.shape},i=e._zod.def;for(let e in t){if(!(e in i.shape))throw Error(`Unrecognized key: "${e}"`);t[e]&&delete r[e]}return e6(e,{...e._zod.def,shape:r,checks:[]})})(e,t),e.partial=(...t)=>(function(e,t,r){let i=t._zod.def.shape,n={...i};if(r)for(let t in r){if(!(t in i))throw Error(`Unrecognized key: "${t}"`);r[t]&&(n[t]=e?new e({type:"optional",innerType:i[t]}):i[t])}else for(let t in i)n[t]=e?new e({type:"optional",innerType:i[t]}):i[t];return e6(t,{...t._zod.def,shape:n,checks:[]})})(iQ,e,t[0]),e.required=(...t)=>(function(e,t,r){let i=t._zod.def.shape,n={...i};if(r)for(let t in r){if(!(t in n))throw Error(`Unrecognized key: "${t}"`);r[t]&&(n[t]=new e({type:"nonoptional",innerType:i[t]}))}else for(let t in i)n[t]=new e({type:"nonoptional",innerType:i[t]});return e6(t,{...t._zod.def,shape:n,checks:[]})})(i6,e,t[0])});function iG(e,t){return new iW({type:"object",get shape(){return eH(this,"shape",{...e}),this.shape},...e3(t)})}let iJ=eq("ZodUnion",(e,t)=>{rP.init(e,t),ip.init(e,t),e.options=t.options}),iK=eq("ZodIntersection",(e,t)=>{rI.init(e,t),ip.init(e,t)}),iY=eq("ZodEnum",(e,t)=>{rT.init(e,t),ip.init(e,t),e.enum=t.entries,e.options=Object.values(t.entries);let r=new Set(Object.keys(t.entries));e.extract=(e,i)=>{let n={};for(let i of e)if(r.has(i))n[i]=t.entries[i];else throw Error(`Key ${i} not found in enum`);return new iY({...t,checks:[],...e3(i),entries:n})},e.exclude=(e,i)=>{let n={...t.entries};for(let t of e)if(r.has(t))delete n[t];else throw Error(`Key ${t} not found in enum`);return new iY({...t,checks:[],...e3(i),entries:n})}});function iH(e,t){return new iY({type:"enum",entries:Array.isArray(e)?Object.fromEntries(e.map(e=>[e,e])):e,...e3(t)})}let iX=eq("ZodTransform",(e,t)=>{rC.init(e,t),ip.init(e,t),e._zod.parse=(r,i)=>{r.addIssue=i=>{"string"==typeof i?r.issues.push(ti(i,r.value,t)):(i.fatal&&(i.continue=!1),i.code??(i.code="custom"),i.input??(i.input=r.value),i.inst??(i.inst=e),i.continue??(i.continue=!0),r.issues.push(ti(i)))};let n=t.transform(r.value,r);return n instanceof Promise?n.then(e=>(r.value=e,r)):(r.value=n,r)}}),iQ=eq("ZodOptional",(e,t)=>{rD.init(e,t),ip.init(e,t),e.unwrap=()=>e._zod.def.innerType});function i0(e){return new iQ({type:"optional",innerType:e})}let i1=eq("ZodNullable",(e,t)=>{rU.init(e,t),ip.init(e,t),e.unwrap=()=>e._zod.def.innerType});function i4(e){return new i1({type:"nullable",innerType:e})}let i2=eq("ZodDefault",(e,t)=>{rR.init(e,t),ip.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeDefault=e.unwrap}),i9=eq("ZodPrefault",(e,t)=>{rq.init(e,t),ip.init(e,t),e.unwrap=()=>e._zod.def.innerType}),i6=eq("ZodNonOptional",(e,t)=>{rL.init(e,t),ip.init(e,t),e.unwrap=()=>e._zod.def.innerType}),i3=eq("ZodCatch",(e,t)=>{rW.init(e,t),ip.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeCatch=e.unwrap}),i8=eq("ZodPipe",(e,t)=>{rG.init(e,t),ip.init(e,t),e.in=t.in,e.out=t.out});function i5(e,t){return new i8({type:"pipe",in:e,out:t})}let i7=eq("ZodReadonly",(e,t)=>{rK.init(e,t),ip.init(e,t)}),ne=eq("ZodCustom",(e,t)=>{rH.init(e,t),ip.init(e,t)}),nt=iH(["gold","silver","bronze"]),nr=iH(["active","inactive"]),ni=iH(["pending","assigned","disputed","approved","rejected"]),nn=iH(["duplicate_end_user","territory_overlap","timing_conflict"]),na=iH(["pending","resolved","dismissed"]),ns=iH(["admin","manager","staff"]);iG({id:iv().uuid().optional(),name:iv().min(1,"Reseller name is required"),email:iv().email("Valid email is required"),territory:iv().min(1,"Territory is required"),tier:nt,status:nr.default("active"),created_at:iv().optional(),updated_at:iv().optional()}),iG({id:iv().uuid().optional(),company_name:iv().min(1,"Company name is required"),contact_name:iv().min(1,"Contact name is required"),contact_email:iv().email("Valid email is required"),territory:iv().min(1,"Territory is required"),created_at:iv().optional(),updated_at:iv().optional()}),iG({id:iv().uuid().optional(),name:iv().min(1,"Product name is required"),category:iv().min(1,"Category is required"),list_price:iC().positive("Price must be positive"),created_at:iv().optional(),updated_at:iv().optional()}),iG({id:iv().uuid().optional(),deal_id:iv().uuid().optional(),product_id:iv().uuid(),quantity:iC().int().positive("Quantity must be positive"),price:iC().positive("Price must be positive"),created_at:iv().optional()}),iG({id:iv().uuid().optional(),reseller_id:iv().uuid(),end_user_id:iv().uuid(),assigned_reseller_id:iv().uuid().nullable().optional(),status:ni.default("pending"),total_value:iC().positive("Total value must be positive"),submission_date:iv().optional(),assignment_date:iv().nullable().optional(),created_at:iv().optional(),updated_at:iv().optional()}),iG({id:iv().uuid().optional(),deal_id:iv().uuid(),competing_deal_id:iv().uuid(),conflict_type:nn,resolution_status:na.default("pending"),assigned_to_staff:iv().uuid().nullable().optional(),created_at:iv().optional(),updated_at:iv().optional()}),iG({id:iv().uuid().optional(),email:iv().email("Valid email is required"),name:iv().min(1,"Name is required"),role:ns.default("staff"),created_at:iv().optional(),updated_at:iv().optional()});let no=iG({reseller_id:iv().uuid("Please select a reseller"),end_user:iG({id:iv().uuid().optional(),company_name:iv().min(1,"Company name is required"),contact_name:iv().min(1,"Contact name is required"),contact_email:iv().email("Valid email is required"),territory:iv().min(1,"Territory is required")}),products:iB(iG({product_id:iv().uuid("Please select a product"),quantity:iC().int().positive("Quantity must be positive"),price:iC().positive("Price must be positive")})).min(1,"At least one product is required")});iG({deal_id:iv().uuid(),assigned_reseller_id:iv().uuid(),reason:iv().optional()});var nl=r(4780),nu=r(43649),nd=r(96474),nc=r(88233);function np(){let e=(0,a.useRouter)(),[t,r]=(0,n.useState)([]),[m,y]=(0,n.useState)([]),[g,w]=(0,n.useState)(!1),[A,N]=(0,n.useState)([]),[V,R]=(0,n.useState)(!1),{register:J,control:X,handleSubmit:Q,watch:et,setValue:er,formState:{errors:ei}}=function(e={}){let t=n.useRef(void 0),r=n.useRef(void 0),[i,a]=n.useState({isDirty:!1,isValidating:!1,isLoading:B(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:B(e.defaultValues)?void 0:e.defaultValues});if(!t.current)if(e.formControl)t.current={...e.formControl,formState:i},e.defaultValues&&!B(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{let{formControl:r,...n}=function(e={}){let t,r={...e$,...e},i={submitCount:0,isDirty:!1,isReady:!1,isLoading:B(r.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:r.errors||{},disabled:r.disabled||!1},n={},a=(h(r.defaultValues)||h(r.values))&&x(r.defaultValues||r.values)||{},s=r.shouldUnregister?{}:x(a),o={action:!1,mount:!1,watch:!1},l={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},u=0,d={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},m={...d},y={array:U(),state:U()},g=r.criteriaMode===F.all,w=e=>t=>{clearTimeout(u),u=setTimeout(e,t)},A=async e=>{if(!r.disabled&&(d.isValid||m.isValid||e)){let e=r.resolver?q((await C()).errors):await J(n,!0);e!==i.isValid&&y.state.next({isValid:e})}},N=(e,t)=>{!r.disabled&&(d.isValidating||d.validatingFields||m.isValidating||m.validatingFields)&&((e||Array.from(l.mount)).forEach(e=>{e&&(t?S(i.validatingFields,e,t):H(i.validatingFields,e))}),y.state.next({validatingFields:i.validatingFields,isValidating:!q(i.validatingFields)}))},V=(e,t)=>{S(i.errors,e,t),y.state.next({errors:i.errors})},Z=(e,t,r,i)=>{let l=j(n,e);if(l){let n=j(s,e,k(r)?j(a,e):r);k(n)||i&&i.defaultChecked||t?S(s,e,t?n:eo(l._f)):et(e,n),o.mount&&A()}},P=(e,t,n,s,o)=>{let l=!1,u=!1,c={name:e};if(!r.disabled){if(!n||s){(d.isDirty||m.isDirty)&&(u=i.isDirty,i.isDirty=c.isDirty=X(),l=u!==c.isDirty);let r=M(j(a,e),t);u=!!j(i.dirtyFields,e),r?H(i.dirtyFields,e):S(i.dirtyFields,e,!0),c.dirtyFields=i.dirtyFields,l=l||(d.dirtyFields||m.dirtyFields)&&!r!==u}if(n){let t=j(i.touchedFields,e);t||(S(i.touchedFields,e,n),c.touchedFields=i.touchedFields,l=l||(d.touchedFields||m.touchedFields)&&t!==n)}l&&o&&y.state.next(c)}return l?c:{}},I=(e,n,a,s)=>{let o=j(i.errors,e),l=(d.isValid||m.isValid)&&$(n)&&i.isValid!==n;if(r.delayError&&a?(t=w(()=>V(e,a)))(r.delayError):(clearTimeout(u),t=null,a?S(i.errors,e,a):H(i.errors,e)),(a?!M(o,a):o)||!q(s)||l){let t={...s,...l&&$(n)?{isValid:n}:{},errors:i.errors,name:e};i={...i,...t},y.state.next(t)}},C=async e=>{N(e,!0);let t=await r.resolver(s,r.context,el(e||l.mount,n,r.criteriaMode,r.shouldUseNativeValidation));return N(e),t},R=async e=>{let{errors:t}=await C(e);if(e)for(let r of e){let e=j(t,r);e?S(i.errors,r,e):H(i.errors,r)}else i.errors=t;return t},J=async(e,t,n={valid:!0})=>{for(let a in e){let o=e[a];if(o){let{_f:e,...u}=o;if(e){let u=l.array.has(e.name),c=o._f&&ef(o._f);c&&d.validatingFields&&N([a],!0);let p=await ej(o,l.disabled,s,g,r.shouldUseNativeValidation&&!t,u);if(c&&d.validatingFields&&N([a]),p[e.name]&&(n.valid=!1,t))break;t||(j(p,e.name)?u?ew(i.errors,p,e.name):S(i.errors,e.name,p[e.name]):H(i.errors,e.name))}q(u)||await J(u,t,n)}}return n.valid},X=(e,t)=>!r.disabled&&(e&&t&&S(s,e,t),!M(ep(),a)),Q=(e,t,r)=>T(e,l,{...o.mount?s:k(t)?a:O(e)?{[e]:t}:t},r,t),et=(e,t,r={})=>{let i=j(n,e),a=t;if(i){let r=i._f;r&&(r.disabled||S(s,e,en(t,r)),a=W(r.ref)&&f(t)?"":t,G(r.ref)?[...r.ref.options].forEach(e=>e.selected=a.includes(e.value)):r.refs?c(r.ref)?r.refs.forEach(e=>{e.defaultChecked&&e.disabled||(Array.isArray(a)?e.checked=!!a.find(t=>t===e.value):e.checked=a===e.value||!!a)}):r.refs.forEach(e=>e.checked=e.value===a):L(r.ref)?r.ref.value="":(r.ref.value=a,r.ref.type||y.state.next({name:e,values:x(s)})))}(r.shouldDirty||r.shouldTouch)&&P(e,a,r.shouldTouch,r.shouldDirty,!0),r.shouldValidate&&eu(e)},er=(e,t,r)=>{for(let i in t){if(!t.hasOwnProperty(i))return;let a=t[i],s=e+"."+i,o=j(n,s);(l.array.has(e)||h(a)||o&&!o._f)&&!p(a)?er(s,a,r):et(s,a,r)}},ei=(e,t,r={})=>{let u=j(n,e),c=l.array.has(e),p=x(t);S(s,e,p),c?(y.array.next({name:e,values:x(s)}),(d.isDirty||d.dirtyFields||m.isDirty||m.dirtyFields)&&r.shouldDirty&&y.state.next({name:e,dirtyFields:ee(a,s),isDirty:X(e,p)})):!u||u._f||f(p)?et(e,p,r):er(e,p,r),eh(e,l)&&y.state.next({...i}),y.state.next({name:o.mount?e:void 0,values:x(s)})},ea=async e=>{o.mount=!0;let a=e.target,u=a.name,c=!0,f=j(n,u),h=e=>{c=Number.isNaN(e)||p(e)&&isNaN(e.getTime())||M(e,j(s,u,e))},_=ec(r.mode),b=ec(r.reValidateMode);if(f){let o,p,w=a.type?eo(f._f):v(e),k=e.type===E.BLUR||e.type===E.FOCUS_OUT,z=!em(f._f)&&!r.resolver&&!j(i.errors,u)&&!f._f.deps||eb(k,j(i.touchedFields,u),i.isSubmitted,b,_),$=eh(u,l,k);S(s,u,w),k?(f._f.onBlur&&f._f.onBlur(e),t&&t(0)):f._f.onChange&&f._f.onChange(e);let F=P(u,w,k),V=!q(F)||$;if(k||y.state.next({name:u,type:e.type,values:x(s)}),z)return(d.isValid||m.isValid)&&("onBlur"===r.mode?k&&A():k||A()),V&&y.state.next({name:u,...$?{}:F});if(!k&&$&&y.state.next({...i}),r.resolver){let{errors:e}=await C([u]);if(h(w),c){let t=ey(i.errors,n,u),r=ey(e,n,t.name||u);o=r.error,u=r.name,p=q(e)}}else N([u],!0),o=(await ej(f,l.disabled,s,g,r.shouldUseNativeValidation))[u],N([u]),h(w),c&&(o?p=!1:(d.isValid||m.isValid)&&(p=await J(n,!0)));c&&(f._f.deps&&eu(f._f.deps),I(u,p,o,F))}},es=(e,t)=>{if(j(i.errors,t)&&e.focus)return e.focus(),1},eu=async(e,t={})=>{let a,s,o=D(e);if(r.resolver){let t=await R(k(e)?e:o);a=q(t),s=e?!o.some(e=>j(t,e)):a}else e?((s=(await Promise.all(o.map(async e=>{let t=j(n,e);return await J(t&&t._f?{[e]:t}:t)}))).every(Boolean))||i.isValid)&&A():s=a=await J(n);return y.state.next({...!O(e)||(d.isValid||m.isValid)&&a!==i.isValid?{}:{name:e},...r.resolver||!e?{isValid:a}:{},errors:i.errors}),t.shouldFocus&&!s&&ev(n,es,e?o:l.mount),s},ep=e=>{let t={...o.mount?s:a};return k(e)?t:O(e)?j(t,e):e.map(e=>j(t,e))},ek=(e,t)=>({invalid:!!j((t||i).errors,e),isDirty:!!j((t||i).dirtyFields,e),error:j((t||i).errors,e),isValidating:!!j(i.validatingFields,e),isTouched:!!j((t||i).touchedFields,e)}),ez=(e,t,r)=>{let a=(j(n,e,{_f:{}})._f||{}).ref,{ref:s,message:o,type:l,...u}=j(i.errors,e)||{};S(i.errors,e,{...u,...t,ref:a}),y.state.next({name:e,errors:i.errors,isValid:!1}),r&&r.shouldFocus&&a&&a.focus&&a.focus()},eA=e=>y.state.subscribe({next:t=>{eg(e.name,t.name,e.exact)&&e_(t,e.formState||d,eI,e.reRenderRoot)&&e.callback({values:{...s},...i,...t})}}).unsubscribe,eS=(e,t={})=>{for(let o of e?D(e):l.mount)l.mount.delete(o),l.array.delete(o),t.keepValue||(H(n,o),H(s,o)),t.keepError||H(i.errors,o),t.keepDirty||H(i.dirtyFields,o),t.keepTouched||H(i.touchedFields,o),t.keepIsValidating||H(i.validatingFields,o),r.shouldUnregister||t.keepDefaultValue||H(a,o);y.state.next({values:x(s)}),y.state.next({...i,...!t.keepDirty?{}:{isDirty:X()}}),t.keepIsValid||A()},eE=({disabled:e,name:t})=>{($(e)&&o.mount||e||l.disabled.has(t))&&(e?l.disabled.add(t):l.disabled.delete(t))},eF=(e,t={})=>{let i=j(n,e),s=$(t.disabled)||$(r.disabled);return S(n,e,{...i||{},_f:{...i&&i._f?i._f:{ref:{name:e}},name:e,mount:!0,...t}}),l.mount.add(e),i?eE({disabled:$(t.disabled)?t.disabled:r.disabled,name:e}):Z(e,!0,t.value),{...s?{disabled:t.disabled||r.disabled}:{},...r.progressive?{required:!!t.required,min:ed(t.min),max:ed(t.max),minLength:ed(t.minLength),maxLength:ed(t.maxLength),pattern:ed(t.pattern)}:{},name:e,onChange:ea,onBlur:ea,ref:s=>{if(s){eF(e,t),i=j(n,e);let r=k(s.value)&&s.querySelectorAll&&s.querySelectorAll("input,select,textarea")[0]||s,o=K(r),l=i._f.refs||[];(o?l.find(e=>e===r):r===i._f.ref)||(S(n,e,{_f:{...i._f,...o?{refs:[...l.filter(Y),r,...Array.isArray(j(a,e))?[{}]:[]],ref:{type:r.type,name:e}}:{ref:r}}}),Z(e,!1,void 0,r))}else(i=j(n,e,{}))._f&&(i._f.mount=!1),(r.shouldUnregister||t.shouldUnregister)&&!(_(l.array,e)&&o.action)&&l.unMount.add(e)}}},eN=()=>r.shouldFocusError&&ev(n,es,l.mount),eV=(e,t)=>async a=>{let o;a&&(a.preventDefault&&a.preventDefault(),a.persist&&a.persist());let u=x(s);if(y.state.next({isSubmitting:!0}),r.resolver){let{errors:e,values:t}=await C();i.errors=e,u=x(t)}else await J(n);if(l.disabled.size)for(let e of l.disabled)H(u,e);if(H(i.errors,"root"),q(i.errors)){y.state.next({errors:{}});try{await e(u,a)}catch(e){o=e}}else t&&await t({...i.errors},a),eN(),setTimeout(eN);if(y.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:q(i.errors)&&!o,submitCount:i.submitCount+1,errors:i.errors}),o)throw o},eZ=(e,t={})=>{let u=e?x(e):a,c=x(u),p=q(e),f=p?a:c;if(t.keepDefaultValues||(a=u),!t.keepValues){if(t.keepDirtyValues)for(let e of Array.from(new Set([...l.mount,...Object.keys(ee(a,s))])))j(i.dirtyFields,e)?S(f,e,j(s,e)):ei(e,j(f,e));else{if(b&&k(e))for(let e of l.mount){let t=j(n,e);if(t&&t._f){let e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(W(e)){let t=e.closest("form");if(t){t.reset();break}}}}if(t.keepFieldsRef)for(let e of l.mount)ei(e,j(f,e));else n={}}s=r.shouldUnregister?t.keepDefaultValues?x(a):{}:x(f),y.array.next({values:{...f}}),y.state.next({values:{...f}})}l={mount:t.keepDirtyValues?l.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},o.mount=!d.isValid||!!t.keepIsValid||!!t.keepDirtyValues,o.watch=!!r.shouldUnregister,y.state.next({submitCount:t.keepSubmitCount?i.submitCount:0,isDirty:!p&&(t.keepDirty?i.isDirty:!!(t.keepDefaultValues&&!M(e,a))),isSubmitted:!!t.keepIsSubmitted&&i.isSubmitted,dirtyFields:p?{}:t.keepDirtyValues?t.keepDefaultValues&&s?ee(a,s):i.dirtyFields:t.keepDefaultValues&&e?ee(a,e):t.keepDirty?i.dirtyFields:{},touchedFields:t.keepTouched?i.touchedFields:{},errors:t.keepErrors?i.errors:{},isSubmitSuccessful:!!t.keepIsSubmitSuccessful&&i.isSubmitSuccessful,isSubmitting:!1})},eP=(e,t)=>eZ(B(e)?e(s):e,t),eI=e=>{i={...i,...e}},eO={control:{register:eF,unregister:eS,getFieldState:ek,handleSubmit:eV,setError:ez,_subscribe:eA,_runSchema:C,_focusError:eN,_getWatch:Q,_getDirty:X,_setValid:A,_setFieldArray:(e,t=[],l,u,c=!0,p=!0)=>{if(u&&l&&!r.disabled){if(o.action=!0,p&&Array.isArray(j(n,e))){let t=l(j(n,e),u.argA,u.argB);c&&S(n,e,t)}if(p&&Array.isArray(j(i.errors,e))){let t=l(j(i.errors,e),u.argA,u.argB);c&&S(i.errors,e,t),ex(i.errors,e)}if((d.touchedFields||m.touchedFields)&&p&&Array.isArray(j(i.touchedFields,e))){let t=l(j(i.touchedFields,e),u.argA,u.argB);c&&S(i.touchedFields,e,t)}(d.dirtyFields||m.dirtyFields)&&(i.dirtyFields=ee(a,s)),y.state.next({name:e,isDirty:X(e,t),dirtyFields:i.dirtyFields,errors:i.errors,isValid:i.isValid})}else S(s,e,t)},_setDisabledField:eE,_setErrors:e=>{i.errors=e,y.state.next({errors:i.errors,isValid:!1})},_getFieldArray:e=>z(j(o.mount?s:a,e,r.shouldUnregister?j(a,e,[]):[])),_reset:eZ,_resetDefaultValues:()=>B(r.defaultValues)&&r.defaultValues().then(e=>{eP(e,r.resetOptions),y.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(let e of l.unMount){let t=j(n,e);t&&(t._f.refs?t._f.refs.every(e=>!Y(e)):!Y(t._f.ref))&&eS(e)}l.unMount=new Set},_disableForm:e=>{$(e)&&(y.state.next({disabled:e}),ev(n,(t,r)=>{let i=j(n,r);i&&(t.disabled=i._f.disabled||e,Array.isArray(i._f.refs)&&i._f.refs.forEach(t=>{t.disabled=i._f.disabled||e}))},0,!1))},_subjects:y,_proxyFormState:d,get _fields(){return n},get _formValues(){return s},get _state(){return o},set _state(value){o=value},get _defaultValues(){return a},get _names(){return l},set _names(value){l=value},get _formState(){return i},get _options(){return r},set _options(value){r={...r,...value}}},subscribe:e=>(o.mount=!0,m={...m,...e.formState},eA({...e,formState:m})),trigger:eu,register:eF,handleSubmit:eV,watch:(e,t)=>B(e)?y.state.subscribe({next:r=>e(Q(void 0,t),r)}):Q(e,t,!0),setValue:ei,getValues:ep,reset:eP,resetField:(e,t={})=>{j(n,e)&&(k(t.defaultValue)?ei(e,x(j(a,e))):(ei(e,t.defaultValue),S(a,e,x(t.defaultValue))),t.keepTouched||H(i.touchedFields,e),t.keepDirty||(H(i.dirtyFields,e),i.isDirty=t.defaultValue?X(e,x(j(a,e))):X()),!t.keepError&&(H(i.errors,e),d.isValid&&A()),y.state.next({...i}))},clearErrors:e=>{e&&D(e).forEach(e=>H(i.errors,e)),y.state.next({errors:e?i.errors:{}})},unregister:eS,setError:ez,setFocus:(e,t={})=>{let r=j(n,e),i=r&&r._f;if(i){let e=i.refs?i.refs[0]:i.ref;e.focus&&(e.focus(),t.shouldSelect&&B(e.select)&&e.select())}},getFieldState:ek};return{...eO,formControl:eO}}(e);t.current={...n,formState:i}}let s=t.current.control;return s._options=e,I(()=>{let e=s._subscribe({formState:s._proxyFormState,callback:()=>a({...s._formState}),reRenderRoot:!0});return a(e=>({...e,isReady:!0})),s._formState.isReady=!0,e},[s]),n.useEffect(()=>s._disableForm(e.disabled),[s,e.disabled]),n.useEffect(()=>{e.mode&&(s._options.mode=e.mode),e.reValidateMode&&(s._options.reValidateMode=e.reValidateMode)},[s,e.mode,e.reValidateMode]),n.useEffect(()=>{e.errors&&(s._setErrors(e.errors),s._focusError())},[s,e.errors]),n.useEffect(()=>{e.shouldUnregister&&s._subjects.state.next({values:s._getWatch()})},[s,e.shouldUnregister]),n.useEffect(()=>{if(s._proxyFormState.isDirty){let e=s._getDirty();e!==i.isDirty&&s._subjects.state.next({isDirty:e})}},[s,i.isDirty]),n.useEffect(()=>{e.values&&!M(e.values,r.current)?(s._reset(e.values,{keepFieldsRef:!0,...s._options.resetOptions}),r.current=e.values,a(e=>({...e}))):s._resetDefaultValues()},[s,e.values]),n.useEffect(()=>{s._state.mount||(s._setValid(),s._state.mount=!0),s._state.watch&&(s._state.watch=!1,s._subjects.state.next({...s._formState})),s._removeUnmounted()}),t.current.formState=P(i,s),t.current}({resolver:function(e,t,r){if(void 0===r&&(r={}),"_def"in e&&"object"==typeof e._def&&"typeName"in e._def)return function(t,i,n){try{return Promise.resolve(th(function(){return Promise.resolve(e["sync"===r.mode?"parse":"parseAsync"](t,void 0)).then(function(e){return n.shouldUseNativeValidation&&eD({},n),{errors:{},values:r.raw?Object.assign({},t):e}})},function(e){if(Array.isArray(null==e?void 0:e.issues))return{values:{},errors:eU(function(e,t){for(var r={};e.length;){var i=e[0],n=i.code,a=i.message,s=i.path.join(".");if(!r[s])if("unionErrors"in i){var o=i.unionErrors[0].errors[0];r[s]={message:o.message,type:o.code}}else r[s]={message:a,type:n};if("unionErrors"in i&&i.unionErrors.forEach(function(t){return t.errors.forEach(function(t){return e.push(t)})}),t){var l=r[s].types,u=l&&l[i.code];r[s]=C(s,t,r,n,u?[].concat(u,i.message):i.message)}e.shift()}return r}(e.errors,!n.shouldUseNativeValidation&&"all"===n.criteriaMode),n)};throw e}))}catch(e){return Promise.reject(e)}};if("_zod"in e&&"object"==typeof e._zod)return function(t,i,n){try{return Promise.resolve(th(function(){return Promise.resolve(("sync"===r.mode?tl:td)(e,t,void 0)).then(function(e){return n.shouldUseNativeValidation&&eD({},n),{errors:{},values:r.raw?Object.assign({},t):e}})},function(e){if(e instanceof ta)return{values:{},errors:eU(function(e,t){for(var r={};e.length;){var i=e[0],n=i.code,a=i.message,s=i.path.join(".");if(!r[s])if("invalid_union"===i.code){var o=i.errors[0][0];r[s]={message:o.message,type:o.code}}else r[s]={message:a,type:n};if("invalid_union"===i.code&&i.errors.forEach(function(t){return t.forEach(function(t){return e.push(t)})}),t){var l=r[s].types,u=l&&l[i.code];r[s]=C(s,t,r,n,u?[].concat(u,i.message):i.message)}e.shift()}return r}(e.issues,!n.shouldUseNativeValidation&&"all"===n.criteriaMode),n)};throw e}))}catch(e){return Promise.reject(e)}};throw Error("Invalid input: not a Zod schema")}(no),defaultValues:{products:[{product_id:"",quantity:1,price:0}]}}),{fields:ea,append:es,remove:eu}=function(e){let t=Z(),{control:r=t.control,name:i,keyName:a="id",shouldUnregister:s,rules:o}=e,[l,u]=n.useState(r._getFieldArray(i)),d=n.useRef(r._getFieldArray(i).map(eS)),c=n.useRef(l),p=n.useRef(i),f=n.useRef(!1);p.current=i,c.current=l,r._names.array.add(i),o&&r.register(i,o),I(()=>r._subjects.array.subscribe({next:({values:e,name:t})=>{if(t===p.current||!t){let t=j(e,p.current);Array.isArray(t)&&(u(t),d.current=t.map(eS))}}}).unsubscribe,[r]);let m=n.useCallback(e=>{f.current=!0,r._setFieldArray(i,e)},[r,i]);return n.useEffect(()=>{if(r._state.action=!1,eh(i,r._names)&&r._subjects.state.next({...r._formState}),f.current&&(!ec(r._options.mode).isOnSubmit||r._formState.isSubmitted)&&!ec(r._options.reValidateMode).isOnSubmit)if(r._options.resolver)r._runSchema([i]).then(e=>{let t=j(e.errors,i),n=j(r._formState.errors,i);(n?!t&&n.type||t&&(n.type!==t.type||n.message!==t.message):t&&t.type)&&(t?S(r._formState.errors,i,t):H(r._formState.errors,i),r._subjects.state.next({errors:r._formState.errors}))});else{let e=j(r._fields,i);e&&e._f&&!(ec(r._options.reValidateMode).isOnSubmit&&ec(r._options.mode).isOnSubmit)&&ej(e,r._names.disabled,r._formValues,r._options.criteriaMode===F.all,r._options.shouldUseNativeValidation,!0).then(e=>!q(e)&&r._subjects.state.next({errors:ew(r._formState.errors,e,i)}))}r._subjects.state.next({name:i,values:x(r._formValues)}),r._names.focus&&ev(r._fields,(e,t)=>{if(r._names.focus&&t.startsWith(r._names.focus)&&e.focus)return e.focus(),1}),r._names.focus="",r._setValid(),f.current=!1},[l,i,r]),n.useEffect(()=>(j(r._formValues,i)||r._setFieldArray(i),()=>{r._options.shouldUnregister||s?r.unregister(i):((e,t)=>{let i=j(r._fields,e);i&&i._f&&(i._f.mount=t)})(i,!1)}),[i,r,a,s]),{swap:n.useCallback((e,t)=>{let n=r._getFieldArray(i);eO(n,e,t),eO(d.current,e,t),m(n),u(n),r._setFieldArray(i,n,eO,{argA:e,argB:t},!1)},[m,i,r]),move:n.useCallback((e,t)=>{let n=r._getFieldArray(i);eZ(n,e,t),eZ(d.current,e,t),m(n),u(n),r._setFieldArray(i,n,eZ,{argA:e,argB:t},!1)},[m,i,r]),prepend:n.useCallback((e,t)=>{let n=D(x(e)),a=eP(r._getFieldArray(i),n);r._names.focus=eE(i,0,t),d.current=eP(d.current,n.map(eS)),m(a),u(a),r._setFieldArray(i,a,eP,{argA:eN(e)})},[m,i,r]),append:n.useCallback((e,t)=>{let n=D(x(e)),a=eF(r._getFieldArray(i),n);r._names.focus=eE(i,a.length-1,t),d.current=eF(d.current,n.map(eS)),m(a),u(a),r._setFieldArray(i,a,eF,{argA:eN(e)})},[m,i,r]),remove:n.useCallback(e=>{let t=eI(r._getFieldArray(i),e);d.current=eI(d.current,e),m(t),u(t),Array.isArray(j(r._fields,i))||S(r._fields,i,void 0),r._setFieldArray(i,t,eI,{argA:e})},[m,i,r]),insert:n.useCallback((e,t,n)=>{let a=D(x(t)),s=eV(r._getFieldArray(i),e,a);r._names.focus=eE(i,e,n),d.current=eV(d.current,e,a.map(eS)),m(s),u(s),r._setFieldArray(i,s,eV,{argA:e,argB:eN(t)})},[m,i,r]),update:n.useCallback((e,t)=>{let n=x(t),a=eT(r._getFieldArray(i),e,n);d.current=[...a].map((t,r)=>t&&r!==e?d.current[r]:eS()),m(a),u([...a]),r._setFieldArray(i,a,eT,{argA:e,argB:n},!0,!1)},[m,i,r]),replace:n.useCallback(e=>{let t=D(x(e));d.current=t.map(eS),m([...t]),u([...t]),r._setFieldArray(i,[...t],e=>e,{},!0,!1)},[m,i,r]),fields:n.useMemo(()=>l.map((e,t)=>({...e,[a]:d.current[t]||eS()})),[l,a])}}({control:X,name:"products"}),ep=et("products"),ek=ep?.reduce((e,t)=>e+t.quantity*t.price,0)||0,ez=(e,t)=>{let r=m.find(e=>e.id===t);r&&er(`products.${e}.price`,r.list_price)},eA=async t=>{w(!0),N([]);try{let r=await fetch("/api/deals",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),i=await r.json();if(r.ok)if(i.data.conflicts?.hasConflicts){let e=i.data.conflicts.conflicts.map(e=>({type:e.type,severity:e.severity,message:e.reason}));N(e),R(!0)}else e.push(`/deals/${i.data.deal.id}`);else console.error("Error creating deal:",i.error)}catch(e){console.error("Error submitting deal:",e)}finally{w(!1)}};return(0,i.jsx)(s.O,{title:"Register New Deal",subtitle:"Submit a new deal registration for review and assignment",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto",children:[V&&A.length>0&&(0,i.jsxs)(o.Zp,{className:"mb-6 border-orange-200 bg-orange-50",children:[(0,i.jsx)(o.aR,{children:(0,i.jsxs)(o.ZB,{className:"flex items-center text-orange-800",children:[(0,i.jsx)(nu.A,{className:"mr-2 h-5 w-5"}),"Conflicts Detected"]})}),(0,i.jsxs)(o.Wu,{children:[(0,i.jsx)("div",{className:"space-y-3",children:A.map((e,t)=>(0,i.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,i.jsx)(d.E,{variant:"high"===e.severity?"error":"warning",className:"mt-0.5",children:e.severity}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm font-medium text-orange-800",children:e.type.replace("_"," ").toUpperCase()}),(0,i.jsx)("p",{className:"text-sm text-orange-700",children:e.message})]})]},t))}),(0,i.jsxs)("div",{className:"mt-4 flex space-x-3",children:[(0,i.jsx)(l.$,{onClick:()=>{R(!1),e.push("/deals")},variant:"outline",children:"Continue to Deals List"}),(0,i.jsx)(l.$,{onClick:()=>R(!1),children:"Edit Deal"})]})]})]}),(0,i.jsxs)("form",{onSubmit:Q(eA),className:"space-y-6",children:[(0,i.jsxs)(o.Zp,{children:[(0,i.jsx)(o.aR,{children:(0,i.jsx)(o.ZB,{children:"Reseller Information"})}),(0,i.jsx)(o.Wu,{children:(0,i.jsx)("div",{className:"space-y-4",children:(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Submitting Reseller *"}),(0,i.jsxs)("select",{...J("reseller_id"),className:"w-full p-2 border rounded-md",children:[(0,i.jsx)("option",{value:"",children:"Select a reseller..."}),t.map(e=>(0,i.jsxs)("option",{value:e.id,children:[e.name," (",e.territory,")"]},e.id))]}),ei.reseller_id&&(0,i.jsx)("p",{className:"text-red-500 text-sm mt-1",children:ei.reseller_id.message})]})})})]}),(0,i.jsxs)(o.Zp,{children:[(0,i.jsx)(o.aR,{children:(0,i.jsx)(o.ZB,{children:"End User Information"})}),(0,i.jsx)(o.Wu,{children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Company Name *"}),(0,i.jsx)(u.p,{...J("end_user.company_name"),placeholder:"Enter company name"}),ei.end_user?.company_name&&(0,i.jsx)("p",{className:"text-red-500 text-sm mt-1",children:ei.end_user.company_name.message})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Contact Name *"}),(0,i.jsx)(u.p,{...J("end_user.contact_name"),placeholder:"Enter contact name"}),ei.end_user?.contact_name&&(0,i.jsx)("p",{className:"text-red-500 text-sm mt-1",children:ei.end_user.contact_name.message})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Contact Email *"}),(0,i.jsx)(u.p,{...J("end_user.contact_email"),type:"email",placeholder:"Enter contact email"}),ei.end_user?.contact_email&&(0,i.jsx)("p",{className:"text-red-500 text-sm mt-1",children:ei.end_user.contact_email.message})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Territory *"}),(0,i.jsx)(u.p,{...J("end_user.territory"),placeholder:"Enter territory"}),ei.end_user?.territory&&(0,i.jsx)("p",{className:"text-red-500 text-sm mt-1",children:ei.end_user.territory.message})]})]})})]}),(0,i.jsxs)(o.Zp,{children:[(0,i.jsx)(o.aR,{children:(0,i.jsxs)(o.ZB,{className:"flex items-center justify-between",children:["Products & Pricing",(0,i.jsxs)(l.$,{type:"button",variant:"outline",size:"sm",onClick:()=>es({product_id:"",quantity:1,price:0}),children:[(0,i.jsx)(nd.A,{className:"h-4 w-4 mr-2"}),"Add Product"]})]})}),(0,i.jsx)(o.Wu,{children:(0,i.jsxs)("div",{className:"space-y-4",children:[ea.map((e,t)=>(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 p-4 border rounded-lg",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Product *"}),(0,i.jsxs)("select",{...J(`products.${t}.product_id`),onChange:e=>ez(t,e.target.value),className:"w-full p-2 border rounded-md",children:[(0,i.jsx)("option",{value:"",children:"Select product..."}),m.map(e=>(0,i.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Quantity *"}),(0,i.jsx)(u.p,{...J(`products.${t}.quantity`,{valueAsNumber:!0}),type:"number",min:"1"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Price *"}),(0,i.jsx)(u.p,{...J(`products.${t}.price`,{valueAsNumber:!0}),type:"number",min:"0",step:"0.01"})]}),(0,i.jsx)("div",{className:"flex items-end",children:ea.length>1&&(0,i.jsx)(l.$,{type:"button",variant:"outline",size:"sm",onClick:()=>eu(t),children:(0,i.jsx)(nc.A,{className:"h-4 w-4"})})})]},e.id)),(0,i.jsx)("div",{className:"border-t pt-4",children:(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("span",{className:"text-lg font-medium",children:"Total Value:"}),(0,i.jsx)("span",{className:"text-xl font-bold",children:(0,nl.vv)(ek)})]})})]})})]}),(0,i.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,i.jsx)(l.$,{type:"button",variant:"outline",onClick:()=>e.back(),children:"Cancel"}),(0,i.jsx)(l.$,{type:"submit",disabled:g,children:g?"Submitting...":"Submit Deal Registration"})]})]})]})})}},94735:e=>{"use strict";e.exports=require("events")},96474:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[447,17,328,375,501],()=>r(58862));module.exports=i})();