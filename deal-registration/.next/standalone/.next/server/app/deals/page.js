(()=>{var e={};e.id=242,e.ids=[242],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4610:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>v});var t=r(60687),a=r(43210),i=r(7425),n=r(44493),l=r(29523),d=r(89667),o=r(96834),c=r(4780),p=r(99270),u=r(96474),x=r(43649);let h=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var m=r(85814),g=r.n(m);function v(){let[e,s]=(0,a.useState)([]),[r,m]=(0,a.useState)(!0),[v,j]=(0,a.useState)(""),[f,b]=(0,a.useState)(""),[y,w]=(0,a.useState)(1),[N,P]=(0,a.useState)(1);(0,a.useCallback)(async()=>{try{m(!0);let e=new URLSearchParams({page:y.toString(),limit:"20"});f&&e.append("status",f);let r=await fetch(`/api/deals?${e}`);if(r.ok){let e=await r.json();s(e.data.items),P(e.data.totalPages)}}catch(e){console.error("Error loading deals:",e)}finally{m(!1)}},[y,f]);let _=e.filter(e=>e.end_user.company_name.toLowerCase().includes(v.toLowerCase())||e.reseller.name.toLowerCase().includes(v.toLowerCase())),q=e=>{switch(e){case"pending":return"warning";case"assigned":case"approved":return"success";case"disputed":return"error";default:return"secondary"}};return(0,t.jsx)(i.O,{title:"Deal Registrations",subtitle:"Manage and track all deal registrations",children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(p.A,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400"}),(0,t.jsx)(d.p,{placeholder:"Search deals...",value:v,onChange:e=>j(e.target.value),className:"pl-10 w-64"})]}),(0,t.jsxs)("select",{value:f,onChange:e=>b(e.target.value),className:"px-3 py-2 border rounded-md",children:[(0,t.jsx)("option",{value:"",children:"All Status"}),(0,t.jsx)("option",{value:"pending",children:"Pending"}),(0,t.jsx)("option",{value:"assigned",children:"Assigned"}),(0,t.jsx)("option",{value:"disputed",children:"Disputed"}),(0,t.jsx)("option",{value:"approved",children:"Approved"}),(0,t.jsx)("option",{value:"rejected",children:"Rejected"})]})]}),(0,t.jsx)(g(),{href:"/deals/new",children:(0,t.jsxs)(l.$,{children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"New Deal Registration"]})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{children:"Deal Registrations"})}),(0,t.jsxs)(n.Wu,{children:[r?(0,t.jsx)("div",{className:"text-center py-8",children:"Loading deals..."}):0===_.length?(0,t.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No deals found matching your criteria"}):(0,t.jsx)("div",{className:"space-y-4",children:_.map(e=>(0,t.jsx)("div",{className:"border rounded-lg p-4 hover:bg-gray-50 transition-colors",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,t.jsx)("h3",{className:"font-semibold text-lg",children:e.end_user.company_name}),(0,t.jsx)(o.E,{variant:q(e.status),children:e.status}),e.conflicts.length>0&&(0,t.jsxs)(o.E,{variant:"error",className:"flex items-center",children:[(0,t.jsx)(x.A,{className:"h-3 w-3 mr-1"}),e.conflicts.length," conflict",e.conflicts.length>1?"s":""]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-600",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Submitted by:"}),(0,t.jsx)("br",{}),e.reseller.name]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Territory:"}),(0,t.jsx)("br",{}),e.end_user.territory]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Value:"}),(0,t.jsx)("br",{}),(0,t.jsx)("span",{className:"font-semibold text-green-600",children:(0,c.vv)(e.total_value)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Submitted:"}),(0,t.jsx)("br",{}),(0,c.Yq)(e.submission_date)]})]}),e.assigned_reseller&&(0,t.jsx)("div",{className:"mt-2 text-sm",children:(0,t.jsxs)("span",{className:"font-medium text-green-600",children:["Assigned to: ",e.assigned_reseller.name]})})]}),(0,t.jsx)("div",{className:"flex space-x-2",children:(0,t.jsx)(g(),{href:`/deals/${e.id}`,children:(0,t.jsxs)(l.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(h,{className:"h-4 w-4 mr-2"}),"View"]})})})]})},e.id))}),N>1&&(0,t.jsxs)("div",{className:"flex justify-center space-x-2 mt-6",children:[(0,t.jsx)(l.$,{variant:"outline",onClick:()=>w(e=>Math.max(1,e-1)),disabled:1===y,children:"Previous"}),(0,t.jsxs)("span",{className:"flex items-center px-4",children:["Page ",y," of ",N]}),(0,t.jsx)(l.$,{variant:"outline",onClick:()=>w(e=>Math.min(N,e+1)),disabled:y===N,children:"Next"})]})]})]})]})})}},7444:(e,s,r)=>{Promise.resolve().then(r.bind(r,68564))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60180:(e,s,r)=>{Promise.resolve().then(r.bind(r,4610))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68564:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/deal registration/deal-registration/src/app/deals/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/deal registration/deal-registration/src/app/deals/page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96474:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},99110:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>o});var t=r(65239),a=r(48088),i=r(88170),n=r.n(i),l=r(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(s,d);let o={children:["",{children:["deals",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,68564)),"/home/<USER>/deal registration/deal-registration/src/app/deals/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/home/<USER>/deal registration/deal-registration/src/app/layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"/home/<USER>/deal registration/deal-registration/src/app/error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"/home/<USER>/deal registration/deal-registration/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"/home/<USER>/deal registration/deal-registration/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/deal registration/deal-registration/src/app/deals/page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/deals/page",pathname:"/deals",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,17,328,375,501],()=>r(99110));module.exports=t})();