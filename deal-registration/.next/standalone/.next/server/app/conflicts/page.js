(()=>{var e={};e.id=98,e.ids=[98],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},42772:(e,s,r)=>{Promise.resolve().then(r.bind(r,76191))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},76191:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>f});var t=r(60687),i=r(43210),a=r(7425),n=r(44493),l=r(29523),o=r(96834),d=r(4780),c=r(41312),p=r(62688);let u=(0,p.A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),m=(0,p.A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);var h=r(43649);let x=(0,p.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),g=(0,p.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function f(){let[e,s]=(0,i.useState)([]),[r,p]=(0,i.useState)(!0),[f,v]=(0,i.useState)("pending"),[j,y]=(0,i.useState)(""),_=(0,i.useCallback)(async()=>{try{p(!0);let e=new URLSearchParams({limit:"50"});f&&e.append("resolution_status",f),j&&e.append("conflict_type",j);let r=await fetch(`/api/conflicts?${e}`);if(r.ok){let e=await r.json();s(e.data.items)}}catch(e){console.error("Error loading conflicts:",e)}finally{p(!1)}},[f,j]),b=async(e,s,r)=>{try{(await fetch(`/api/deals/${s}/assign`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({assigned_reseller_id:r,reason:"Conflict resolution"})})).ok&&(await fetch("/api/conflicts",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({conflict_id:e,resolution_status:"resolved"})})).ok&&_()}catch(e){console.error("Error resolving conflict:",e)}},N=async e=>{try{(await fetch("/api/conflicts",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({conflict_id:e,resolution_status:"dismissed"})})).ok&&_()}catch(e){console.error("Error dismissing conflict:",e)}},w=e=>{switch(e){case"duplicate_end_user":return(0,t.jsx)(c.A,{className:"h-5 w-5"});case"territory_overlap":return(0,t.jsx)(u,{className:"h-5 w-5"});case"timing_conflict":return(0,t.jsx)(m,{className:"h-5 w-5"});default:return(0,t.jsx)(h.A,{className:"h-5 w-5"})}},k=e=>{switch(e){case"duplicate_end_user":return"text-red-600 bg-red-100";case"territory_overlap":return"text-orange-600 bg-orange-100";case"timing_conflict":return"text-yellow-600 bg-yellow-100";default:return"text-gray-600 bg-gray-100"}},P=e=>{let s=Math.max(e.deal.total_value,e.competing_deal.total_value),r=Math.floor((new Date().getTime()-new Date(e.created_at).getTime())/864e5);return s>1e5||r>7?"high":s>5e4||r>3?"medium":"low"};return(0,t.jsx)(a.O,{title:"Deal Conflicts",subtitle:"Resolve conflicts and assign deals to appropriate resellers",children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsxs)("select",{value:f,onChange:e=>v(e.target.value),className:"px-3 py-2 border rounded-md",children:[(0,t.jsx)("option",{value:"",children:"All Status"}),(0,t.jsx)("option",{value:"pending",children:"Pending"}),(0,t.jsx)("option",{value:"resolved",children:"Resolved"}),(0,t.jsx)("option",{value:"dismissed",children:"Dismissed"})]}),(0,t.jsxs)("select",{value:j,onChange:e=>y(e.target.value),className:"px-3 py-2 border rounded-md",children:[(0,t.jsx)("option",{value:"",children:"All Types"}),(0,t.jsx)("option",{value:"duplicate_end_user",children:"Duplicate End User"}),(0,t.jsx)("option",{value:"territory_overlap",children:"Territory Overlap"}),(0,t.jsx)("option",{value:"timing_conflict",children:"Timing Conflict"})]})]}),(0,t.jsx)("div",{className:"space-y-4",children:r?(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"text-center py-8",children:"Loading conflicts..."})}):0===e.length?(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"text-center py-8 text-gray-500",children:"No conflicts found matching your criteria"})}):e.map(e=>{let s=P(e);return(0,t.jsxs)(n.Zp,{className:"border-l-4 border-l-orange-400",children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:`p-2 rounded-full ${k(e.conflict_type)}`,children:w(e.conflict_type)}),(0,t.jsxs)("div",{children:[(0,t.jsx)(n.ZB,{className:"text-lg",children:(0,d.ND)(e.conflict_type.replace("_"," "))}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.deal.end_user.company_name})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(o.E,{variant:"high"===s?"error":"medium"===s?"warning":"secondary",children:[s," priority"]}),(0,t.jsx)(o.E,{variant:"pending"===e.resolution_status?"warning":"success",children:e.resolution_status})]})]})}),(0,t.jsxs)(n.Wu,{children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"font-semibold mb-3 text-blue-600",children:"Deal #1"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"End User:"})," ",e.deal.end_user.company_name]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Reseller:"})," ",e.deal.reseller.name]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Territory:"})," ",e.deal.reseller.territory]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Value:"})," ",(0,d.vv)(e.deal.total_value)]})]}),"pending"===e.resolution_status&&(0,t.jsxs)(l.$,{className:"w-full mt-3",size:"sm",onClick:()=>b(e.id,e.deal.id,e.deal.reseller.id),children:[(0,t.jsx)(x,{className:"h-4 w-4 mr-2"}),"Assign to This Reseller"]})]}),(0,t.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"font-semibold mb-3 text-green-600",children:"Deal #2"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"End User:"})," ",e.competing_deal.end_user.company_name]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Reseller:"})," ",e.competing_deal.reseller.name]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Territory:"})," ",e.competing_deal.reseller.territory]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Value:"})," ",(0,d.vv)(e.competing_deal.total_value)]})]}),"pending"===e.resolution_status&&(0,t.jsxs)(l.$,{className:"w-full mt-3",size:"sm",onClick:()=>b(e.id,e.competing_deal.id,e.competing_deal.reseller.id),children:[(0,t.jsx)(x,{className:"h-4 w-4 mr-2"}),"Assign to This Reseller"]})]})]}),"pending"===e.resolution_status&&(0,t.jsx)("div",{className:"flex justify-center mt-4",children:(0,t.jsxs)(l.$,{variant:"outline",onClick:()=>N(e.id),children:[(0,t.jsx)(g,{className:"h-4 w-4 mr-2"}),"Dismiss Conflict"]})}),(0,t.jsxs)("div",{className:"mt-4 text-xs text-gray-500",children:["Conflict detected on ",(0,d.Yq)(e.created_at)]})]})]},e.id)})})]})})}},77636:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/deal registration/deal-registration/src/app/conflicts/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/deal registration/deal-registration/src/app/conflicts/page.tsx","default")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79814:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var t=r(65239),i=r(48088),a=r(88170),n=r.n(a),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(s,o);let d={children:["",{children:["conflicts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,77636)),"/home/<USER>/deal registration/deal-registration/src/app/conflicts/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/home/<USER>/deal registration/deal-registration/src/app/layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"/home/<USER>/deal registration/deal-registration/src/app/error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"/home/<USER>/deal registration/deal-registration/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"/home/<USER>/deal registration/deal-registration/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/deal registration/deal-registration/src/app/conflicts/page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/conflicts/page",pathname:"/conflicts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},81630:e=>{"use strict";e.exports=require("http")},90036:(e,s,r)=>{Promise.resolve().then(r.bind(r,77636))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,17,328,375,501],()=>r(79814));module.exports=t})();