(()=>{var e={};e.id=146,e.ids=[146],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},54898:(e,t,r)=>{"use strict";r.d(t,{Rw:()=>m,lp:()=>p,uM:()=>c,yA:()=>l});var i=r(87916);let s=i.k5(["gold","silver","bronze"]),a=i.k5(["active","inactive"]),o=i.k5(["pending","assigned","disputed","approved","rejected"]),n=i.k5(["duplicate_end_user","territory_overlap","timing_conflict"]),u=i.k5(["pending","resolved","dismissed"]),d=i.k5(["admin","manager","staff"]),l=i.Ik({id:i.Yj().uuid().optional(),name:i.Yj().min(1,"Reseller name is required"),email:i.Yj().email("Valid email is required"),territory:i.Yj().min(1,"Territory is required"),tier:s,status:a.default("active"),created_at:i.Yj().optional(),updated_at:i.Yj().optional()});i.Ik({id:i.Yj().uuid().optional(),company_name:i.Yj().min(1,"Company name is required"),contact_name:i.Yj().min(1,"Contact name is required"),contact_email:i.Yj().email("Valid email is required"),territory:i.Yj().min(1,"Territory is required"),created_at:i.Yj().optional(),updated_at:i.Yj().optional()});let p=i.Ik({id:i.Yj().uuid().optional(),name:i.Yj().min(1,"Product name is required"),category:i.Yj().min(1,"Category is required"),list_price:i.ai().positive("Price must be positive"),created_at:i.Yj().optional(),updated_at:i.Yj().optional()});i.Ik({id:i.Yj().uuid().optional(),deal_id:i.Yj().uuid().optional(),product_id:i.Yj().uuid(),quantity:i.ai().int().positive("Quantity must be positive"),price:i.ai().positive("Price must be positive"),created_at:i.Yj().optional()}),i.Ik({id:i.Yj().uuid().optional(),reseller_id:i.Yj().uuid(),end_user_id:i.Yj().uuid(),assigned_reseller_id:i.Yj().uuid().nullable().optional(),status:o.default("pending"),total_value:i.ai().positive("Total value must be positive"),submission_date:i.Yj().optional(),assignment_date:i.Yj().nullable().optional(),created_at:i.Yj().optional(),updated_at:i.Yj().optional()}),i.Ik({id:i.Yj().uuid().optional(),deal_id:i.Yj().uuid(),competing_deal_id:i.Yj().uuid(),conflict_type:n,resolution_status:u.default("pending"),assigned_to_staff:i.Yj().uuid().nullable().optional(),created_at:i.Yj().optional(),updated_at:i.Yj().optional()}),i.Ik({id:i.Yj().uuid().optional(),email:i.Yj().email("Valid email is required"),name:i.Yj().min(1,"Name is required"),role:d.default("staff"),created_at:i.Yj().optional(),updated_at:i.Yj().optional()});let c=i.Ik({reseller_id:i.Yj().uuid("Please select a reseller"),end_user:i.Ik({id:i.Yj().uuid().optional(),company_name:i.Yj().min(1,"Company name is required"),contact_name:i.Yj().min(1,"Contact name is required"),contact_email:i.Yj().email("Valid email is required"),territory:i.Yj().min(1,"Territory is required")}),products:i.YO(i.Ik({product_id:i.Yj().uuid("Please select a product"),quantity:i.ai().int().positive("Quantity must be positive"),price:i.ai().positive("Price must be positive")})).min(1,"At least one product is required")}),m=i.Ik({deal_id:i.Yj().uuid(),assigned_reseller_id:i.Yj().uuid(),reason:i.Yj().optional()})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,r)=>{"use strict";r.d(t,{Wz:()=>o,Zf:()=>n});var i=r(66437);r(98766);let s="https://xnyyanfulgvcsgjiyfyb.supabase.co",a="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhueXlhbmZ1bGd2Y3Nnaml5ZnliIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIzNTgzMTIsImV4cCI6MjA2NzkzNDMxMn0.iOR4d3e3UROwqkKZIOTFHDXuK4vVLs9LaSOj2Thz2WE",o=()=>{if(!s||!a)throw Error("Missing Supabase environment variables");return(0,i.UU)(s,a)},n=()=>{let e=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!s||!e)throw Error("Missing Supabase environment variables");return(0,i.UU)(s,e,{auth:{autoRefreshToken:!1,persistSession:!1}})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65545:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>_,routeModule:()=>c,serverHooks:()=>Y,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>j});var i={};r.r(i),r.d(i,{GET:()=>l,POST:()=>p});var s=r(96559),a=r(48088),o=r(37719),n=r(32190),u=r(56621),d=r(54898);async function l(e){try{let t=(0,u.Wz)(),{searchParams:r}=new URL(e.url),i=parseInt(r.get("page")||"1"),s=parseInt(r.get("limit")||"50"),a=r.get("category"),o=r.get("search"),d=(i-1)*s,l=t.from("products").select("*").order("name",{ascending:!0}).range(d,d+s-1);a&&(l=l.eq("category",a)),o&&(l=l.ilike("name",`%${o}%`));let{data:p,error:c}=await l;if(c)return console.error("Error fetching products:",c),n.NextResponse.json({error:"Failed to fetch products",details:c.message},{status:500});let{count:m}=await t.from("products").select("*",{count:"exact",head:!0});return n.NextResponse.json({data:{items:p||[],total:m||0,page:i,limit:s,totalPages:Math.ceil((m||0)/s)},success:!0,error:null})}catch(e){return console.error("Unexpected error:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}async function p(e){try{let t=(0,u.Wz)(),r=await e.json(),i=d.lp.safeParse(r);if(!i.success)return n.NextResponse.json({error:"Invalid request data",details:i.error.issues},{status:400});let s=i.data,{data:a,error:o}=await t.from("products").insert(s).select().single();if(o)return console.error("Error creating product:",o),n.NextResponse.json({error:"Failed to create product",details:o.message},{status:500});return n.NextResponse.json({data:a,success:!0,error:null},{status:201})}catch(e){return console.error("Unexpected error:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/products/route",pathname:"/api/products",filename:"route",bundlePath:"app/api/products/route"},resolvedPagePath:"/home/<USER>/deal registration/deal-registration/src/app/api/products/route.ts",nextConfigOutput:"standalone",userland:i}),{workAsyncStorage:m,workUnitAsyncStorage:j,serverHooks:Y}=c;function _(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:j})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[447,580,437,766,916],()=>r(65545));module.exports=i})();