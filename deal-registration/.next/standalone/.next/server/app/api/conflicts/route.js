(()=>{var e={};e.id=607,e.ids=[607],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},54290:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>d,serverHooks:()=>_,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{GET:()=>u,PATCH:()=>c});var o=t(96559),n=t(48088),a=t(37719),i=t(32190),l=t(56621);async function u(e){try{let r=(0,l.Wz)(),{searchParams:t}=new URL(e.url),s=parseInt(t.get("page")||"1"),o=parseInt(t.get("limit")||"10"),n=t.get("resolution_status"),a=t.get("conflict_type"),u=t.get("assigned_to_staff"),c=(s-1)*o,d=r.from("deal_conflicts").select(`
        *,
        deal:deals!deal_conflicts_deal_id_fkey(
          *,
          reseller:resellers!deals_reseller_id_fkey(*),
          end_user:end_users(*),
          products:deal_products(
            *,
            product:products(*)
          )
        ),
        competing_deal:deals!deal_conflicts_competing_deal_id_fkey(
          *,
          reseller:resellers!deals_reseller_id_fkey(*),
          end_user:end_users(*),
          products:deal_products(
            *,
            product:products(*)
          )
        ),
        assigned_staff:staff_users(*)
      `).order("created_at",{ascending:!1}).range(c,c+o-1);n&&(d=d.eq("resolution_status",n)),a&&(d=d.eq("conflict_type",a)),u&&(d=d.eq("assigned_to_staff",u));let{data:p,error:f}=await d;if(f)return console.error("Error fetching conflicts:",f),i.NextResponse.json({error:"Failed to fetch conflicts",details:f.message},{status:500});let{count:_}=await r.from("deal_conflicts").select("*",{count:"exact",head:!0});return i.NextResponse.json({data:{items:p||[],total:_||0,page:s,limit:o,totalPages:Math.ceil((_||0)/o)},success:!0,error:null})}catch(e){return console.error("Unexpected error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function c(e){try{let r=(0,l.Wz)(),{conflict_id:t,resolution_status:s,assigned_to_staff:o}=await e.json();if(!t)return i.NextResponse.json({error:"Conflict ID is required"},{status:400});let n={};s&&(n.resolution_status=s),void 0!==o&&(n.assigned_to_staff=o),n.updated_at=new Date().toISOString();let{data:a,error:u}=await r.from("deal_conflicts").update(n).eq("id",t).select(`
        *,
        deal:deals!deal_conflicts_deal_id_fkey(
          *,
          reseller:resellers(*),
          end_user:end_users(*)
        ),
        competing_deal:deals!deal_conflicts_competing_deal_id_fkey(
          *,
          reseller:resellers(*),
          end_user:end_users(*)
        ),
        assigned_staff:staff_users(*)
      `).single();if(u)return console.error("Error updating conflict:",u),i.NextResponse.json({error:"Failed to update conflict",details:u.message},{status:500});return i.NextResponse.json({data:a,success:!0,error:null})}catch(e){return console.error("Unexpected error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/conflicts/route",pathname:"/api/conflicts",filename:"route",bundlePath:"app/api/conflicts/route"},resolvedPagePath:"/home/<USER>/deal registration/deal-registration/src/app/api/conflicts/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:f,serverHooks:_}=d;function g(){return(0,a.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:f})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,r,t)=>{"use strict";t.d(r,{Wz:()=>a,Zf:()=>i});var s=t(66437);t(98766);let o="https://xnyyanfulgvcsgjiyfyb.supabase.co",n="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhueXlhbmZ1bGd2Y3Nnaml5ZnliIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIzNTgzMTIsImV4cCI6MjA2NzkzNDMxMn0.iOR4d3e3UROwqkKZIOTFHDXuK4vVLs9LaSOj2Thz2WE",a=()=>{if(!o||!n)throw Error("Missing Supabase environment variables");return(0,s.UU)(o,n)},i=()=>{let e=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!o||!e)throw Error("Missing Supabase environment variables");return(0,s.UU)(o,e,{auth:{autoRefreshToken:!1,persistSession:!1}})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580,437,766],()=>t(54290));module.exports=s})();