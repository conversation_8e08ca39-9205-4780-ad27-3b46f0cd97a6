(()=>{var e={};e.id=675,e.ids=[675],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(e,t,r)=>{"use strict";r.d(t,{Ag:()=>d,Qr:()=>o,cn:()=>a,hJ:()=>c,mY:()=>l,nN:()=>u,vv:()=>n});var i=r(75986),s=r(8974);function a(...e){return(0,s.QP)((0,i.$)(e))}function n(e){return new Intl.NumberFormat("en-GB",{style:"currency",currency:"GBP"}).format(e)}function o(e,t){let r=e.length>t.length?e:t,i=e.length>t.length?t:e;if(0===r.length)return 1;let s=function(e,t){let r=[];for(let e=0;e<=t.length;e++)r[e]=[e];for(let t=0;t<=e.length;t++)r[0][t]=t;for(let i=1;i<=t.length;i++)for(let s=1;s<=e.length;s++)t.charAt(i-1)===e.charAt(s-1)?r[i][s]=r[i-1][s-1]:r[i][s]=Math.min(r[i-1][s-1]+1,r[i][s-1]+1,r[i-1][s]+1);return r[t.length][e.length]}(r,i);return(r.length-s)/r.length}function l(e){return e.toLowerCase().replace(/\b(inc|corp|corporation|ltd|limited|llc|co)\b\.?/g,"").replace(/[^\w\s]/g,"").replace(/\s+/g," ").trim()}function d(e,t){let r=e.toLowerCase().trim(),i=t.toLowerCase().trim();if(r===i)return!0;for(let e of[/^(north|south|east|west)\s*(america|us|usa|united states)$/,/^(northeast|northwest|southeast|southwest)\s*(region|territory)?$/,/^(global|worldwide|international)$/,/^(enterprise|commercial|federal)$/])if(e.test(r)&&e.test(i))return!0;return!1}function u(e,t,r=.1){return Math.abs(e-t)/((e+t)/2)<=r}function c(e,t,r=90){let i=new Date(e);return Math.ceil(Math.abs(new Date(t).getTime()-i.getTime())/864e5)<=r}},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},54898:(e,t,r)=>{"use strict";r.d(t,{Rw:()=>m,lp:()=>c,uM:()=>p,yA:()=>u});var i=r(87916);let s=i.k5(["gold","silver","bronze"]),a=i.k5(["active","inactive"]),n=i.k5(["pending","assigned","disputed","approved","rejected"]),o=i.k5(["duplicate_end_user","territory_overlap","timing_conflict"]),l=i.k5(["pending","resolved","dismissed"]),d=i.k5(["admin","manager","staff"]),u=i.Ik({id:i.Yj().uuid().optional(),name:i.Yj().min(1,"Reseller name is required"),email:i.Yj().email("Valid email is required"),territory:i.Yj().min(1,"Territory is required"),tier:s,status:a.default("active"),created_at:i.Yj().optional(),updated_at:i.Yj().optional()});i.Ik({id:i.Yj().uuid().optional(),company_name:i.Yj().min(1,"Company name is required"),contact_name:i.Yj().min(1,"Contact name is required"),contact_email:i.Yj().email("Valid email is required"),territory:i.Yj().min(1,"Territory is required"),created_at:i.Yj().optional(),updated_at:i.Yj().optional()});let c=i.Ik({id:i.Yj().uuid().optional(),name:i.Yj().min(1,"Product name is required"),category:i.Yj().min(1,"Category is required"),list_price:i.ai().positive("Price must be positive"),created_at:i.Yj().optional(),updated_at:i.Yj().optional()});i.Ik({id:i.Yj().uuid().optional(),deal_id:i.Yj().uuid().optional(),product_id:i.Yj().uuid(),quantity:i.ai().int().positive("Quantity must be positive"),price:i.ai().positive("Price must be positive"),created_at:i.Yj().optional()}),i.Ik({id:i.Yj().uuid().optional(),reseller_id:i.Yj().uuid(),end_user_id:i.Yj().uuid(),assigned_reseller_id:i.Yj().uuid().nullable().optional(),status:n.default("pending"),total_value:i.ai().positive("Total value must be positive"),submission_date:i.Yj().optional(),assignment_date:i.Yj().nullable().optional(),created_at:i.Yj().optional(),updated_at:i.Yj().optional()}),i.Ik({id:i.Yj().uuid().optional(),deal_id:i.Yj().uuid(),competing_deal_id:i.Yj().uuid(),conflict_type:o,resolution_status:l.default("pending"),assigned_to_staff:i.Yj().uuid().nullable().optional(),created_at:i.Yj().optional(),updated_at:i.Yj().optional()}),i.Ik({id:i.Yj().uuid().optional(),email:i.Yj().email("Valid email is required"),name:i.Yj().min(1,"Name is required"),role:d.default("staff"),created_at:i.Yj().optional(),updated_at:i.Yj().optional()});let p=i.Ik({reseller_id:i.Yj().uuid("Please select a reseller"),end_user:i.Ik({id:i.Yj().uuid().optional(),company_name:i.Yj().min(1,"Company name is required"),contact_name:i.Yj().min(1,"Contact name is required"),contact_email:i.Yj().email("Valid email is required"),territory:i.Yj().min(1,"Territory is required")}),products:i.YO(i.Ik({product_id:i.Yj().uuid("Please select a product"),quantity:i.ai().int().positive("Quantity must be positive"),price:i.ai().positive("Price must be positive")})).min(1,"At least one product is required")}),m=i.Ik({deal_id:i.Yj().uuid(),assigned_reseller_id:i.Yj().uuid(),reason:i.Yj().optional()})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,r)=>{"use strict";r.d(t,{Wz:()=>n,Zf:()=>o});var i=r(66437);r(98766);let s="https://xnyyanfulgvcsgjiyfyb.supabase.co",a="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhueXlhbmZ1bGd2Y3Nnaml5ZnliIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIzNTgzMTIsImV4cCI6MjA2NzkzNDMxMn0.iOR4d3e3UROwqkKZIOTFHDXuK4vVLs9LaSOj2Thz2WE",n=()=>{if(!s||!a)throw Error("Missing Supabase environment variables");return(0,i.UU)(s,a)},o=()=>{let e=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!s||!e)throw Error("Missing Supabase environment variables");return(0,i.UU)(s,e,{auth:{autoRefreshToken:!1,persistSession:!1}})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},98191:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>_,serverHooks:()=>h,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>g});var i={};r.r(i),r.d(i,{GET:()=>p,POST:()=>m});var s=r(96559),a=r(48088),n=r(37719),o=r(32190),l=r(56621),d=r(54898),u=r(10974);class c{async detectConflicts(e){let t=[],r=[];try{let{data:i,error:s}=await this.supabase.from("deals").select(`
          *,
          end_user:end_users(*),
          reseller:resellers(*)
        `).neq("status","rejected").order("created_at",{ascending:!1}).limit(1e3);if(s)return console.error("Error fetching existing deals:",s),{hasConflicts:!1,conflicts:[],suggestions:[]};if(!i)return{hasConflicts:!1,conflicts:[],suggestions:[]};for(let r of i){let i=await this.checkDealConflicts(e,r);t.push(...i)}return t.length>0&&r.push(...this.generateSuggestions(t)),{hasConflicts:t.length>0,conflicts:this.prioritizeConflicts(t),suggestions:r}}catch(e){return console.error("Conflict detection error:",e),{hasConflicts:!1,conflicts:[],suggestions:[]}}}async checkDealConflicts(e,t){let r=[],i=this.checkEndUserConflict(e,t);i&&r.push(i);let s=this.checkTerritoryConflict(e,t);s&&r.push(s);let a=this.checkTimingConflict(e,t);return a&&r.push(a),r}checkEndUserConflict(e,t){let r=(0,u.mY)(e.end_user.company_name),i=(0,u.mY)(t.end_user.company_name),s=(0,u.Qr)(r,i);if(s>=.85){let r=(0,u.Qr)(e.end_user.contact_email.toLowerCase(),t.end_user.contact_email.toLowerCase()),i="medium",a=`Similar company name: "${e.end_user.company_name}" vs "${t.end_user.company_name}"`;return s>=.95||r>=.8?(i="high",a=`Potential duplicate: ${a}`):e.reseller_id===t.reseller_id&&(i="high",a=`Same reseller submitting for same end user: ${a}`),{type:"duplicate_end_user",severity:i,conflictingDeal:t,reason:a,similarity:s}}return null}checkTerritoryConflict(e,t){return e.reseller_id===t.reseller_id?null:(0,u.Ag)(e.end_user.territory,t.end_user.territory)?{type:"territory_overlap",severity:(0,u.Qr)((0,u.mY)(e.end_user.company_name),(0,u.mY)(t.end_user.company_name))>=.7?"high":"medium",conflictingDeal:t,reason:`Territory overlap: "${e.end_user.territory}" overlaps with "${t.end_user.territory}"`}:null}checkTimingConflict(e,t){let r=e.submission_date||new Date().toISOString(),i=t.submission_date||t.created_at||new Date().toISOString();if(!(0,u.hJ)(r,i,90))return null;let s=(0,u.Qr)((0,u.mY)(e.end_user.company_name),(0,u.mY)(t.end_user.company_name)),a=(0,u.nN)(e.total_value,t.total_value,.2);if(s>=.7&&a){let e=Math.abs((new Date(r).getTime()-new Date(i).getTime())/864e5),s="low";return e<=7?s="high":e<=30&&(s="medium"),{type:"timing_conflict",severity:s,conflictingDeal:t,reason:`Similar deal submitted ${Math.round(e)} days ago for similar end user and value`}}return null}prioritizeConflicts(e){return e.sort((e,t)=>{let r={high:3,medium:2,low:1},i=r[t.severity]-r[e.severity];if(0!==i)return i;let s={duplicate_end_user:3,territory_overlap:2,timing_conflict:1},a=s[t.type]-s[e.type];if(0!==a)return a;let n=e.similarity||0;return(t.similarity||0)-n})}generateSuggestions(e){let t=[],r=e.filter(e=>"high"===e.severity),i=e.filter(e=>"duplicate_end_user"===e.type),s=e.filter(e=>"territory_overlap"===e.type);return r.length>0&&t.push("⚠️ High-priority conflicts detected - requires immediate review"),i.length>0&&(t.push("\uD83D\uDD0D Verify if this is a duplicate submission for the same end user"),t.push("\uD83D\uDCDE Contact the reseller to confirm deal details")),s.length>0&&(t.push("\uD83D\uDDFA️ Review territory assignments and partner agreements"),t.push("⚖️ Consider first-come-first-served or partner tier priority")),e.length>2&&t.push("\uD83D\uDCCB Multiple conflicts detected - consider escalating to management"),t}async createConflictRecords(e,t){if(0===t.length)return;let r=t.map(t=>({deal_id:e,competing_deal_id:t.conflictingDeal.id,conflict_type:t.type,resolution_status:"pending"})),{error:i}=await this.supabase.from("deal_conflicts").insert(r);if(i)throw console.error("Error creating conflict records:",i),i}constructor(){this.supabase=(0,l.Zf)()}}async function p(e){try{let t=(0,l.Wz)(),{searchParams:r}=new URL(e.url),i=parseInt(r.get("page")||"1"),s=parseInt(r.get("limit")||"10"),a=r.get("status"),n=r.get("reseller_id"),d=r.get("territory"),u=r.get("has_conflicts"),c=(i-1)*s,p=t.from("deals").select(`
        *,
        reseller:resellers!deals_reseller_id_fkey(*),
        end_user:end_users(*),
        assigned_reseller:resellers!deals_assigned_reseller_id_fkey(*),
        products:deal_products(
          *,
          product:products(*)
        ),
        conflicts:deal_conflicts!deal_conflicts_deal_id_fkey(
          *,
          competing_deal:deals!deal_conflicts_competing_deal_id_fkey(
            *,
            reseller:resellers!deals_reseller_id_fkey(*),
            end_user:end_users(*)
          )
        )
      `).order("created_at",{ascending:!1}).range(c,c+s-1);a&&(p=p.eq("status",a)),n&&(p=p.eq("reseller_id",n)),d&&(p=p.eq("end_users.territory",d)),"true"===u&&(p=p.not("conflicts","is",null));let{data:m,error:_}=await p;if(_)return console.error("Error fetching deals:",_),o.NextResponse.json({error:"Failed to fetch deals",details:_.message},{status:500});let{count:f}=await t.from("deals").select("*",{count:"exact",head:!0});return o.NextResponse.json({data:{items:m||[],total:f||0,page:i,limit:s,totalPages:Math.ceil((f||0)/s)},success:!0,error:null})}catch(e){return console.error("Unexpected error:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}async function m(e){try{let t=(0,l.Wz)(),r=await e.json(),i=d.uM.safeParse(r);if(!i.success)return o.NextResponse.json({error:"Invalid request data",details:i.error.issues},{status:400});let s=i.data,{data:a,error:n}=await t.from("end_users").upsert({id:s.end_user.id,company_name:s.end_user.company_name,contact_name:s.end_user.contact_name,contact_email:s.end_user.contact_email,territory:s.end_user.territory}).select().single();if(n)return console.error("Error creating/updating end user:",n),o.NextResponse.json({error:"Failed to create end user",details:n.message},{status:500});let u=s.products.reduce((e,t)=>e+t.quantity*t.price,0),{data:p,error:m}=await t.from("deals").insert({reseller_id:s.reseller_id,end_user_id:a.id,total_value:u,status:"pending"}).select().single();if(m)return console.error("Error creating deal:",m),o.NextResponse.json({error:"Failed to create deal",details:m.message},{status:500});let _=s.products.map(e=>({deal_id:p.id,product_id:e.product_id,quantity:e.quantity,price:e.price})),{error:f}=await t.from("deal_products").insert(_);if(f)return console.error("Error creating deal products:",f),await t.from("deals").delete().eq("id",p.id),o.NextResponse.json({error:"Failed to create deal products",details:f.message},{status:500});let g=new c,h=await g.detectConflicts({end_user:a,reseller_id:s.reseller_id,total_value:u,submission_date:p.created_at});h.hasConflicts&&(await g.createConflictRecords(p.id,h.conflicts),h.conflicts.some(e=>"high"===e.severity)&&await t.from("deals").update({status:"disputed"}).eq("id",p.id));let{data:y,error:j}=await t.from("deals").select(`
        *,
        reseller:resellers(*),
        end_user:end_users(*),
        products:deal_products(
          *,
          product:products(*)
        ),
        conflicts:deal_conflicts(
          *,
          competing_deal:deals!deal_conflicts_competing_deal_id_fkey(
            *,
            reseller:resellers(*),
            end_user:end_users(*)
          )
        )
      `).eq("id",p.id).single();if(j)return console.error("Error fetching complete deal:",j),o.NextResponse.json({error:"Deal created but failed to fetch details",details:j.message},{status:500});return o.NextResponse.json({data:{deal:y,conflicts:h},success:!0,error:null},{status:201})}catch(e){return console.error("Unexpected error:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}let _=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/deals/route",pathname:"/api/deals",filename:"route",bundlePath:"app/api/deals/route"},resolvedPagePath:"/home/<USER>/deal registration/deal-registration/src/app/api/deals/route.ts",nextConfigOutput:"standalone",userland:i}),{workAsyncStorage:f,workUnitAsyncStorage:g,serverHooks:h}=_;function y(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:g})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[447,17,580,437,766,916],()=>r(98191));module.exports=i})();