(()=>{var e={};e.id=859,e.ids=[859],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5745:(e,t,r)=>{"use strict";var i,s=Object.defineProperty,a=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,n=Object.prototype.hasOwnProperty,l={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(l,{createBrowserSupabaseClient:()=>j,createClientComponentClient:()=>d,createMiddlewareClient:()=>b,createMiddlewareSupabaseClient:()=>A,createPagesBrowserClient:()=>u,createPagesServerClient:()=>m,createRouteHandlerClient:()=>N,createServerActionClient:()=>S,createServerComponentClient:()=>C,createServerSupabaseClient:()=>O}),e.exports=((e,t,r,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let r of o(t))n.call(e,r)||void 0===r||s(e,r,{get:()=>t[r],enumerable:!(i=a(t,r))||i.enumerable});return e})(s({},"__esModule",{value:!0}),l);var c=r(32353);function d({supabaseUrl:e="https://xnyyanfulgvcsgjiyfyb.supabase.co",supabaseKey:t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhueXlhbmZ1bGd2Y3Nnaml5ZnliIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIzNTgzMTIsImV4cCI6MjA2NzkzNDMxMn0.iOR4d3e3UROwqkKZIOTFHDXuK4vVLs9LaSOj2Thz2WE",options:r,cookieOptions:s,isSingleton:a=!0}={}){if(!e||!t)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");let o=()=>{var i;return(0,c.createSupabaseClient)(e,t,{...r,global:{...null==r?void 0:r.global,headers:{...null==(i=null==r?void 0:r.global)?void 0:i.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.10.0"}},auth:{storage:new c.BrowserCookieAuthStorageAdapter(s)}})};if(a){let e=i??o();return"undefined"==typeof window?e:(i||(i=e),i)}return o()}var u=d,p=r(32353),h=r(28356),f=class extends p.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t,r,i;return(0,h.splitCookiesString)((null==(r=null==(t=this.context.res)?void 0:t.getHeader("set-cookie"))?void 0:r.toString())??"").map(t=>(0,p.parseCookies)(t)[e]).find(e=>!!e)??(null==(i=this.context.req)?void 0:i.cookies[e])}setCookie(e,t){this._setCookie(e,t)}deleteCookie(e){this._setCookie(e,"",{maxAge:0})}_setCookie(e,t,r){var i;let s=(0,h.splitCookiesString)((null==(i=this.context.res.getHeader("set-cookie"))?void 0:i.toString())??"").filter(t=>!(e in(0,p.parseCookies)(t))),a=(0,p.serializeCookie)(e,t,{...this.cookieOptions,...r,httpOnly:!1});this.context.res.setHeader("set-cookie",[...s,a])}};function m(e,{supabaseUrl:t="https://xnyyanfulgvcsgjiyfyb.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhueXlhbmZ1bGd2Y3Nnaml5ZnliIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIzNTgzMTIsImV4cCI6MjA2NzkzNDMxMn0.iOR4d3e3UROwqkKZIOTFHDXuK4vVLs9LaSOj2Thz2WE",options:i,cookieOptions:s}={}){var a;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,p.createSupabaseClient)(t,r,{...i,global:{...null==i?void 0:i.global,headers:{...null==(a=null==i?void 0:i.global)?void 0:a.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.10.0"}},auth:{storage:new f(e,s)}})}var g=r(32353),x=r(28356),v=class extends g.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t;let r=(0,x.splitCookiesString)((null==(t=this.context.res.headers.get("set-cookie"))?void 0:t.toString())??"").map(t=>(0,g.parseCookies)(t)[e]).find(e=>!!e);return r||(0,g.parseCookies)(this.context.req.headers.get("cookie")??"")[e]}setCookie(e,t){this._setCookie(e,t)}deleteCookie(e){this._setCookie(e,"",{maxAge:0})}_setCookie(e,t,r){let i=(0,g.serializeCookie)(e,t,{...this.cookieOptions,...r,httpOnly:!1});this.context.res.headers&&this.context.res.headers.append("set-cookie",i)}};function b(e,{supabaseUrl:t="https://xnyyanfulgvcsgjiyfyb.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhueXlhbmZ1bGd2Y3Nnaml5ZnliIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIzNTgzMTIsImV4cCI6MjA2NzkzNDMxMn0.iOR4d3e3UROwqkKZIOTFHDXuK4vVLs9LaSOj2Thz2WE",options:i,cookieOptions:s}={}){var a;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,g.createSupabaseClient)(t,r,{...i,global:{...null==i?void 0:i.global,headers:{...null==(a=null==i?void 0:i.global)?void 0:a.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.10.0"}},auth:{storage:new v(e,s)}})}var y=r(32353),I=class extends y.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e,this.isServer=!0}getCookie(e){var t;return null==(t=this.context.cookies().get(e))?void 0:t.value}setCookie(e,t){}deleteCookie(e){}};function C(e,{supabaseUrl:t="https://xnyyanfulgvcsgjiyfyb.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhueXlhbmZ1bGd2Y3Nnaml5ZnliIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIzNTgzMTIsImV4cCI6MjA2NzkzNDMxMn0.iOR4d3e3UROwqkKZIOTFHDXuK4vVLs9LaSOj2Thz2WE",options:i,cookieOptions:s}={}){var a;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,y.createSupabaseClient)(t,r,{...i,global:{...null==i?void 0:i.global,headers:{...null==(a=null==i?void 0:i.global)?void 0:a.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.10.0"}},auth:{storage:new I(e,s)}})}var k=r(32353),w=class extends k.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t;return null==(t=this.context.cookies().get(e))?void 0:t.value}setCookie(e,t){this.context.cookies().set(e,t,this.cookieOptions)}deleteCookie(e){this.context.cookies().set(e,"",{...this.cookieOptions,maxAge:0})}};function N(e,{supabaseUrl:t="https://xnyyanfulgvcsgjiyfyb.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhueXlhbmZ1bGd2Y3Nnaml5ZnliIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIzNTgzMTIsImV4cCI6MjA2NzkzNDMxMn0.iOR4d3e3UROwqkKZIOTFHDXuK4vVLs9LaSOj2Thz2WE",options:i,cookieOptions:s}={}){var a;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,k.createSupabaseClient)(t,r,{...i,global:{...null==i?void 0:i.global,headers:{...null==(a=null==i?void 0:i.global)?void 0:a.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.10.0"}},auth:{storage:new w(e,s)}})}var S=N;function j({supabaseUrl:e="https://xnyyanfulgvcsgjiyfyb.supabase.co",supabaseKey:t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhueXlhbmZ1bGd2Y3Nnaml5ZnliIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIzNTgzMTIsImV4cCI6MjA2NzkzNDMxMn0.iOR4d3e3UROwqkKZIOTFHDXuK4vVLs9LaSOj2Thz2WE",options:r,cookieOptions:i}={}){return console.warn("Please utilize the `createPagesBrowserClient` function instead of the deprecated `createBrowserSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages"),u({supabaseUrl:e,supabaseKey:t,options:r,cookieOptions:i})}function O(e,{supabaseUrl:t="https://xnyyanfulgvcsgjiyfyb.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhueXlhbmZ1bGd2Y3Nnaml5ZnliIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIzNTgzMTIsImV4cCI6MjA2NzkzNDMxMn0.iOR4d3e3UROwqkKZIOTFHDXuK4vVLs9LaSOj2Thz2WE",options:i,cookieOptions:s}={}){return console.warn("Please utilize the `createPagesServerClient` function instead of the deprecated `createServerSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages"),m(e,{supabaseUrl:t,supabaseKey:r,options:i,cookieOptions:s})}function A(e,{supabaseUrl:t="https://xnyyanfulgvcsgjiyfyb.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhueXlhbmZ1bGd2Y3Nnaml5ZnliIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIzNTgzMTIsImV4cCI6MjA2NzkzNDMxMn0.iOR4d3e3UROwqkKZIOTFHDXuK4vVLs9LaSOj2Thz2WE",options:i,cookieOptions:s}={}){return console.warn("Please utilize the `createMiddlewareClient` function instead of the deprecated `createMiddlewareSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs#middleware"),b(e,{supabaseUrl:t,supabaseKey:r,options:i,cookieOptions:s})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},15254:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var i=r(65239),s=r(48088),a=r(88170),o=r.n(a),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c={children:["",{children:["auth",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,81351)),"/home/<USER>/deal registration/deal-registration/src/app/auth/login/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/home/<USER>/deal registration/deal-registration/src/app/layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"/home/<USER>/deal registration/deal-registration/src/app/error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"/home/<USER>/deal registration/deal-registration/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"/home/<USER>/deal registration/deal-registration/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/home/<USER>/deal registration/deal-registration/src/app/auth/login/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new i.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/auth/login/page",pathname:"/auth/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28356:e=>{"use strict";var t={decodeValues:!0,map:!1,silent:!1};function r(e){return"string"==typeof e&&!!e.trim()}function i(e,i){var s,a,o,n,l=e.split(";").filter(r),c=(s=l.shift(),a="",o="",(n=s.split("=")).length>1?(a=n.shift(),o=n.join("=")):o=s,{name:a,value:o}),d=c.name,u=c.value;i=i?Object.assign({},t,i):t;try{u=i.decodeValues?decodeURIComponent(u):u}catch(e){console.error("set-cookie-parser encountered an error while decoding a cookie with value '"+u+"'. Set options.decodeValues to false to disable this feature.",e)}var p={name:d,value:u};return l.forEach(function(e){var t=e.split("="),r=t.shift().trimLeft().toLowerCase(),i=t.join("=");"expires"===r?p.expires=new Date(i):"max-age"===r?p.maxAge=parseInt(i,10):"secure"===r?p.secure=!0:"httponly"===r?p.httpOnly=!0:"samesite"===r?p.sameSite=i:"partitioned"===r?p.partitioned=!0:p[r]=i}),p}function s(e,s){if(s=s?Object.assign({},t,s):t,!e)if(!s.map)return[];else return{};if(e.headers)if("function"==typeof e.headers.getSetCookie)e=e.headers.getSetCookie();else if(e.headers["set-cookie"])e=e.headers["set-cookie"];else{var a=e.headers[Object.keys(e.headers).find(function(e){return"set-cookie"===e.toLowerCase()})];a||!e.headers.cookie||s.silent||console.warn("Warning: set-cookie-parser appears to have been called on a request object. It is designed to parse Set-Cookie headers from responses, not Cookie headers from requests. Set the option {silent: true} to suppress this warning."),e=a}return(Array.isArray(e)||(e=[e]),s.map)?e.filter(r).reduce(function(e,t){var r=i(t,s);return e[r.name]=r,e},{}):e.filter(r).map(function(e){return i(e,s)})}e.exports=s,e.exports.parse=s,e.exports.parseString=i,e.exports.splitCookiesString=function(e){if(Array.isArray(e))return e;if("string"!=typeof e)return[];var t,r,i,s,a,o=[],n=0;function l(){for(;n<e.length&&/\s/.test(e.charAt(n));)n+=1;return n<e.length}for(;n<e.length;){for(t=n,a=!1;l();)if(","===(r=e.charAt(n))){for(i=n,n+=1,l(),s=n;n<e.length&&"="!==(r=e.charAt(n))&&";"!==r&&","!==r;)n+=1;n<e.length&&"="===e.charAt(n)?(a=!0,n=s,o.push(e.substring(t,i)),t=n):n=i+1}else n+=1;(!a||n>=e.length)&&o.push(e.substring(t,e.length))}return o}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32353:(e,t,r)=>{"use strict";r.r(t),r.d(t,{BrowserCookieAuthStorageAdapter:()=>w,CookieAuthStorageAdapter:()=>k,DEFAULT_COOKIE_OPTIONS:()=>I,createSupabaseClient:()=>N,isBrowser:()=>y,parseCookies:()=>S,parseSupabaseCookie:()=>v,serializeCookie:()=>j,stringifySupabaseSession:()=>b});var i=r(79428);new TextEncoder;let s=new TextDecoder;i.Buffer.isEncoding("base64url");let a=e=>i.Buffer.from(function(e){let t=e;return t instanceof Uint8Array&&(t=s.decode(t)),t}(e),"base64");var o=r(60463),n=Object.create,l=Object.defineProperty,c=Object.getOwnPropertyDescriptor,d=Object.getOwnPropertyNames,u=Object.getPrototypeOf,p=Object.prototype.hasOwnProperty,h=(e,t,r,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of d(t))p.call(e,s)||s===r||l(e,s,{get:()=>t[s],enumerable:!(i=c(t,s))||i.enumerable});return e},f=(e,t,r)=>(r=null!=e?n(u(e)):{},h(!t&&e&&e.__esModule?r:l(r,"default",{value:e,enumerable:!0}),e)),m=((e,t)=>function(){return t||(0,e[d(e)[0]])((t={exports:{}}).exports,t),t.exports})({"../../node_modules/.pnpm/cookie@0.5.0/node_modules/cookie/index.js"(e){e.parse=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");for(var r={},s=(t||{}).decode||i,a=0;a<e.length;){var o=e.indexOf("=",a);if(-1===o)break;var n=e.indexOf(";",a);if(-1===n)n=e.length;else if(n<o){a=e.lastIndexOf(";",o-1)+1;continue}var l=e.slice(a,o).trim();if(void 0===r[l]){var c=e.slice(o+1,n).trim();34===c.charCodeAt(0)&&(c=c.slice(1,-1)),r[l]=function(e,t){try{return t(e)}catch(t){return e}}(c,s)}a=n+1}return r},e.serialize=function(e,i,a){var o=a||{},n=o.encode||s;if("function"!=typeof n)throw TypeError("option encode is invalid");if(!r.test(e))throw TypeError("argument name is invalid");var l=n(i);if(l&&!r.test(l))throw TypeError("argument val is invalid");var c=e+"="+l;if(null!=o.maxAge){var d=o.maxAge-0;if(isNaN(d)||!isFinite(d))throw TypeError("option maxAge is invalid");c+="; Max-Age="+Math.floor(d)}if(o.domain){if(!r.test(o.domain))throw TypeError("option domain is invalid");c+="; Domain="+o.domain}if(o.path){if(!r.test(o.path))throw TypeError("option path is invalid");c+="; Path="+o.path}if(o.expires){var u,p=o.expires;if(u=p,"[object Date]"!==t.call(u)&&!(u instanceof Date)||isNaN(p.valueOf()))throw TypeError("option expires is invalid");c+="; Expires="+p.toUTCString()}if(o.httpOnly&&(c+="; HttpOnly"),o.secure&&(c+="; Secure"),o.priority)switch("string"==typeof o.priority?o.priority.toLowerCase():o.priority){case"low":c+="; Priority=Low";break;case"medium":c+="; Priority=Medium";break;case"high":c+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(o.sameSite)switch("string"==typeof o.sameSite?o.sameSite.toLowerCase():o.sameSite){case!0:case"strict":c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"none":c+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return c};var t=Object.prototype.toString,r=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;function i(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}function s(e){return encodeURIComponent(e)}}}),g=f(m()),x=f(m());function v(e){if(!e)return null;try{let t=JSON.parse(e);if(!t)return null;if("Object"===t.constructor.name)return t;if("Array"!==t.constructor.name)throw Error(`Unexpected format: ${t.constructor.name}`);let[r,i,s]=t[0].split("."),o=a(i),n=new TextDecoder,{exp:l,sub:c,...d}=JSON.parse(n.decode(o));return{expires_at:l,expires_in:l-Math.round(Date.now()/1e3),token_type:"bearer",access_token:t[0],refresh_token:t[1],provider_token:t[2],provider_refresh_token:t[3],user:{id:c,factors:t[4],...d}}}catch(e){return console.warn("Failed to parse cookie string:",e),null}}function b(e){var t;return JSON.stringify([e.access_token,e.refresh_token,e.provider_token,e.provider_refresh_token,(null==(t=e.user)?void 0:t.factors)??null])}function y(){return"undefined"!=typeof window&&void 0!==window.document}var I={path:"/",sameSite:"lax",maxAge:31536e6},C=RegExp(".{1,3180}","g"),k=class{constructor(e){this.cookieOptions={...I,...e,maxAge:I.maxAge}}getItem(e){let t=this.getCookie(e);if(e.endsWith("-code-verifier")&&t)return t;if(t)return JSON.stringify(v(t));let r=function(e,t=()=>null){let r=[];for(let i=0;;i++){let s=t(`${e}.${i}`);if(!s)break;r.push(s)}return r.length?r.join(""):null}(e,e=>this.getCookie(e));return null!==r?JSON.stringify(v(r)):null}setItem(e,t){if(e.endsWith("-code-verifier"))return void this.setCookie(e,t);(function(e,t,r){if(1===Math.ceil(t.length/((void 0)??3180)))return[{name:e,value:t}];let i=[],s=t.match(C);return null==s||s.forEach((t,r)=>{let s=`${e}.${r}`;i.push({name:s,value:t})}),i})(e,b(JSON.parse(t))).forEach(e=>{this.setCookie(e.name,e.value)})}removeItem(e){this._deleteSingleCookie(e),this._deleteChunkedCookies(e)}_deleteSingleCookie(e){this.getCookie(e)&&this.deleteCookie(e)}_deleteChunkedCookies(e,t=0){for(let r=t;;r++){let t=`${e}.${r}`;if(void 0===this.getCookie(t))break;this.deleteCookie(t)}}},w=class extends k{constructor(e){super(e)}getCookie(e){return y()?(0,g.parse)(document.cookie)[e]:null}setCookie(e,t){if(!y())return null;document.cookie=(0,g.serialize)(e,t,{...this.cookieOptions,httpOnly:!1})}deleteCookie(e){if(!y())return null;document.cookie=(0,g.serialize)(e,"",{...this.cookieOptions,maxAge:0,httpOnly:!1})}};function N(e,t,r){var i;let s=y();return(0,o.UU)(e,t,{...r,auth:{flowType:"pkce",autoRefreshToken:s,detectSessionInUrl:s,persistSession:!0,storage:r.auth.storage,...(null==(i=r.auth)?void 0:i.storageKey)?{storageKey:r.auth.storageKey}:{}}})}var S=x.parse,j=x.serialize},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44493:(e,t,r)=>{"use strict";r.d(t,{Wu:()=>c,ZB:()=>l,Zp:()=>o,aR:()=>n});var i=r(60687),s=r(43210),a=r(4780);let o=s.forwardRef(({className:e,...t},r)=>(0,i.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border border-gray-200 bg-white text-gray-900 shadow-sm",e),...t}));o.displayName="Card";let n=s.forwardRef(({className:e,...t},r)=>(0,i.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...t}));n.displayName="CardHeader";let l=s.forwardRef(({className:e,...t},r)=>(0,i.jsx)("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle",s.forwardRef(({className:e,...t},r)=>(0,i.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-gray-600",e),...t})).displayName="CardDescription";let c=s.forwardRef(({className:e,...t},r)=>(0,i.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent",s.forwardRef(({className:e,...t},r)=>(0,i.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},52828:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var i=r(60687),s=r(43210),a=r(5745),o=r(16189),n=r(44493),l=r(29523),c=r(89667),d=r(24224),u=r(4780);let p=(0,d.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),h=s.forwardRef(({className:e,variant:t,...r},s)=>(0,i.jsx)("div",{ref:s,role:"alert",className:(0,u.cn)(p({variant:t}),e),...r}));h.displayName="Alert",s.forwardRef(({className:e,...t},r)=>(0,i.jsx)("h5",{ref:r,className:(0,u.cn)("mb-1 font-medium leading-none tracking-tight",e),...t})).displayName="AlertTitle";let f=s.forwardRef(({className:e,...t},r)=>(0,i.jsx)("div",{ref:r,className:(0,u.cn)("text-sm [&_p]:leading-relaxed",e),...t}));f.displayName="AlertDescription";var m=r(62688);let g=(0,m.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]),x=(0,m.A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]),v=(0,m.A)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]]);function b(){let[e,t]=(0,s.useState)(""),[r,d]=(0,s.useState)(""),[u,p]=(0,s.useState)(!1),[m,b]=(0,s.useState)(null),[y,I]=(0,s.useState)(null),C=(0,o.useRouter)(),k=(0,a.createClientComponentClient)(),w=async t=>{t.preventDefault(),p(!0),b(null);try{let{error:t}=await k.auth.signInWithPassword({email:e,password:r});t?b(t.message):(C.push("/"),C.refresh())}catch{b("An unexpected error occurred")}finally{p(!1)}},N=async()=>{p(!0),b(null);try{let{error:e}=await k.auth.signInWithOAuth({provider:"google",options:{redirectTo:`${window.location.origin}/auth/callback`}});e&&(b(e.message),p(!1))}catch{b("An unexpected error occurred"),p(!1)}},S=async()=>{if(!e)return void b("Please enter your email address");p(!0),b(null);try{let{error:t}=await k.auth.signInWithOtp({email:e,options:{emailRedirectTo:`${window.location.origin}/auth/callback`}});t?b(t.message):I("Check your email for the magic link!")}catch{b("An unexpected error occurred")}finally{p(!1)}};return(0,i.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,i.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("h2",{className:"mt-6 text-3xl font-bold text-gray-900",children:"Deal Registration System"}),(0,i.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Sign in to your account"})]}),(0,i.jsxs)(n.Zp,{children:[(0,i.jsx)(n.aR,{children:(0,i.jsx)(n.ZB,{className:"text-center",children:"Sign In"})}),(0,i.jsxs)(n.Wu,{className:"space-y-6",children:[m&&(0,i.jsx)(h,{variant:"destructive",children:(0,i.jsx)(f,{children:m})}),y&&(0,i.jsx)(h,{children:(0,i.jsx)(f,{children:y})}),(0,i.jsxs)(l.$,{onClick:N,disabled:u,className:"w-full",variant:"outline",children:[(0,i.jsxs)("svg",{className:"w-5 h-5 mr-2",viewBox:"0 0 24 24",children:[(0,i.jsx)("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,i.jsx)("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,i.jsx)("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,i.jsx)("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Continue with Google"]}),(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,i.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,i.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,i.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]}),(0,i.jsxs)("form",{onSubmit:w,className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"email",className:"sr-only",children:"Email address"}),(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(g,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400"}),(0,i.jsx)(c.p,{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"pl-10",placeholder:"Email address",value:e,onChange:e=>t(e.target.value)})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"password",className:"sr-only",children:"Password"}),(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(x,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400"}),(0,i.jsx)(c.p,{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,className:"pl-10",placeholder:"Password",value:r,onChange:e=>d(e.target.value)})]})]}),(0,i.jsxs)(l.$,{type:"submit",disabled:u,className:"w-full",children:[(0,i.jsx)(v,{className:"w-4 h-4 mr-2"}),u?"Signing in...":"Sign in"]})]}),(0,i.jsx)("div",{className:"text-center",children:(0,i.jsx)(l.$,{variant:"link",onClick:S,disabled:u||!e,className:"text-sm",children:"Send magic link instead"})}),(0,i.jsxs)("div",{className:"text-center text-sm text-gray-600",children:["Don't have an account?"," ",(0,i.jsx)(l.$,{variant:"link",className:"p-0 h-auto text-sm",children:"Contact your administrator"})]})]})]})]})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77411:(e,t,r)=>{Promise.resolve().then(r.bind(r,52828))},78091:(e,t,r)=>{Promise.resolve().then(r.bind(r,81351))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81351:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/deal registration/deal-registration/src/app/auth/login/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/deal registration/deal-registration/src/app/auth/login/page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>o});var i=r(60687),s=r(43210),a=r(4780);let o=s.forwardRef(({className:e,type:t,...r},s)=>(0,i.jsx)("input",{type:t,className:(0,a.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...r}));o.displayName="Input"},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[447,17,328,375],()=>r(15254));module.exports=i})();