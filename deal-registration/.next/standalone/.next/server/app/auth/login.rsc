1:"$Sreact.fragment"
2:I[3117,["825","static/chunks/825-089fc0b8aa72e472.js","535","static/chunks/535-33f6bffbc4adbbd4.js","177","static/chunks/app/layout-49bc6b7fa0f80c1f.js"],"AuthProvider"]
3:I[7555,[],""]
4:I[1901,["497","static/chunks/497-719dd3a186306a38.js","39","static/chunks/app/error-9b335b5295dba09e.js"],"default"]
5:I[1295,[],""]
6:I[6874,["874","static/chunks/874-38d41a3904c5bca2.js","345","static/chunks/app/not-found-c633564aef4b9321.js"],""]
7:I[894,[],"ClientPageRoot"]
8:I[9178,["825","static/chunks/825-089fc0b8aa72e472.js","497","static/chunks/497-719dd3a186306a38.js","328","static/chunks/328-9b6dbdeb7f4633d4.js","859","static/chunks/app/auth/login/page-07be355436ac15f4.js"],"default"]
b:I[9665,[],"OutletBoundary"]
e:I[4911,[],"AsyncMetadataOutlet"]
10:I[9665,[],"ViewportBoundary"]
12:I[9665,[],"MetadataBoundary"]
14:I[6614,[],""]
:HL["/_next/static/media/e4af272ccee01ff0-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/css/a4becaf290fa60d5.css","style"]
0:{"P":null,"b":"fLprcXhzNjSlnQPGlfDm-","p":"","c":["","auth","login"],"i":false,"f":[[["",{"children":["auth",{"children":["login",{"children":["__PAGE__",{}]}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/a4becaf290fa60d5.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","className":"__variable_e8ce0c","children":[["$","head",null,{"children":[["$","link",null,{"rel":"preconnect","href":"https://fonts.googleapis.com"}],["$","link",null,{"rel":"preconnect","href":"https://fonts.gstatic.com","crossOrigin":"anonymous"}],["$","meta",null,{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta",null,{"name":"theme-color","content":"#000000"}]]}],["$","body",null,{"className":"__className_e8ce0c antialiased","children":["$","$L2",null,{"children":["$","$L3",null,{"parallelRouterKey":"children","error":"$4","errorStyles":[],"errorScripts":[],"template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","div",null,{"className":"min-h-screen flex items-center justify-center bg-gray-50","children":["$","div",null,{"className":"text-center max-w-md mx-auto p-6","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-file-x w-16 h-16 text-gray-400 mx-auto mb-4","aria-hidden":"true","children":[["$","path","1rqfz7",{"d":"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"}],["$","path","tnqrlb",{"d":"M14 2v4a2 2 0 0 0 2 2h4"}],["$","path","b62r18",{"d":"m14.5 12.5-5 5"}],["$","path","1rk7el",{"d":"m9.5 12.5 5 5"}],"$undefined"]}],["$","h2",null,{"className":"text-2xl font-bold text-gray-900 mb-2","children":"Page Not Found"}],["$","p",null,{"className":"text-gray-600 mb-6","children":"Sorry, we couldn't find the page you're looking for."}],["$","div",null,{"className":"space-y-3","children":[["$","$L6",null,{"href":"/","children":"Go to Dashboard","className":"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-blue-600 text-white hover:bg-blue-700 h-10 px-4 py-2 w-full","ref":null}],["$","$L6",null,{"href":"/deals","children":"View Deals","className":"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 bg-white hover:bg-gray-50 h-10 px-4 py-2 w-full","ref":null}]]}]]}]}],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]]}]]}],{"children":["auth",["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["login",["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L7",null,{"Component":"$8","searchParams":{},"params":{},"promises":["$@9","$@a"]}],null,["$","$Lb",null,{"children":["$Lc","$Ld",["$","$Le",null,{"promise":"$@f"}]]}]]}],{},null,false]},null,false]},null,false]},[["$","div","l",{"className":"min-h-screen flex items-center justify-center bg-gray-50","children":["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"loading-spinner w-12 h-12 mx-auto mb-4"}],["$","h2",null,{"className":"text-lg font-semibold text-gray-900 mb-2","children":"Loading..."}],["$","p",null,{"className":"text-gray-600","children":"Please wait while we load your content."}]]}]}],[],[]],false],["$","$1","h",{"children":[null,["$","$1","7wxeV82U_5DeKKCcBGNkVv",{"children":[["$","$L10",null,{"children":"$L11"}],["$","meta",null,{"name":"next-size-adjust","content":""}]]}],["$","$L12",null,{"children":"$L13"}]]}],false]],"m":"$undefined","G":["$14","$undefined"],"s":false,"S":true}
15:"$Sreact.suspense"
16:I[4911,[],"AsyncMetadata"]
9:{}
a:{}
13:["$","div",null,{"hidden":true,"children":["$","$15",null,{"fallback":null,"children":["$","$L16",null,{"promise":"$@17"}]}]}]
d:null
11:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
c:null
f:{"metadata":[["$","title","0",{"children":"Deal Registration System"}],["$","meta","1",{"name":"description","content":"Comprehensive deal registration system with conflict detection, built with Next.js, Supabase, and Google OAuth"}],["$","meta","2",{"name":"author","content":"Deal Registration Team"}],["$","meta","3",{"name":"keywords","content":"deal registration,conflict detection,sales management,CRM"}],["$","meta","4",{"name":"creator","content":"Deal Registration System"}],["$","meta","5",{"name":"publisher","content":"Deal Registration System"}],["$","meta","6",{"name":"robots","content":"index, follow"}],["$","meta","7",{"name":"googlebot","content":"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"}],["$","meta","8",{"property":"og:title","content":"Deal Registration System"}],["$","meta","9",{"property":"og:description","content":"Comprehensive deal registration system with conflict detection"}],["$","meta","10",{"property":"og:url","content":"https://deal-registration-system.vercel.app"}],["$","meta","11",{"property":"og:site_name","content":"Deal Registration System"}],["$","meta","12",{"property":"og:locale","content":"en_US"}],["$","meta","13",{"property":"og:type","content":"website"}],["$","meta","14",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","15",{"name":"twitter:title","content":"Deal Registration System"}],["$","meta","16",{"name":"twitter:description","content":"Comprehensive deal registration system with conflict detection"}],["$","link","17",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}]],"error":null,"digest":"$undefined"}
17:{"metadata":"$f:metadata","error":null,"digest":"$undefined"}
