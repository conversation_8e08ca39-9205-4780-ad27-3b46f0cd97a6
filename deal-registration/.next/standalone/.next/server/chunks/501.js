"use strict";exports.id=501,exports.ids=[501],exports.modules={7425:(e,a,r)=>{r.d(a,{O:()=>d});var t=r(60687),s=r(76830),l=r(56014);function d({children:e,title:a,subtitle:r}){return(0,t.jsxs)("div",{className:"flex h-screen bg-gray-100",children:[(0,t.jsx)(s.Navigation,{}),(0,t.jsxs)("div",{className:"flex flex-1 flex-col overflow-hidden",children:[(0,t.jsx)(<PERSON><PERSON>,{title:a,subtitle:r}),(0,t.jsx)("main",{className:"flex-1 overflow-auto p-6",children:e})]})]})}},17313:(e,a,r)=>{r.d(a,{A:()=>t});let t=(0,r(62688).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},19080:(e,a,r)=>{r.d(a,{A:()=>t});let t=(0,r(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},41312:(e,a,r)=>{r.d(a,{A:()=>t});let t=(0,r(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},44493:(e,a,r)=>{r.d(a,{Wu:()=>c,ZB:()=>n,Zp:()=>d,aR:()=>i});var t=r(60687),s=r(43210),l=r(4780);let d=s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border border-gray-200 bg-white text-gray-900 shadow-sm",e),...a}));d.displayName="Card";let i=s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...a}));i.displayName="CardHeader";let n=s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("h3",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...a}));n.displayName="CardTitle",s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-gray-600",e),...a})).displayName="CardDescription";let c=s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",e),...a}));c.displayName="CardContent",s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",e),...a})).displayName="CardFooter"},56014:(e,a,r)=>{r.d(a,{Header:()=>h});var t=r(60687),s=r(99270),l=r(97051);let d=(0,r(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);var i=r(29523),n=r(89667),c=r(96834);function h({title:e,subtitle:a}){return(0,t.jsx)("header",{className:"border-b bg-white px-6 py-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:e}),a&&(0,t.jsx)("p",{className:"text-sm text-gray-600",children:a})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(s.A,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400"}),(0,t.jsx)(n.p,{placeholder:"Search deals, resellers...",className:"w-64 pl-10"})]}),(0,t.jsxs)(i.$,{variant:"ghost",size:"icon",className:"relative",children:[(0,t.jsx)(l.A,{className:"h-5 w-5"}),(0,t.jsx)(c.E,{variant:"destructive",className:"absolute -right-1 -top-1 h-5 w-5 rounded-full p-0 text-xs",children:"3"})]}),(0,t.jsx)(i.$,{variant:"ghost",size:"icon",children:(0,t.jsx)(d,{className:"h-5 w-5"})})]})]})})}},76830:(e,a,r)=>{r.d(a,{Navigation:()=>b});var t=r(60687),s=r(85814),l=r.n(s),d=r(16189),i=r(4780),n=r(29523),c=r(40565),h=r(62688);let o=(0,h.A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]]),x=(0,h.A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var f=r(43649),y=r(41312),p=r(17313),g=r(19080),m=r(84027);let v=(0,h.A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]),u=[{name:"Dashboard",href:"/",icon:o},{name:"Deal Registration",href:"/deals/new",icon:x},{name:"All Deals",href:"/deals",icon:x},{name:"Conflicts",href:"/conflicts",icon:f.A},{name:"Resellers",href:"/resellers",icon:y.A},{name:"End Users",href:"/end-users",icon:p.A},{name:"Products",href:"/products",icon:g.A},{name:"Settings",href:"/settings",icon:m.A}];function b(){let e=(0,d.usePathname)(),{signOut:a}=(0,c.A)();return(0,t.jsxs)("div",{className:"flex h-full w-64 flex-col bg-gray-900",children:[(0,t.jsx)("div",{className:"flex h-16 items-center px-6",children:(0,t.jsx)("h1",{className:"text-xl font-bold text-white",children:"Deal Registration"})}),(0,t.jsx)("nav",{className:"flex-1 space-y-1 px-3 py-4",children:u.map(a=>{let r=e===a.href||"/"!==a.href&&e.startsWith(a.href);return(0,t.jsxs)(l(),{href:a.href,className:(0,i.cn)("group flex items-center rounded-md px-3 py-2 text-sm font-medium transition-colors",r?"bg-gray-800 text-white":"text-gray-300 hover:bg-gray-700 hover:text-white"),children:[(0,t.jsx)(a.icon,{className:(0,i.cn)("mr-3 h-5 w-5 flex-shrink-0",r?"text-white":"text-gray-400 group-hover:text-white")}),a.name]},a.name)})}),(0,t.jsx)("div",{className:"border-t border-gray-700 p-4",children:(0,t.jsxs)(n.$,{variant:"ghost",className:"w-full justify-start text-gray-300 hover:bg-gray-700 hover:text-white",onClick:a,children:[(0,t.jsx)(v,{className:"mr-3 h-5 w-5"}),"Sign Out"]})})]})}},84027:(e,a,r)=>{r.d(a,{A:()=>t});let t=(0,r(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},89667:(e,a,r)=>{r.d(a,{p:()=>d});var t=r(60687),s=r(43210),l=r(4780);let d=s.forwardRef(({className:e,type:a,...r},s)=>(0,t.jsx)("input",{type:a,className:(0,l.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...r}));d.displayName="Input"},96834:(e,a,r)=>{r.d(a,{E:()=>i});var t=r(60687);r(43210);var s=r(24224),l=r(4780);let d=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-blue-600 text-white hover:bg-blue-700",secondary:"border-transparent bg-gray-100 text-gray-900 hover:bg-gray-200",destructive:"border-transparent bg-red-600 text-white hover:bg-red-700",outline:"text-gray-900 border-gray-300",success:"border-transparent bg-green-100 text-green-800 hover:bg-green-200",warning:"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200",error:"border-transparent bg-red-100 text-red-800 hover:bg-red-200"}},defaultVariants:{variant:"default"}});function i({className:e,variant:a,...r}){return(0,t.jsx)("div",{className:(0,l.cn)(d({variant:a}),e),...r})}},97051:(e,a,r)=>{r.d(a,{A:()=>t});let t=(0,r(62688).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},99270:(e,a,r)=>{r.d(a,{A:()=>t});let t=(0,r(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};