exports.id=375,exports.ids=[375],exports.modules={4780:(e,t,r)=>{"use strict";r.d(t,{ND:()=>l,Yq:()=>a,cn:()=>s,vv:()=>o});var n=r(49384),i=r(82348);function s(...e){return(0,i.QP)((0,n.$)(e))}function o(e){return new Intl.NumberFormat("en-GB",{style:"currency",currency:"GBP"}).format(e)}function a(e){return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(new Date(e))}function l(e){return e.charAt(0).toUpperCase()+e.slice(1)}},7199:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>i});var n=r(12907);(0,n.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/deal registration/deal-registration/src/components/providers/auth-provider.tsx","useAuth");let i=(0,n.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/deal registration/deal-registration/src/components/providers/auth-provider.tsx","AuthProvider")},10974:(e,t,r)=>{"use strict";r.d(t,{Ag:()=>d,Qr:()=>a,cn:()=>s,hJ:()=>u,mY:()=>l,nN:()=>c,vv:()=>o});var n=r(75986),i=r(8974);function s(...e){return(0,i.QP)((0,n.$)(e))}function o(e){return new Intl.NumberFormat("en-GB",{style:"currency",currency:"GBP"}).format(e)}function a(e,t){let r=e.length>t.length?e:t,n=e.length>t.length?t:e;if(0===r.length)return 1;let i=function(e,t){let r=[];for(let e=0;e<=t.length;e++)r[e]=[e];for(let t=0;t<=e.length;t++)r[0][t]=t;for(let n=1;n<=t.length;n++)for(let i=1;i<=e.length;i++)t.charAt(n-1)===e.charAt(i-1)?r[n][i]=r[n-1][i-1]:r[n][i]=Math.min(r[n-1][i-1]+1,r[n][i-1]+1,r[n-1][i]+1);return r[t.length][e.length]}(r,n);return(r.length-i)/r.length}function l(e){return e.toLowerCase().replace(/\b(inc|corp|corporation|ltd|limited|llc|co)\b\.?/g,"").replace(/[^\w\s]/g,"").replace(/\s+/g," ").trim()}function d(e,t){let r=e.toLowerCase().trim(),n=t.toLowerCase().trim();if(r===n)return!0;for(let e of[/^(north|south|east|west)\s*(america|us|usa|united states)$/,/^(northeast|northwest|southeast|southwest)\s*(region|territory)?$/,/^(global|worldwide|international)$/,/^(enterprise|commercial|federal)$/])if(e.test(r)&&e.test(n))return!0;return!1}function c(e,t,r=.1){return Math.abs(e-t)/((e+t)/2)<=r}function u(e,t,r=90){let n=new Date(e);return Math.ceil(Math.abs(new Date(t).getTime()-n.getTime())/864e5)<=r}},12915:(e,t,r)=>{Promise.resolve().then(r.bind(r,40565))},14329:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var n=r(60687);r(43210);var i=r(29523),s=r(43649);function o({error:e,reset:t}){return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,n.jsxs)("div",{className:"text-center max-w-md mx-auto p-6",children:[(0,n.jsx)(s.A,{className:"w-16 h-16 text-red-500 mx-auto mb-4"}),(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Something went wrong!"}),(0,n.jsx)("p",{className:"text-gray-600 mb-6",children:"We apologize for the inconvenience. An error occurred while loading this page."}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsx)(i.$,{onClick:t,className:"w-full",children:"Try again"}),(0,n.jsx)(i.$,{variant:"outline",onClick:()=>window.location.href="/",className:"w-full",children:"Go to Dashboard"})]}),!1]})})}},23469:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var n=r(37413),i=r(61120),s=r(70403),o=r(50662),a=r(10974);let l=(0,o.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700",outline:"border border-gray-300 bg-white hover:bg-gray-50",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",ghost:"hover:bg-gray-100",link:"text-blue-600 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=i.forwardRef(({className:e,variant:t,size:r,asChild:i=!1,...o},d)=>{let c=i?s.DX:"button";return(0,n.jsx)(c,{className:(0,a.cn)(l({variant:t,size:r,className:e})),ref:d,...o})});d.displayName="Button"},24612:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var n=r(60687),i=r(43210),s=r(81391),o=r(24224),a=r(4780);let l=(0,o.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700",outline:"border border-gray-300 bg-white hover:bg-gray-50",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",ghost:"hover:bg-gray-100",link:"text-blue-600 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=i.forwardRef(({className:e,variant:t,size:r,asChild:i=!1,...o},d)=>{let c=i?s.DX:"button";return(0,n.jsx)(c,{className:(0,a.cn)(l({variant:t,size:r,className:e})),ref:d,...o})});d.displayName="Button"},36779:(e,t,r)=>{Promise.resolve().then(r.bind(r,14329))},39727:()=>{},40565:(e,t,r)=>{"use strict";r.d(t,{A:()=>l,AuthProvider:()=>d});var n=r(60687),i=r(43210),s=r(5745),o=r(16189);let a=(0,i.createContext)({user:null,loading:!0,signOut:async()=>{}}),l=()=>{let e=(0,i.useContext)(a);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e};function d({children:e}){let[t,r]=(0,i.useState)(null),[l,d]=(0,i.useState)(!0);(0,o.useRouter)();let c=(0,s.createClientComponentClient)(),u=async()=>{await c.auth.signOut()};return(0,n.jsx)(a.Provider,{value:{user:t,loading:l,signOut:u},children:e})}},42355:(e,t,r)=>{Promise.resolve().then(r.bind(r,54431))},45545:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23))},47990:()=>{},52827:(e,t,r)=>{Promise.resolve().then(r.bind(r,7199))},54413:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var n=r(37413),i=r(4536),s=r.n(i),o=r(23469),a=r(96083);function l(){return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,n.jsxs)("div",{className:"text-center max-w-md mx-auto p-6",children:[(0,n.jsx)(a.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Page Not Found"}),(0,n.jsx)("p",{className:"text-gray-600 mb-6",children:"Sorry, we couldn't find the page you're looking for."}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsx)(o.$,{asChild:!0,className:"w-full",children:(0,n.jsx)(s(),{href:"/",children:"Go to Dashboard"})}),(0,n.jsx)(o.$,{variant:"outline",asChild:!0,className:"w-full",children:(0,n.jsx)(s(),{href:"/deals",children:"View Deals"})})]})]})})}},54431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/deal registration/deal-registration/src/app/error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/deal registration/deal-registration/src/app/error.tsx","default")},55273:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23))},59524:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},61135:()=>{},67393:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(37413);function i(){return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"loading-spinner w-12 h-12 mx-auto mb-4"}),(0,n.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Loading..."}),(0,n.jsx)("p",{className:"text-gray-600",children:"Please wait while we load your content."})]})})}},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},78335:()=>{},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>a});var n=r(37413),i=r(52001),s=r.n(i),o=r(7199);r(61135);let a={title:"Deal Registration System",description:"Comprehensive deal registration system with conflict detection, built with Next.js, Supabase, and Google OAuth",keywords:["deal registration","conflict detection","sales management","CRM"],authors:[{name:"Deal Registration Team"}],creator:"Deal Registration System",publisher:"Deal Registration System",robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},openGraph:{type:"website",locale:"en_US",url:"https://deal-registration-system.vercel.app",title:"Deal Registration System",description:"Comprehensive deal registration system with conflict detection",siteName:"Deal Registration System"},twitter:{card:"summary_large_image",title:"Deal Registration System",description:"Comprehensive deal registration system with conflict detection"}};function l({children:e}){return(0,n.jsxs)("html",{lang:"en",className:s().variable,children:[(0,n.jsxs)("head",{children:[(0,n.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,n.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),(0,n.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,n.jsx)("meta",{name:"theme-color",content:"#000000"})]}),(0,n.jsx)("body",{className:`${s().className} antialiased`,children:(0,n.jsx)(o.AuthProvider,{children:e})})]})}},96487:()=>{}};