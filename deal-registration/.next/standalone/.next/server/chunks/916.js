"use strict";exports.id=916,exports.ids=[916],exports.modules={87916:(e,t,n)=>{function i(e,t,n){function i(n,i){var r;for(let o in Object.defineProperty(n,"_zod",{value:n._zod??{},enumerable:!1}),(r=n._zod).traits??(r.traits=new Set),n._zod.traits.add(e),t(n,i),a.prototype)o in n||Object.defineProperty(n,o,{value:a.prototype[o].bind(n)});n._zod.constr=a,n._zod.def=i}let r=n?.Parent??Object;class o extends r{}function a(e){var t;let r=n?.Parent?new o:this;for(let n of(i(r,e),(t=r._zod).deferred??(t.deferred=[]),r._zod.deferred))n();return r}return Object.defineProperty(o,"name",{value:e}),Object.defineProperty(a,"init",{value:i}),Object.defineProperty(a,Symbol.hasInstance,{value:t=>!!n?.Parent&&t instanceof n.Parent||t?._zod?.traits?.has(e)}),Object.defineProperty(a,"name",{value:e}),a}n.d(t,{EB:()=>tW,YO:()=>nu,k5:()=>nh,ai:()=>nt,Ik:()=>nc,Yj:()=>tL}),Object.freeze({status:"aborted"}),Symbol("zod_brand");class r extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}let o={};function a(e){return e&&Object.assign(o,e),o}let s=/^[cC][^\s-]{8,}$/,u=/^[0-9a-z]+$/,l=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,c=/^[0-9a-vA-V]{20}$/,d=/^[A-Za-z0-9]{27}$/,p=/^[a-zA-Z0-9_-]{21}$/,f=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,h=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,m=e=>e?RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${e}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$/,v=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,_=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,g=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/,y=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,z=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,b=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,k=/^[A-Za-z0-9_-]*$/,w=/^([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+$/,$=/^\+(?:[0-9]){6,14}[0-9]$/,Z="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",I=RegExp(`^${Z}$`);function x(e){let t="(?:[01]\\d|2[0-3]):[0-5]\\d";return"number"==typeof e.precision?-1===e.precision?`${t}`:0===e.precision?`${t}:[0-5]\\d`:`${t}:[0-5]\\d\\.\\d{${e.precision}}`:`${t}(?::[0-5]\\d(?:\\.\\d+)?)?`}let E=e=>{let t=e?`[\\s\\S]{${e?.minimum??0},${e?.maximum??""}}`:"[\\s\\S]*";return RegExp(`^${t}$`)},P=/^\d+$/,T=/^-?\d+(?:\.\d+)?/i,A=/^[^A-Z]*$/,N=/^[^a-z]*$/;function O(e,t){return"bigint"==typeof t?t.toString():t}function S(e){return{get value(){{let t=e();return Object.defineProperty(this,"value",{value:t}),t}}}}function j(e){let t=+!!e.startsWith("^"),n=e.endsWith("$")?e.length-1:e.length;return e.slice(t,n)}function F(e,t,n){Object.defineProperty(e,t,{get(){{let i=n();return e[t]=i,i}},set(n){Object.defineProperty(e,t,{value:n})},configurable:!0})}function C(e,t,n){Object.defineProperty(e,t,{value:n,writable:!0,enumerable:!0,configurable:!0})}function R(e){return JSON.stringify(e)}let U=Error.captureStackTrace?Error.captureStackTrace:(...e)=>{};function D(e){return"object"==typeof e&&null!==e&&!Array.isArray(e)}let M=S(()=>{if("undefined"!=typeof navigator&&navigator?.userAgent?.includes("Cloudflare"))return!1;try{return Function(""),!0}catch(e){return!1}});function V(e){if(!1===D(e))return!1;let t=e.constructor;if(void 0===t)return!0;let n=t.prototype;return!1!==D(n)&&!1!==Object.prototype.hasOwnProperty.call(n,"isPrototypeOf")}let L=new Set(["string","number","symbol"]);function W(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function G(e,t,n){let i=new e._zod.constr(t??e._zod.def);return(!t||n?.parent)&&(i._zod.parent=e),i}function Y(e){if(!e)return{};if("string"==typeof e)return{error:()=>e};if(e?.message!==void 0){if(e?.error!==void 0)throw Error("Cannot specify both `message` and `error` params");e.error=e.message}return(delete e.message,"string"==typeof e.error)?{...e,error:()=>e.error}:e}let B={safeint:[Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],int32:[-0x80000000,0x7fffffff],uint32:[0,0xffffffff],float32:[-34028234663852886e22,34028234663852886e22],float64:[-Number.MAX_VALUE,Number.MAX_VALUE]};function J(e,t=0){for(let n=t;n<e.issues.length;n++)if(e.issues[n]?.continue!==!0)return!0;return!1}function K(e,t){return t.map(t=>(t.path??(t.path=[]),t.path.unshift(e),t))}function X(e){return"string"==typeof e?e:e?.message}function q(e,t,n){let i={...e,path:e.path??[]};return e.message||(i.message=X(e.inst?._zod.def?.error?.(e))??X(t?.error?.(e))??X(n.customError?.(e))??X(n.localeError?.(e))??"Invalid input"),delete i.inst,delete i.continue,t?.reportInput||delete i.input,i}function H(e){return Array.isArray(e)?"array":"string"==typeof e?"string":"unknown"}function Q(...e){let[t,n,i]=e;return"string"==typeof t?{message:t,code:"custom",input:n,inst:i}:{...t}}let ee=i("$ZodCheck",(e,t)=>{var n;e._zod??(e._zod={}),e._zod.def=t,(n=e._zod).onattach??(n.onattach=[])}),et={number:"number",bigint:"bigint",object:"date"},en=i("$ZodCheckLessThan",(e,t)=>{ee.init(e,t);let n=et[typeof t.value];e._zod.onattach.push(e=>{let n=e._zod.bag,i=(t.inclusive?n.maximum:n.exclusiveMaximum)??Number.POSITIVE_INFINITY;t.value<i&&(t.inclusive?n.maximum=t.value:n.exclusiveMaximum=t.value)}),e._zod.check=i=>{(t.inclusive?i.value<=t.value:i.value<t.value)||i.issues.push({origin:n,code:"too_big",maximum:t.value,input:i.value,inclusive:t.inclusive,inst:e,continue:!t.abort})}}),ei=i("$ZodCheckGreaterThan",(e,t)=>{ee.init(e,t);let n=et[typeof t.value];e._zod.onattach.push(e=>{let n=e._zod.bag,i=(t.inclusive?n.minimum:n.exclusiveMinimum)??Number.NEGATIVE_INFINITY;t.value>i&&(t.inclusive?n.minimum=t.value:n.exclusiveMinimum=t.value)}),e._zod.check=i=>{(t.inclusive?i.value>=t.value:i.value>t.value)||i.issues.push({origin:n,code:"too_small",minimum:t.value,input:i.value,inclusive:t.inclusive,inst:e,continue:!t.abort})}}),er=i("$ZodCheckMultipleOf",(e,t)=>{ee.init(e,t),e._zod.onattach.push(e=>{var n;(n=e._zod.bag).multipleOf??(n.multipleOf=t.value)}),e._zod.check=n=>{if(typeof n.value!=typeof t.value)throw Error("Cannot mix number and bigint in multiple_of check.");("bigint"==typeof n.value?n.value%t.value===BigInt(0):0===function(e,t){let n=(e.toString().split(".")[1]||"").length,i=(t.toString().split(".")[1]||"").length,r=n>i?n:i;return Number.parseInt(e.toFixed(r).replace(".",""))%Number.parseInt(t.toFixed(r).replace(".",""))/10**r}(n.value,t.value))||n.issues.push({origin:typeof n.value,code:"not_multiple_of",divisor:t.value,input:n.value,inst:e,continue:!t.abort})}}),eo=i("$ZodCheckNumberFormat",(e,t)=>{ee.init(e,t),t.format=t.format||"float64";let n=t.format?.includes("int"),i=n?"int":"number",[r,o]=B[t.format];e._zod.onattach.push(e=>{let i=e._zod.bag;i.format=t.format,i.minimum=r,i.maximum=o,n&&(i.pattern=P)}),e._zod.check=a=>{let s=a.value;if(n){if(!Number.isInteger(s))return void a.issues.push({expected:i,format:t.format,code:"invalid_type",input:s,inst:e});if(!Number.isSafeInteger(s))return void(s>0?a.issues.push({input:s,code:"too_big",maximum:Number.MAX_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:e,origin:i,continue:!t.abort}):a.issues.push({input:s,code:"too_small",minimum:Number.MIN_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:e,origin:i,continue:!t.abort}))}s<r&&a.issues.push({origin:"number",input:s,code:"too_small",minimum:r,inclusive:!0,inst:e,continue:!t.abort}),s>o&&a.issues.push({origin:"number",input:s,code:"too_big",maximum:o,inst:e})}}),ea=i("$ZodCheckMaxLength",(e,t)=>{var n;ee.init(e,t),(n=e._zod.def).when??(n.when=e=>{let t=e.value;return null!=t&&void 0!==t.length}),e._zod.onattach.push(e=>{let n=e._zod.bag.maximum??Number.POSITIVE_INFINITY;t.maximum<n&&(e._zod.bag.maximum=t.maximum)}),e._zod.check=n=>{let i=n.value;if(i.length<=t.maximum)return;let r=H(i);n.issues.push({origin:r,code:"too_big",maximum:t.maximum,inclusive:!0,input:i,inst:e,continue:!t.abort})}}),es=i("$ZodCheckMinLength",(e,t)=>{var n;ee.init(e,t),(n=e._zod.def).when??(n.when=e=>{let t=e.value;return null!=t&&void 0!==t.length}),e._zod.onattach.push(e=>{let n=e._zod.bag.minimum??Number.NEGATIVE_INFINITY;t.minimum>n&&(e._zod.bag.minimum=t.minimum)}),e._zod.check=n=>{let i=n.value;if(i.length>=t.minimum)return;let r=H(i);n.issues.push({origin:r,code:"too_small",minimum:t.minimum,inclusive:!0,input:i,inst:e,continue:!t.abort})}}),eu=i("$ZodCheckLengthEquals",(e,t)=>{var n;ee.init(e,t),(n=e._zod.def).when??(n.when=e=>{let t=e.value;return null!=t&&void 0!==t.length}),e._zod.onattach.push(e=>{let n=e._zod.bag;n.minimum=t.length,n.maximum=t.length,n.length=t.length}),e._zod.check=n=>{let i=n.value,r=i.length;if(r===t.length)return;let o=H(i),a=r>t.length;n.issues.push({origin:o,...a?{code:"too_big",maximum:t.length}:{code:"too_small",minimum:t.length},inclusive:!0,exact:!0,input:n.value,inst:e,continue:!t.abort})}}),el=i("$ZodCheckStringFormat",(e,t)=>{var n,i;ee.init(e,t),e._zod.onattach.push(e=>{let n=e._zod.bag;n.format=t.format,t.pattern&&(n.patterns??(n.patterns=new Set),n.patterns.add(t.pattern))}),t.pattern?(n=e._zod).check??(n.check=n=>{t.pattern.lastIndex=0,t.pattern.test(n.value)||n.issues.push({origin:"string",code:"invalid_format",format:t.format,input:n.value,...t.pattern?{pattern:t.pattern.toString()}:{},inst:e,continue:!t.abort})}):(i=e._zod).check??(i.check=()=>{})}),ec=i("$ZodCheckRegex",(e,t)=>{el.init(e,t),e._zod.check=n=>{t.pattern.lastIndex=0,t.pattern.test(n.value)||n.issues.push({origin:"string",code:"invalid_format",format:"regex",input:n.value,pattern:t.pattern.toString(),inst:e,continue:!t.abort})}}),ed=i("$ZodCheckLowerCase",(e,t)=>{t.pattern??(t.pattern=A),el.init(e,t)}),ep=i("$ZodCheckUpperCase",(e,t)=>{t.pattern??(t.pattern=N),el.init(e,t)}),ef=i("$ZodCheckIncludes",(e,t)=>{ee.init(e,t);let n=W(t.includes),i=new RegExp("number"==typeof t.position?`^.{${t.position}}${n}`:n);t.pattern=i,e._zod.onattach.push(e=>{let t=e._zod.bag;t.patterns??(t.patterns=new Set),t.patterns.add(i)}),e._zod.check=n=>{n.value.includes(t.includes,t.position)||n.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:t.includes,input:n.value,inst:e,continue:!t.abort})}}),eh=i("$ZodCheckStartsWith",(e,t)=>{ee.init(e,t);let n=RegExp(`^${W(t.prefix)}.*`);t.pattern??(t.pattern=n),e._zod.onattach.push(e=>{let t=e._zod.bag;t.patterns??(t.patterns=new Set),t.patterns.add(n)}),e._zod.check=n=>{n.value.startsWith(t.prefix)||n.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:t.prefix,input:n.value,inst:e,continue:!t.abort})}}),em=i("$ZodCheckEndsWith",(e,t)=>{ee.init(e,t);let n=RegExp(`.*${W(t.suffix)}$`);t.pattern??(t.pattern=n),e._zod.onattach.push(e=>{let t=e._zod.bag;t.patterns??(t.patterns=new Set),t.patterns.add(n)}),e._zod.check=n=>{n.value.endsWith(t.suffix)||n.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:t.suffix,input:n.value,inst:e,continue:!t.abort})}}),ev=i("$ZodCheckOverwrite",(e,t)=>{ee.init(e,t),e._zod.check=e=>{e.value=t.tx(e.value)}});class e_{constructor(e=[]){this.content=[],this.indent=0,this&&(this.args=e)}indented(e){this.indent+=1,e(this),this.indent-=1}write(e){if("function"==typeof e){e(this,{execution:"sync"}),e(this,{execution:"async"});return}let t=e.split("\n").filter(e=>e),n=Math.min(...t.map(e=>e.length-e.trimStart().length));for(let e of t.map(e=>e.slice(n)).map(e=>" ".repeat(2*this.indent)+e))this.content.push(e)}compile(){return Function(...this?.args,[...(this?.content??[""]).map(e=>`  ${e}`)].join("\n"))}}let eg=(e,t)=>{e.name="$ZodError",Object.defineProperty(e,"_zod",{value:e._zod,enumerable:!1}),Object.defineProperty(e,"issues",{value:t,enumerable:!1}),Object.defineProperty(e,"message",{get:()=>JSON.stringify(t,O,2),enumerable:!0}),Object.defineProperty(e,"toString",{value:()=>e.message,enumerable:!1})},ey=i("$ZodError",eg),ez=i("$ZodError",eg,{Parent:Error}),eb=e=>(t,n,i)=>{let o=i?{...i,async:!1}:{async:!1},s=t._zod.run({value:n,issues:[]},o);if(s instanceof Promise)throw new r;return s.issues.length?{success:!1,error:new(e??ey)(s.issues.map(e=>q(e,o,a())))}:{success:!0,data:s.value}},ek=eb(ez),ew=e=>async(t,n,i)=>{let r=i?Object.assign(i,{async:!0}):{async:!0},o=t._zod.run({value:n,issues:[]},r);return o instanceof Promise&&(o=await o),o.issues.length?{success:!1,error:new e(o.issues.map(e=>q(e,r,a())))}:{success:!0,data:o.value}},e$=ew(ez),eZ={major:4,minor:0,patch:5},eI=i("$ZodType",(e,t)=>{var n;e??(e={}),e._zod.def=t,e._zod.bag=e._zod.bag||{},e._zod.version=eZ;let i=[...e._zod.def.checks??[]];for(let t of(e._zod.traits.has("$ZodCheck")&&i.unshift(e),i))for(let n of t._zod.onattach)n(e);if(0===i.length)(n=e._zod).deferred??(n.deferred=[]),e._zod.deferred?.push(()=>{e._zod.run=e._zod.parse});else{let t=(e,t,n)=>{let i,o=J(e);for(let a of t){if(a._zod.def.when){if(!a._zod.def.when(e))continue}else if(o)continue;let t=e.issues.length,s=a._zod.check(e);if(s instanceof Promise&&n?.async===!1)throw new r;if(i||s instanceof Promise)i=(i??Promise.resolve()).then(async()=>{await s,e.issues.length!==t&&(o||(o=J(e,t)))});else{if(e.issues.length===t)continue;o||(o=J(e,t))}}return i?i.then(()=>e):e};e._zod.run=(n,o)=>{let a=e._zod.parse(n,o);if(a instanceof Promise){if(!1===o.async)throw new r;return a.then(e=>t(e,i,o))}return t(a,i,o)}}e["~standard"]={validate:t=>{try{let n=ek(e,t);return n.success?{value:n.data}:{issues:n.error?.issues}}catch(n){return e$(e,t).then(e=>e.success?{value:e.data}:{issues:e.error?.issues})}},vendor:"zod",version:1}}),ex=i("$ZodString",(e,t)=>{eI.init(e,t),e._zod.pattern=[...e?._zod.bag?.patterns??[]].pop()??E(e._zod.bag),e._zod.parse=(n,i)=>{if(t.coerce)try{n.value=String(n.value)}catch(e){}return"string"==typeof n.value||n.issues.push({expected:"string",code:"invalid_type",input:n.value,inst:e}),n}}),eE=i("$ZodStringFormat",(e,t)=>{el.init(e,t),ex.init(e,t)}),eP=i("$ZodGUID",(e,t)=>{t.pattern??(t.pattern=h),eE.init(e,t)}),eT=i("$ZodUUID",(e,t)=>{if(t.version){let e={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[t.version];if(void 0===e)throw Error(`Invalid UUID version: "${t.version}"`);t.pattern??(t.pattern=m(e))}else t.pattern??(t.pattern=m());eE.init(e,t)}),eA=i("$ZodEmail",(e,t)=>{t.pattern??(t.pattern=v),eE.init(e,t)}),eN=i("$ZodURL",(e,t)=>{eE.init(e,t),e._zod.check=n=>{try{let i=n.value,r=new URL(i),o=r.href;t.hostname&&(t.hostname.lastIndex=0,t.hostname.test(r.hostname)||n.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:w.source,input:n.value,inst:e,continue:!t.abort})),t.protocol&&(t.protocol.lastIndex=0,t.protocol.test(r.protocol.endsWith(":")?r.protocol.slice(0,-1):r.protocol)||n.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:t.protocol.source,input:n.value,inst:e,continue:!t.abort})),!i.endsWith("/")&&o.endsWith("/")?n.value=o.slice(0,-1):n.value=o;return}catch(i){n.issues.push({code:"invalid_format",format:"url",input:n.value,inst:e,continue:!t.abort})}}}),eO=i("$ZodEmoji",(e,t)=>{t.pattern??(t.pattern=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),eE.init(e,t)}),eS=i("$ZodNanoID",(e,t)=>{t.pattern??(t.pattern=p),eE.init(e,t)}),ej=i("$ZodCUID",(e,t)=>{t.pattern??(t.pattern=s),eE.init(e,t)}),eF=i("$ZodCUID2",(e,t)=>{t.pattern??(t.pattern=u),eE.init(e,t)}),eC=i("$ZodULID",(e,t)=>{t.pattern??(t.pattern=l),eE.init(e,t)}),eR=i("$ZodXID",(e,t)=>{t.pattern??(t.pattern=c),eE.init(e,t)}),eU=i("$ZodKSUID",(e,t)=>{t.pattern??(t.pattern=d),eE.init(e,t)}),eD=i("$ZodISODateTime",(e,t)=>{t.pattern??(t.pattern=function(e){let t=x({precision:e.precision}),n=["Z"];e.local&&n.push(""),e.offset&&n.push("([+-]\\d{2}:\\d{2})");let i=`${t}(?:${n.join("|")})`;return RegExp(`^${Z}T(?:${i})$`)}(t)),eE.init(e,t)}),eM=i("$ZodISODate",(e,t)=>{t.pattern??(t.pattern=I),eE.init(e,t)}),eV=i("$ZodISOTime",(e,t)=>{t.pattern??(t.pattern=RegExp(`^${x(t)}$`)),eE.init(e,t)}),eL=i("$ZodISODuration",(e,t)=>{t.pattern??(t.pattern=f),eE.init(e,t)}),eW=i("$ZodIPv4",(e,t)=>{t.pattern??(t.pattern=_),eE.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.format="ipv4"})}),eG=i("$ZodIPv6",(e,t)=>{t.pattern??(t.pattern=g),eE.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.format="ipv6"}),e._zod.check=n=>{try{new URL(`http://[${n.value}]`)}catch{n.issues.push({code:"invalid_format",format:"ipv6",input:n.value,inst:e,continue:!t.abort})}}}),eY=i("$ZodCIDRv4",(e,t)=>{t.pattern??(t.pattern=y),eE.init(e,t)}),eB=i("$ZodCIDRv6",(e,t)=>{t.pattern??(t.pattern=z),eE.init(e,t),e._zod.check=n=>{let[i,r]=n.value.split("/");try{if(!r)throw Error();let e=Number(r);if(`${e}`!==r||e<0||e>128)throw Error();new URL(`http://[${i}]`)}catch{n.issues.push({code:"invalid_format",format:"cidrv6",input:n.value,inst:e,continue:!t.abort})}}});function eJ(e){if(""===e)return!0;if(e.length%4!=0)return!1;try{return atob(e),!0}catch{return!1}}let eK=i("$ZodBase64",(e,t)=>{t.pattern??(t.pattern=b),eE.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.contentEncoding="base64"}),e._zod.check=n=>{eJ(n.value)||n.issues.push({code:"invalid_format",format:"base64",input:n.value,inst:e,continue:!t.abort})}}),eX=i("$ZodBase64URL",(e,t)=>{t.pattern??(t.pattern=k),eE.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.contentEncoding="base64url"}),e._zod.check=n=>{!function(e){if(!k.test(e))return!1;let t=e.replace(/[-_]/g,e=>"-"===e?"+":"/");return eJ(t.padEnd(4*Math.ceil(t.length/4),"="))}(n.value)&&n.issues.push({code:"invalid_format",format:"base64url",input:n.value,inst:e,continue:!t.abort})}}),eq=i("$ZodE164",(e,t)=>{t.pattern??(t.pattern=$),eE.init(e,t)}),eH=i("$ZodJWT",(e,t)=>{eE.init(e,t),e._zod.check=n=>{!function(e,t=null){try{let n=e.split(".");if(3!==n.length)return!1;let[i]=n;if(!i)return!1;let r=JSON.parse(atob(i));if("typ"in r&&r?.typ!=="JWT"||!r.alg||t&&(!("alg"in r)||r.alg!==t))return!1;return!0}catch{return!1}}(n.value,t.alg)&&n.issues.push({code:"invalid_format",format:"jwt",input:n.value,inst:e,continue:!t.abort})}}),eQ=i("$ZodNumber",(e,t)=>{eI.init(e,t),e._zod.pattern=e._zod.bag.pattern??T,e._zod.parse=(n,i)=>{if(t.coerce)try{n.value=Number(n.value)}catch(e){}let r=n.value;if("number"==typeof r&&!Number.isNaN(r)&&Number.isFinite(r))return n;let o="number"==typeof r?Number.isNaN(r)?"NaN":Number.isFinite(r)?void 0:"Infinity":void 0;return n.issues.push({expected:"number",code:"invalid_type",input:r,inst:e,...o?{received:o}:{}}),n}}),e0=i("$ZodNumber",(e,t)=>{eo.init(e,t),eQ.init(e,t)}),e1=i("$ZodUnknown",(e,t)=>{eI.init(e,t),e._zod.parse=e=>e}),e9=i("$ZodNever",(e,t)=>{eI.init(e,t),e._zod.parse=(t,n)=>(t.issues.push({expected:"never",code:"invalid_type",input:t.value,inst:e}),t)});function e4(e,t,n){e.issues.length&&t.issues.push(...K(n,e.issues)),t.value[n]=e.value}let e2=i("$ZodArray",(e,t)=>{eI.init(e,t),e._zod.parse=(n,i)=>{let r=n.value;if(!Array.isArray(r))return n.issues.push({expected:"array",code:"invalid_type",input:r,inst:e}),n;n.value=Array(r.length);let o=[];for(let e=0;e<r.length;e++){let a=r[e],s=t.element._zod.run({value:a,issues:[]},i);s instanceof Promise?o.push(s.then(t=>e4(t,n,e))):e4(s,n,e)}return o.length?Promise.all(o).then(()=>n):n}});function e6(e,t,n){e.issues.length&&t.issues.push(...K(n,e.issues)),t.value[n]=e.value}function e8(e,t,n,i){e.issues.length?void 0===i[n]?n in i?t.value[n]=void 0:t.value[n]=e.value:t.issues.push(...K(n,e.issues)):void 0===e.value?n in i&&(t.value[n]=void 0):t.value[n]=e.value}let e3=i("$ZodObject",(e,t)=>{let n,i;eI.init(e,t);let r=S(()=>{let e=Object.keys(t.shape);for(let n of e)if(!(t.shape[n]instanceof eI))throw Error(`Invalid element at key "${n}": expected a Zod schema`);let n=function(e){return Object.keys(e).filter(t=>"optional"===e[t]._zod.optin&&"optional"===e[t]._zod.optout)}(t.shape);return{shape:t.shape,keys:e,keySet:new Set(e),numKeys:e.length,optionalKeys:new Set(n)}});F(e._zod,"propValues",()=>{let e=t.shape,n={};for(let t in e){let i=e[t]._zod;if(i.values)for(let e of(n[t]??(n[t]=new Set),i.values))n[t].add(e)}return n});let a=e=>{let t=new e_(["shape","payload","ctx"]),n=r.value,i=e=>{let t=R(e);return`shape[${t}]._zod.run({ value: input[${t}], issues: [] }, ctx)`};t.write("const input = payload.value;");let o=Object.create(null),a=0;for(let e of n.keys)o[e]=`key_${a++}`;for(let e of(t.write("const newResult = {}"),n.keys))if(n.optionalKeys.has(e)){let n=o[e];t.write(`const ${n} = ${i(e)};`);let r=R(e);t.write(`
        if (${n}.issues.length) {
          if (input[${r}] === undefined) {
            if (${r} in input) {
              newResult[${r}] = undefined;
            }
          } else {
            payload.issues = payload.issues.concat(
              ${n}.issues.map((iss) => ({
                ...iss,
                path: iss.path ? [${r}, ...iss.path] : [${r}],
              }))
            );
          }
        } else if (${n}.value === undefined) {
          if (${r} in input) newResult[${r}] = undefined;
        } else {
          newResult[${r}] = ${n}.value;
        }
        `)}else{let n=o[e];t.write(`const ${n} = ${i(e)};`),t.write(`
          if (${n}.issues.length) payload.issues = payload.issues.concat(${n}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${R(e)}, ...iss.path] : [${R(e)}]
          })));`),t.write(`newResult[${R(e)}] = ${n}.value`)}t.write("payload.value = newResult;"),t.write("return payload;");let s=t.compile();return(t,n)=>s(e,t,n)},s=!o.jitless,u=s&&M.value,l=t.catchall;e._zod.parse=(o,c)=>{i??(i=r.value);let d=o.value;if(!D(d))return o.issues.push({expected:"object",code:"invalid_type",input:d,inst:e}),o;let p=[];if(s&&u&&c?.async===!1&&!0!==c.jitless)n||(n=a(t.shape)),o=n(o,c);else{o.value={};let e=i.shape;for(let t of i.keys){let n=e[t],i=n._zod.run({value:d[t],issues:[]},c),r="optional"===n._zod.optin&&"optional"===n._zod.optout;i instanceof Promise?p.push(i.then(e=>r?e8(e,o,t,d):e6(e,o,t))):r?e8(i,o,t,d):e6(i,o,t)}}if(!l)return p.length?Promise.all(p).then(()=>o):o;let f=[],h=i.keySet,m=l._zod,v=m.def.type;for(let e of Object.keys(d)){if(h.has(e))continue;if("never"===v){f.push(e);continue}let t=m.run({value:d[e],issues:[]},c);t instanceof Promise?p.push(t.then(t=>e6(t,o,e))):e6(t,o,e)}return(f.length&&o.issues.push({code:"unrecognized_keys",keys:f,input:d,inst:e}),p.length)?Promise.all(p).then(()=>o):o}});function e5(e,t,n,i){for(let n of e)if(0===n.issues.length)return t.value=n.value,t;return t.issues.push({code:"invalid_union",input:t.value,inst:n,errors:e.map(e=>e.issues.map(e=>q(e,i,a())))}),t}let e7=i("$ZodUnion",(e,t)=>{eI.init(e,t),F(e._zod,"optin",()=>t.options.some(e=>"optional"===e._zod.optin)?"optional":void 0),F(e._zod,"optout",()=>t.options.some(e=>"optional"===e._zod.optout)?"optional":void 0),F(e._zod,"values",()=>{if(t.options.every(e=>e._zod.values))return new Set(t.options.flatMap(e=>Array.from(e._zod.values)))}),F(e._zod,"pattern",()=>{if(t.options.every(e=>e._zod.pattern)){let e=t.options.map(e=>e._zod.pattern);return RegExp(`^(${e.map(e=>j(e.source)).join("|")})$`)}}),e._zod.parse=(n,i)=>{let r=!1,o=[];for(let e of t.options){let t=e._zod.run({value:n.value,issues:[]},i);if(t instanceof Promise)o.push(t),r=!0;else{if(0===t.issues.length)return t;o.push(t)}}return r?Promise.all(o).then(t=>e5(t,n,e,i)):e5(o,n,e,i)}}),te=i("$ZodIntersection",(e,t)=>{eI.init(e,t),e._zod.parse=(e,n)=>{let i=e.value,r=t.left._zod.run({value:i,issues:[]},n),o=t.right._zod.run({value:i,issues:[]},n);return r instanceof Promise||o instanceof Promise?Promise.all([r,o]).then(([t,n])=>tt(e,t,n)):tt(e,r,o)}});function tt(e,t,n){if(t.issues.length&&e.issues.push(...t.issues),n.issues.length&&e.issues.push(...n.issues),J(e))return e;let i=function e(t,n){if(t===n||t instanceof Date&&n instanceof Date&&+t==+n)return{valid:!0,data:t};if(V(t)&&V(n)){let i=Object.keys(n),r=Object.keys(t).filter(e=>-1!==i.indexOf(e)),o={...t,...n};for(let i of r){let r=e(t[i],n[i]);if(!r.valid)return{valid:!1,mergeErrorPath:[i,...r.mergeErrorPath]};o[i]=r.data}return{valid:!0,data:o}}if(Array.isArray(t)&&Array.isArray(n)){if(t.length!==n.length)return{valid:!1,mergeErrorPath:[]};let i=[];for(let r=0;r<t.length;r++){let o=e(t[r],n[r]);if(!o.valid)return{valid:!1,mergeErrorPath:[r,...o.mergeErrorPath]};i.push(o.data)}return{valid:!0,data:i}}return{valid:!1,mergeErrorPath:[]}}(t.value,n.value);if(!i.valid)throw Error(`Unmergable intersection. Error path: ${JSON.stringify(i.mergeErrorPath)}`);return e.value=i.data,e}let tn=i("$ZodEnum",(e,t)=>{eI.init(e,t);let n=function(e){let t=Object.values(e).filter(e=>"number"==typeof e);return Object.entries(e).filter(([e,n])=>-1===t.indexOf(+e)).map(([e,t])=>t)}(t.entries);e._zod.values=new Set(n),e._zod.pattern=RegExp(`^(${n.filter(e=>L.has(typeof e)).map(e=>"string"==typeof e?W(e):e.toString()).join("|")})$`),e._zod.parse=(t,i)=>{let r=t.value;return e._zod.values.has(r)||t.issues.push({code:"invalid_value",values:n,input:r,inst:e}),t}}),ti=i("$ZodTransform",(e,t)=>{eI.init(e,t),e._zod.parse=(e,n)=>{let i=t.transform(e.value,e);if(n.async)return(i instanceof Promise?i:Promise.resolve(i)).then(t=>(e.value=t,e));if(i instanceof Promise)throw new r;return e.value=i,e}}),tr=i("$ZodOptional",(e,t)=>{eI.init(e,t),e._zod.optin="optional",e._zod.optout="optional",F(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,void 0]):void 0),F(e._zod,"pattern",()=>{let e=t.innerType._zod.pattern;return e?RegExp(`^(${j(e.source)})?$`):void 0}),e._zod.parse=(e,n)=>"optional"===t.innerType._zod.optin?t.innerType._zod.run(e,n):void 0===e.value?e:t.innerType._zod.run(e,n)}),to=i("$ZodNullable",(e,t)=>{eI.init(e,t),F(e._zod,"optin",()=>t.innerType._zod.optin),F(e._zod,"optout",()=>t.innerType._zod.optout),F(e._zod,"pattern",()=>{let e=t.innerType._zod.pattern;return e?RegExp(`^(${j(e.source)}|null)$`):void 0}),F(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,null]):void 0),e._zod.parse=(e,n)=>null===e.value?e:t.innerType._zod.run(e,n)}),ta=i("$ZodDefault",(e,t)=>{eI.init(e,t),e._zod.optin="optional",F(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,n)=>{if(void 0===e.value)return e.value=t.defaultValue,e;let i=t.innerType._zod.run(e,n);return i instanceof Promise?i.then(e=>ts(e,t)):ts(i,t)}});function ts(e,t){return void 0===e.value&&(e.value=t.defaultValue),e}let tu=i("$ZodPrefault",(e,t)=>{eI.init(e,t),e._zod.optin="optional",F(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,n)=>(void 0===e.value&&(e.value=t.defaultValue),t.innerType._zod.run(e,n))}),tl=i("$ZodNonOptional",(e,t)=>{eI.init(e,t),F(e._zod,"values",()=>{let e=t.innerType._zod.values;return e?new Set([...e].filter(e=>void 0!==e)):void 0}),e._zod.parse=(n,i)=>{let r=t.innerType._zod.run(n,i);return r instanceof Promise?r.then(t=>tc(t,e)):tc(r,e)}});function tc(e,t){return e.issues.length||void 0!==e.value||e.issues.push({code:"invalid_type",expected:"nonoptional",input:e.value,inst:t}),e}let td=i("$ZodCatch",(e,t)=>{eI.init(e,t),e._zod.optin="optional",F(e._zod,"optout",()=>t.innerType._zod.optout),F(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,n)=>{let i=t.innerType._zod.run(e,n);return i instanceof Promise?i.then(i=>(e.value=i.value,i.issues.length&&(e.value=t.catchValue({...e,error:{issues:i.issues.map(e=>q(e,n,a()))},input:e.value}),e.issues=[]),e)):(e.value=i.value,i.issues.length&&(e.value=t.catchValue({...e,error:{issues:i.issues.map(e=>q(e,n,a()))},input:e.value}),e.issues=[]),e)}}),tp=i("$ZodPipe",(e,t)=>{eI.init(e,t),F(e._zod,"values",()=>t.in._zod.values),F(e._zod,"optin",()=>t.in._zod.optin),F(e._zod,"optout",()=>t.out._zod.optout),F(e._zod,"propValues",()=>t.in._zod.propValues),e._zod.parse=(e,n)=>{let i=t.in._zod.run(e,n);return i instanceof Promise?i.then(e=>tf(e,t,n)):tf(i,t,n)}});function tf(e,t,n){return J(e)?e:t.out._zod.run({value:e.value,issues:e.issues},n)}let th=i("$ZodReadonly",(e,t)=>{eI.init(e,t),F(e._zod,"propValues",()=>t.innerType._zod.propValues),F(e._zod,"values",()=>t.innerType._zod.values),F(e._zod,"optin",()=>t.innerType._zod.optin),F(e._zod,"optout",()=>t.innerType._zod.optout),e._zod.parse=(e,n)=>{let i=t.innerType._zod.run(e,n);return i instanceof Promise?i.then(tm):tm(i)}});function tm(e){return e.value=Object.freeze(e.value),e}let tv=i("$ZodCustom",(e,t)=>{ee.init(e,t),eI.init(e,t),e._zod.parse=(e,t)=>e,e._zod.check=n=>{let i=n.value,r=t.fn(i);if(r instanceof Promise)return r.then(t=>t_(t,n,i,e));t_(r,n,i,e)}});function t_(e,t,n,i){if(!e){let e={code:"custom",input:n,inst:i,path:[...i._zod.def.path??[]],continue:!i._zod.def.abort};i._zod.def.params&&(e.params=i._zod.def.params),t.issues.push(Q(e))}}Symbol("ZodOutput"),Symbol("ZodInput");class tg{constructor(){this._map=new Map,this._idmap=new Map}add(e,...t){let n=t[0];if(this._map.set(e,n),n&&"object"==typeof n&&"id"in n){if(this._idmap.has(n.id))throw Error(`ID ${n.id} already exists in the registry`);this._idmap.set(n.id,e)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(e){let t=this._map.get(e);return t&&"object"==typeof t&&"id"in t&&this._idmap.delete(t.id),this._map.delete(e),this}get(e){let t=e._zod.parent;if(t){let n={...this.get(t)??{}};return delete n.id,{...n,...this._map.get(e)}}return this._map.get(e)}has(e){return this._map.has(e)}}let ty=new tg;function tz(e,t){return new e({type:"string",format:"guid",check:"string_format",abort:!1,...Y(t)})}function tb(e,t){return new en({check:"less_than",...Y(t),value:e,inclusive:!1})}function tk(e,t){return new en({check:"less_than",...Y(t),value:e,inclusive:!0})}function tw(e,t){return new ei({check:"greater_than",...Y(t),value:e,inclusive:!1})}function t$(e,t){return new ei({check:"greater_than",...Y(t),value:e,inclusive:!0})}function tZ(e,t){return new er({check:"multiple_of",...Y(t),value:e})}function tI(e,t){return new ea({check:"max_length",...Y(t),maximum:e})}function tx(e,t){return new es({check:"min_length",...Y(t),minimum:e})}function tE(e,t){return new eu({check:"length_equals",...Y(t),length:e})}function tP(e){return new ev({check:"overwrite",tx:e})}let tT=i("ZodISODateTime",(e,t)=>{eD.init(e,t),tW.init(e,t)}),tA=i("ZodISODate",(e,t)=>{eM.init(e,t),tW.init(e,t)}),tN=i("ZodISOTime",(e,t)=>{eV.init(e,t),tW.init(e,t)}),tO=i("ZodISODuration",(e,t)=>{eL.init(e,t),tW.init(e,t)}),tS=(e,t)=>{ey.init(e,t),e.name="ZodError",Object.defineProperties(e,{format:{value:t=>(function(e,t){let n=t||function(e){return e.message},i={_errors:[]},r=e=>{for(let t of e.issues)if("invalid_union"===t.code&&t.errors.length)t.errors.map(e=>r({issues:e}));else if("invalid_key"===t.code)r({issues:t.issues});else if("invalid_element"===t.code)r({issues:t.issues});else if(0===t.path.length)i._errors.push(n(t));else{let e=i,r=0;for(;r<t.path.length;){let i=t.path[r];r===t.path.length-1?(e[i]=e[i]||{_errors:[]},e[i]._errors.push(n(t))):e[i]=e[i]||{_errors:[]},e=e[i],r++}}};return r(e),i})(e,t)},flatten:{value:t=>(function(e,t=e=>e.message){let n={},i=[];for(let r of e.issues)r.path.length>0?(n[r.path[0]]=n[r.path[0]]||[],n[r.path[0]].push(t(r))):i.push(t(r));return{formErrors:i,fieldErrors:n}})(e,t)},addIssue:{value:t=>e.issues.push(t)},addIssues:{value:t=>e.issues.push(...t)},isEmpty:{get:()=>0===e.issues.length}})};i("ZodError",tS);let tj=i("ZodError",tS,{Parent:Error}),tF=(e,t,n,i)=>{let o=n?Object.assign(n,{async:!1}):{async:!1},s=e._zod.run({value:t,issues:[]},o);if(s instanceof Promise)throw new r;if(s.issues.length){let e=new(i?.Err??tj)(s.issues.map(e=>q(e,o,a())));throw U(e,i?.callee),e}return s.value},tC=async(e,t,n,i)=>{let r=n?Object.assign(n,{async:!0}):{async:!0},o=e._zod.run({value:t,issues:[]},r);if(o instanceof Promise&&(o=await o),o.issues.length){let e=new(i?.Err??tj)(o.issues.map(e=>q(e,r,a())));throw U(e,i?.callee),e}return o.value},tR=eb(tj),tU=ew(tj),tD=i("ZodType",(e,t)=>(eI.init(e,t),e.def=t,Object.defineProperty(e,"_def",{value:t}),e.check=(...n)=>e.clone({...t,checks:[...t.checks??[],...n.map(e=>"function"==typeof e?{_zod:{check:e,def:{check:"custom"},onattach:[]}}:e)]}),e.clone=(t,n)=>G(e,t,n),e.brand=()=>e,e.register=(t,n)=>(t.add(e,n),e),e.parse=(t,n)=>tF(e,t,n,{callee:e.parse}),e.safeParse=(t,n)=>tR(e,t,n),e.parseAsync=async(t,n)=>tC(e,t,n,{callee:e.parseAsync}),e.safeParseAsync=async(t,n)=>tU(e,t,n),e.spa=e.safeParseAsync,e.refine=(t,n)=>e.check(function(e,t={}){return new nx({type:"custom",check:"custom",fn:e,...Y(t)})}(t,n)),e.superRefine=t=>e.check(function(e){let t=function(e){let t=new ee({check:"custom"});return t._zod.check=e,t}(n=>(n.addIssue=e=>{"string"==typeof e?n.issues.push(Q(e,n.value,t._zod.def)):(e.fatal&&(e.continue=!1),e.code??(e.code="custom"),e.input??(e.input=n.value),e.inst??(e.inst=t),e.continue??(e.continue=!t._zod.def.abort),n.issues.push(Q(e)))},e(n.value,n)));return t}(t)),e.overwrite=t=>e.check(tP(t)),e.optional=()=>n_(e),e.nullable=()=>ny(e),e.nullish=()=>n_(ny(e)),e.nonoptional=t=>new nk({type:"nonoptional",innerType:e,...Y(t)}),e.array=()=>nu(e),e.or=t=>(function(e,t){return new nd({type:"union",options:e,...Y(t)})})([e,t]),e.and=t=>new np({type:"intersection",left:e,right:t}),e.transform=t=>nZ(e,function(e){return new nm({type:"transform",transform:e})}(t)),e.default=t=>(function(e,t){return new nz({type:"default",innerType:e,get defaultValue(){return"function"==typeof t?t():t}})})(e,t),e.prefault=t=>(function(e,t){return new nb({type:"prefault",innerType:e,get defaultValue(){return"function"==typeof t?t():t}})})(e,t),e.catch=t=>(function(e,t){return new nw({type:"catch",innerType:e,catchValue:"function"==typeof t?t:()=>t})})(e,t),e.pipe=t=>nZ(e,t),e.readonly=()=>new nI({type:"readonly",innerType:e}),e.describe=t=>{let n=e.clone();return ty.add(n,{description:t}),n},Object.defineProperty(e,"description",{get:()=>ty.get(e)?.description,configurable:!0}),e.meta=(...t)=>{if(0===t.length)return ty.get(e);let n=e.clone();return ty.add(n,t[0]),n},e.isOptional=()=>e.safeParse(void 0).success,e.isNullable=()=>e.safeParse(null).success,e)),tM=i("_ZodString",(e,t)=>{ex.init(e,t),tD.init(e,t);let n=e._zod.bag;e.format=n.format??null,e.minLength=n.minimum??null,e.maxLength=n.maximum??null,e.regex=(...t)=>e.check(function(e,t){return new ec({check:"string_format",format:"regex",...Y(t),pattern:e})}(...t)),e.includes=(...t)=>e.check(function(e,t){return new ef({check:"string_format",format:"includes",...Y(t),includes:e})}(...t)),e.startsWith=(...t)=>e.check(function(e,t){return new eh({check:"string_format",format:"starts_with",...Y(t),prefix:e})}(...t)),e.endsWith=(...t)=>e.check(function(e,t){return new em({check:"string_format",format:"ends_with",...Y(t),suffix:e})}(...t)),e.min=(...t)=>e.check(tx(...t)),e.max=(...t)=>e.check(tI(...t)),e.length=(...t)=>e.check(tE(...t)),e.nonempty=(...t)=>e.check(tx(1,...t)),e.lowercase=t=>e.check(new ed({check:"string_format",format:"lowercase",...Y(t)})),e.uppercase=t=>e.check(new ep({check:"string_format",format:"uppercase",...Y(t)})),e.trim=()=>e.check(tP(e=>e.trim())),e.normalize=(...t)=>e.check(function(e){return tP(t=>t.normalize(e))}(...t)),e.toLowerCase=()=>e.check(tP(e=>e.toLowerCase())),e.toUpperCase=()=>e.check(tP(e=>e.toUpperCase()))}),tV=i("ZodString",(e,t)=>{ex.init(e,t),tM.init(e,t),e.email=t=>e.check(new tG({type:"string",format:"email",check:"string_format",abort:!1,...Y(t)})),e.url=t=>e.check(new tJ({type:"string",format:"url",check:"string_format",abort:!1,...Y(t)})),e.jwt=t=>e.check(new t7({type:"string",format:"jwt",check:"string_format",abort:!1,...Y(t)})),e.emoji=t=>e.check(new tK({type:"string",format:"emoji",check:"string_format",abort:!1,...Y(t)})),e.guid=t=>e.check(tz(tY,t)),e.uuid=t=>e.check(new tB({type:"string",format:"uuid",check:"string_format",abort:!1,...Y(t)})),e.uuidv4=t=>e.check(new tB({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...Y(t)})),e.uuidv6=t=>e.check(new tB({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...Y(t)})),e.uuidv7=t=>e.check(new tB({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...Y(t)})),e.nanoid=t=>e.check(new tX({type:"string",format:"nanoid",check:"string_format",abort:!1,...Y(t)})),e.guid=t=>e.check(tz(tY,t)),e.cuid=t=>e.check(new tq({type:"string",format:"cuid",check:"string_format",abort:!1,...Y(t)})),e.cuid2=t=>e.check(new tH({type:"string",format:"cuid2",check:"string_format",abort:!1,...Y(t)})),e.ulid=t=>e.check(new tQ({type:"string",format:"ulid",check:"string_format",abort:!1,...Y(t)})),e.base64=t=>e.check(new t8({type:"string",format:"base64",check:"string_format",abort:!1,...Y(t)})),e.base64url=t=>e.check(new t3({type:"string",format:"base64url",check:"string_format",abort:!1,...Y(t)})),e.xid=t=>e.check(new t0({type:"string",format:"xid",check:"string_format",abort:!1,...Y(t)})),e.ksuid=t=>e.check(new t1({type:"string",format:"ksuid",check:"string_format",abort:!1,...Y(t)})),e.ipv4=t=>e.check(new t9({type:"string",format:"ipv4",check:"string_format",abort:!1,...Y(t)})),e.ipv6=t=>e.check(new t4({type:"string",format:"ipv6",check:"string_format",abort:!1,...Y(t)})),e.cidrv4=t=>e.check(new t2({type:"string",format:"cidrv4",check:"string_format",abort:!1,...Y(t)})),e.cidrv6=t=>e.check(new t6({type:"string",format:"cidrv6",check:"string_format",abort:!1,...Y(t)})),e.e164=t=>e.check(new t5({type:"string",format:"e164",check:"string_format",abort:!1,...Y(t)})),e.datetime=t=>e.check(new tT({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...Y(t)})),e.date=t=>e.check(new tA({type:"string",format:"date",check:"string_format",...Y(t)})),e.time=t=>e.check(new tN({type:"string",format:"time",check:"string_format",precision:null,...Y(t)})),e.duration=t=>e.check(new tO({type:"string",format:"duration",check:"string_format",...Y(t)}))});function tL(e){return new tV({type:"string",...Y(e)})}let tW=i("ZodStringFormat",(e,t)=>{eE.init(e,t),tM.init(e,t)}),tG=i("ZodEmail",(e,t)=>{eA.init(e,t),tW.init(e,t)}),tY=i("ZodGUID",(e,t)=>{eP.init(e,t),tW.init(e,t)}),tB=i("ZodUUID",(e,t)=>{eT.init(e,t),tW.init(e,t)}),tJ=i("ZodURL",(e,t)=>{eN.init(e,t),tW.init(e,t)}),tK=i("ZodEmoji",(e,t)=>{eO.init(e,t),tW.init(e,t)}),tX=i("ZodNanoID",(e,t)=>{eS.init(e,t),tW.init(e,t)}),tq=i("ZodCUID",(e,t)=>{ej.init(e,t),tW.init(e,t)}),tH=i("ZodCUID2",(e,t)=>{eF.init(e,t),tW.init(e,t)}),tQ=i("ZodULID",(e,t)=>{eC.init(e,t),tW.init(e,t)}),t0=i("ZodXID",(e,t)=>{eR.init(e,t),tW.init(e,t)}),t1=i("ZodKSUID",(e,t)=>{eU.init(e,t),tW.init(e,t)}),t9=i("ZodIPv4",(e,t)=>{eW.init(e,t),tW.init(e,t)}),t4=i("ZodIPv6",(e,t)=>{eG.init(e,t),tW.init(e,t)}),t2=i("ZodCIDRv4",(e,t)=>{eY.init(e,t),tW.init(e,t)}),t6=i("ZodCIDRv6",(e,t)=>{eB.init(e,t),tW.init(e,t)}),t8=i("ZodBase64",(e,t)=>{eK.init(e,t),tW.init(e,t)}),t3=i("ZodBase64URL",(e,t)=>{eX.init(e,t),tW.init(e,t)}),t5=i("ZodE164",(e,t)=>{eq.init(e,t),tW.init(e,t)}),t7=i("ZodJWT",(e,t)=>{eH.init(e,t),tW.init(e,t)}),ne=i("ZodNumber",(e,t)=>{eQ.init(e,t),tD.init(e,t),e.gt=(t,n)=>e.check(tw(t,n)),e.gte=(t,n)=>e.check(t$(t,n)),e.min=(t,n)=>e.check(t$(t,n)),e.lt=(t,n)=>e.check(tb(t,n)),e.lte=(t,n)=>e.check(tk(t,n)),e.max=(t,n)=>e.check(tk(t,n)),e.int=t=>e.check(ni(t)),e.safe=t=>e.check(ni(t)),e.positive=t=>e.check(tw(0,t)),e.nonnegative=t=>e.check(t$(0,t)),e.negative=t=>e.check(tb(0,t)),e.nonpositive=t=>e.check(tk(0,t)),e.multipleOf=(t,n)=>e.check(tZ(t,n)),e.step=(t,n)=>e.check(tZ(t,n)),e.finite=()=>e;let n=e._zod.bag;e.minValue=Math.max(n.minimum??Number.NEGATIVE_INFINITY,n.exclusiveMinimum??Number.NEGATIVE_INFINITY)??null,e.maxValue=Math.min(n.maximum??Number.POSITIVE_INFINITY,n.exclusiveMaximum??Number.POSITIVE_INFINITY)??null,e.isInt=(n.format??"").includes("int")||Number.isSafeInteger(n.multipleOf??.5),e.isFinite=!0,e.format=n.format??null});function nt(e){return new ne({type:"number",checks:[],...Y(e)})}let nn=i("ZodNumberFormat",(e,t)=>{e0.init(e,t),ne.init(e,t)});function ni(e){return new nn({type:"number",check:"number_format",abort:!1,format:"safeint",...Y(e)})}let nr=i("ZodUnknown",(e,t)=>{e1.init(e,t),tD.init(e,t)});function no(){return new nr({type:"unknown"})}let na=i("ZodNever",(e,t)=>{e9.init(e,t),tD.init(e,t)}),ns=i("ZodArray",(e,t)=>{e2.init(e,t),tD.init(e,t),e.element=t.element,e.min=(t,n)=>e.check(tx(t,n)),e.nonempty=t=>e.check(tx(1,t)),e.max=(t,n)=>e.check(tI(t,n)),e.length=(t,n)=>e.check(tE(t,n)),e.unwrap=()=>e.element});function nu(e,t){return new ns({type:"array",element:e,...Y(t)})}let nl=i("ZodObject",(e,t)=>{e3.init(e,t),tD.init(e,t),F(e,"shape",()=>t.shape),e.keyof=()=>nh(Object.keys(e._zod.def.shape)),e.catchall=t=>e.clone({...e._zod.def,catchall:t}),e.passthrough=()=>e.clone({...e._zod.def,catchall:no()}),e.loose=()=>e.clone({...e._zod.def,catchall:no()}),e.strict=()=>{var t;return e.clone({...e._zod.def,catchall:new na({type:"never",...Y(void 0)})})},e.strip=()=>e.clone({...e._zod.def,catchall:void 0}),e.extend=t=>(function(e,t){if(!V(t))throw Error("Invalid input to extend: expected a plain object");let n={...e._zod.def,get shape(){let n={...e._zod.def.shape,...t};return C(this,"shape",n),n},checks:[]};return G(e,n)})(e,t),e.merge=t=>(function(e,t){return G(e,{...e._zod.def,get shape(){let n={...e._zod.def.shape,...t._zod.def.shape};return C(this,"shape",n),n},catchall:t._zod.def.catchall,checks:[]})})(e,t),e.pick=t=>(function(e,t){let n={},i=e._zod.def;for(let e in t){if(!(e in i.shape))throw Error(`Unrecognized key: "${e}"`);t[e]&&(n[e]=i.shape[e])}return G(e,{...e._zod.def,shape:n,checks:[]})})(e,t),e.omit=t=>(function(e,t){let n={...e._zod.def.shape},i=e._zod.def;for(let e in t){if(!(e in i.shape))throw Error(`Unrecognized key: "${e}"`);t[e]&&delete n[e]}return G(e,{...e._zod.def,shape:n,checks:[]})})(e,t),e.partial=(...t)=>(function(e,t,n){let i=t._zod.def.shape,r={...i};if(n)for(let t in n){if(!(t in i))throw Error(`Unrecognized key: "${t}"`);n[t]&&(r[t]=e?new e({type:"optional",innerType:i[t]}):i[t])}else for(let t in i)r[t]=e?new e({type:"optional",innerType:i[t]}):i[t];return G(t,{...t._zod.def,shape:r,checks:[]})})(nv,e,t[0]),e.required=(...t)=>(function(e,t,n){let i=t._zod.def.shape,r={...i};if(n)for(let t in n){if(!(t in r))throw Error(`Unrecognized key: "${t}"`);n[t]&&(r[t]=new e({type:"nonoptional",innerType:i[t]}))}else for(let t in i)r[t]=new e({type:"nonoptional",innerType:i[t]});return G(t,{...t._zod.def,shape:r,checks:[]})})(nk,e,t[0])});function nc(e,t){return new nl({type:"object",get shape(){return C(this,"shape",{...e}),this.shape},...Y(t)})}let nd=i("ZodUnion",(e,t)=>{e7.init(e,t),tD.init(e,t),e.options=t.options}),np=i("ZodIntersection",(e,t)=>{te.init(e,t),tD.init(e,t)}),nf=i("ZodEnum",(e,t)=>{tn.init(e,t),tD.init(e,t),e.enum=t.entries,e.options=Object.values(t.entries);let n=new Set(Object.keys(t.entries));e.extract=(e,i)=>{let r={};for(let i of e)if(n.has(i))r[i]=t.entries[i];else throw Error(`Key ${i} not found in enum`);return new nf({...t,checks:[],...Y(i),entries:r})},e.exclude=(e,i)=>{let r={...t.entries};for(let t of e)if(n.has(t))delete r[t];else throw Error(`Key ${t} not found in enum`);return new nf({...t,checks:[],...Y(i),entries:r})}});function nh(e,t){return new nf({type:"enum",entries:Array.isArray(e)?Object.fromEntries(e.map(e=>[e,e])):e,...Y(t)})}let nm=i("ZodTransform",(e,t)=>{ti.init(e,t),tD.init(e,t),e._zod.parse=(n,i)=>{n.addIssue=i=>{"string"==typeof i?n.issues.push(Q(i,n.value,t)):(i.fatal&&(i.continue=!1),i.code??(i.code="custom"),i.input??(i.input=n.value),i.inst??(i.inst=e),i.continue??(i.continue=!0),n.issues.push(Q(i)))};let r=t.transform(n.value,n);return r instanceof Promise?r.then(e=>(n.value=e,n)):(n.value=r,n)}}),nv=i("ZodOptional",(e,t)=>{tr.init(e,t),tD.init(e,t),e.unwrap=()=>e._zod.def.innerType});function n_(e){return new nv({type:"optional",innerType:e})}let ng=i("ZodNullable",(e,t)=>{to.init(e,t),tD.init(e,t),e.unwrap=()=>e._zod.def.innerType});function ny(e){return new ng({type:"nullable",innerType:e})}let nz=i("ZodDefault",(e,t)=>{ta.init(e,t),tD.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeDefault=e.unwrap}),nb=i("ZodPrefault",(e,t)=>{tu.init(e,t),tD.init(e,t),e.unwrap=()=>e._zod.def.innerType}),nk=i("ZodNonOptional",(e,t)=>{tl.init(e,t),tD.init(e,t),e.unwrap=()=>e._zod.def.innerType}),nw=i("ZodCatch",(e,t)=>{td.init(e,t),tD.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeCatch=e.unwrap}),n$=i("ZodPipe",(e,t)=>{tp.init(e,t),tD.init(e,t),e.in=t.in,e.out=t.out});function nZ(e,t){return new n$({type:"pipe",in:e,out:t})}let nI=i("ZodReadonly",(e,t)=>{th.init(e,t),tD.init(e,t)}),nx=i("ZodCustom",(e,t)=>{tv.init(e,t),tD.init(e,t)})}};