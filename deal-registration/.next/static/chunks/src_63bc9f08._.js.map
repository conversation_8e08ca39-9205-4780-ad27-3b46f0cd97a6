{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Date formatting utilities\nexport function formatDate(date: string | Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(new Date(date))\n}\n\nexport function formatDateTime(date: string | Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount)\n}\n\n// String utilities\nexport function capitalizeFirst(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1)\n}\n\nexport function slugify(str: string): string {\n  return str\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '')\n}\n\n// Fuzzy matching for duplicate detection\nexport function calculateSimilarity(str1: string, str2: string): number {\n  const longer = str1.length > str2.length ? str1 : str2\n  const shorter = str1.length > str2.length ? str2 : str1\n  \n  if (longer.length === 0) return 1.0\n  \n  const distance = levenshteinDistance(longer, shorter)\n  return (longer.length - distance) / longer.length\n}\n\nfunction levenshteinDistance(str1: string, str2: string): number {\n  const matrix = []\n  \n  for (let i = 0; i <= str2.length; i++) {\n    matrix[i] = [i]\n  }\n  \n  for (let j = 0; j <= str1.length; j++) {\n    matrix[0][j] = j\n  }\n  \n  for (let i = 1; i <= str2.length; i++) {\n    for (let j = 1; j <= str1.length; j++) {\n      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {\n        matrix[i][j] = matrix[i - 1][j - 1]\n      } else {\n        matrix[i][j] = Math.min(\n          matrix[i - 1][j - 1] + 1,\n          matrix[i][j - 1] + 1,\n          matrix[i - 1][j] + 1\n        )\n      }\n    }\n  }\n  \n  return matrix[str2.length][str1.length]\n}\n\n// Company name normalization for better matching\nexport function normalizeCompanyName(name: string): string {\n  return name\n    .toLowerCase()\n    .replace(/\\b(inc|corp|corporation|ltd|limited|llc|co)\\b\\.?/g, '')\n    .replace(/[^\\w\\s]/g, '')\n    .replace(/\\s+/g, ' ')\n    .trim()\n}\n\n// Territory overlap detection\nexport function checkTerritoryOverlap(territory1: string, territory2: string): boolean {\n  const t1 = territory1.toLowerCase().trim()\n  const t2 = territory2.toLowerCase().trim()\n  \n  // Exact match\n  if (t1 === t2) return true\n  \n  // Check for common territory patterns\n  const patterns = [\n    /^(north|south|east|west)\\s*(america|us|usa|united states)$/,\n    /^(northeast|northwest|southeast|southwest)\\s*(region|territory)?$/,\n    /^(global|worldwide|international)$/,\n    /^(enterprise|commercial|federal)$/,\n  ]\n  \n  for (const pattern of patterns) {\n    if (pattern.test(t1) && pattern.test(t2)) return true\n  }\n  \n  return false\n}\n\n// Deal value comparison with tolerance\nexport function isDealValueSimilar(value1: number, value2: number, tolerance = 0.1): boolean {\n  const diff = Math.abs(value1 - value2)\n  const avg = (value1 + value2) / 2\n  return diff / avg <= tolerance\n}\n\n// Time window checking\nexport function isWithinTimeWindow(date1: string | Date, date2: string | Date, windowDays = 90): boolean {\n  const d1 = new Date(date1)\n  const d2 = new Date(date2)\n  const diffTime = Math.abs(d2.getTime() - d1.getTime())\n  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))\n  return diffDays <= windowDays\n}\n\n// Validation utilities\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function isValidUUID(uuid: string): boolean {\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i\n  return uuidRegex.test(uuid)\n}\n\n// Array utilities\nexport function groupBy<T, K extends keyof any>(array: T[], key: (item: T) => K): Record<K, T[]> {\n  return array.reduce((groups, item) => {\n    const group = key(item)\n    groups[group] = groups[group] || []\n    groups[group].push(item)\n    return groups\n  }, {} as Record<K, T[]>)\n}\n\nexport function sortBy<T>(array: T[], key: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] {\n  return [...array].sort((a, b) => {\n    const aVal = a[key]\n    const bVal = b[key]\n    \n    if (aVal < bVal) return direction === 'asc' ? -1 : 1\n    if (aVal > bVal) return direction === 'asc' ? 1 : -1\n    return 0\n  })\n}\n\n// Debounce utility for search\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null\n  \n  return (...args: Parameters<T>) => {\n    if (timeout) clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\n// Error handling\nexport function getErrorMessage(error: unknown): string {\n  if (error instanceof Error) return error.message\n  if (typeof error === 'string') return error\n  return 'An unknown error occurred'\n}\n\n// Status badge colors\nexport function getStatusColor(status: string): string {\n  const colors: Record<string, string> = {\n    pending: 'bg-yellow-100 text-yellow-800',\n    assigned: 'bg-green-100 text-green-800',\n    disputed: 'bg-red-100 text-red-800',\n    approved: 'bg-blue-100 text-blue-800',\n    rejected: 'bg-gray-100 text-gray-800',\n    active: 'bg-green-100 text-green-800',\n    inactive: 'bg-gray-100 text-gray-800',\n    resolved: 'bg-green-100 text-green-800',\n    dismissed: 'bg-gray-100 text-gray-800',\n  }\n  \n  return colors[status] || 'bg-gray-100 text-gray-800'\n}\n\n// Priority calculation for conflicts\nexport function calculateConflictPriority(\n  dealValue: number,\n  conflictType: string,\n  daysSinceSubmission: number\n): 'high' | 'medium' | 'low' {\n  let score = 0\n  \n  // Deal value weight\n  if (dealValue > 100000) score += 3\n  else if (dealValue > 50000) score += 2\n  else score += 1\n  \n  // Conflict type weight\n  if (conflictType === 'duplicate_end_user') score += 3\n  else if (conflictType === 'territory_overlap') score += 2\n  else score += 1\n  \n  // Time weight\n  if (daysSinceSubmission > 7) score += 2\n  else if (daysSinceSubmission > 3) score += 1\n  \n  if (score >= 7) return 'high'\n  if (score >= 4) return 'medium'\n  return 'low'\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,eAAe,IAAmB;IAChD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,gBAAgB,GAAW;IACzC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD;AAEO,SAAS,QAAQ,GAAW;IACjC,OAAO,IACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAGO,SAAS,oBAAoB,IAAY,EAAE,IAAY;IAC5D,MAAM,SAAS,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,OAAO;IAClD,MAAM,UAAU,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,OAAO;IAEnD,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;IAEhC,MAAM,WAAW,oBAAoB,QAAQ;IAC7C,OAAO,CAAC,OAAO,MAAM,GAAG,QAAQ,IAAI,OAAO,MAAM;AACnD;AAEA,SAAS,oBAAoB,IAAY,EAAE,IAAY;IACrD,MAAM,SAAS,EAAE;IAEjB,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,MAAM,EAAE,IAAK;QACrC,MAAM,CAAC,EAAE,GAAG;YAAC;SAAE;IACjB;IAEA,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,MAAM,EAAE,IAAK;QACrC,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG;IACjB;IAEA,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,MAAM,EAAE,IAAK;QACrC,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,MAAM,EAAE,IAAK;YACrC,IAAI,KAAK,MAAM,CAAC,IAAI,OAAO,KAAK,MAAM,CAAC,IAAI,IAAI;gBAC7C,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE;YACrC,OAAO;gBACL,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,GAAG,CACrB,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,GAAG,GACvB,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,GACnB,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG;YAEvB;QACF;IACF;IAEA,OAAO,MAAM,CAAC,KAAK,MAAM,CAAC,CAAC,KAAK,MAAM,CAAC;AACzC;AAGO,SAAS,qBAAqB,IAAY;IAC/C,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,qDAAqD,IAC7D,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,QAAQ,KAChB,IAAI;AACT;AAGO,SAAS,sBAAsB,UAAkB,EAAE,UAAkB;IAC1E,MAAM,KAAK,WAAW,WAAW,GAAG,IAAI;IACxC,MAAM,KAAK,WAAW,WAAW,GAAG,IAAI;IAExC,cAAc;IACd,IAAI,OAAO,IAAI,OAAO;IAEtB,sCAAsC;IACtC,MAAM,WAAW;QACf;QACA;QACA;QACA;KACD;IAED,KAAK,MAAM,WAAW,SAAU;QAC9B,IAAI,QAAQ,IAAI,CAAC,OAAO,QAAQ,IAAI,CAAC,KAAK,OAAO;IACnD;IAEA,OAAO;AACT;AAGO,SAAS,mBAAmB,MAAc,EAAE,MAAc,EAAE,YAAY,GAAG;IAChF,MAAM,OAAO,KAAK,GAAG,CAAC,SAAS;IAC/B,MAAM,MAAM,CAAC,SAAS,MAAM,IAAI;IAChC,OAAO,OAAO,OAAO;AACvB;AAGO,SAAS,mBAAmB,KAAoB,EAAE,KAAoB,EAAE,aAAa,EAAE;IAC5F,MAAM,KAAK,IAAI,KAAK;IACpB,MAAM,KAAK,IAAI,KAAK;IACpB,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,OAAO,KAAK,GAAG,OAAO;IACnD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAC1D,OAAO,YAAY;AACrB;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,YAAY,IAAY;IACtC,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB;AAGO,SAAS,QAAgC,KAAU,EAAE,GAAmB;IAC7E,OAAO,MAAM,MAAM,CAAC,CAAC,QAAQ;QAC3B,MAAM,QAAQ,IAAI;QAClB,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,EAAE;QACnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;QACnB,OAAO;IACT,GAAG,CAAC;AACN;AAEO,SAAS,OAAU,KAAU,EAAE,GAAY,EAAE,YAA4B,KAAK;IACnF,OAAO;WAAI;KAAM,CAAC,IAAI,CAAC,CAAC,GAAG;QACzB,MAAM,OAAO,CAAC,CAAC,IAAI;QACnB,MAAM,OAAO,CAAC,CAAC,IAAI;QAEnB,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,CAAC,IAAI;QACnD,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,IAAI,CAAC;QACnD,OAAO;IACT;AACF;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO,CAAC,GAAG;QACT,IAAI,SAAS,aAAa;QAC1B,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,gBAAgB,KAAc;IAC5C,IAAI,iBAAiB,OAAO,OAAO,MAAM,OAAO;IAChD,IAAI,OAAO,UAAU,UAAU,OAAO;IACtC,OAAO;AACT;AAGO,SAAS,eAAe,MAAc;IAC3C,MAAM,SAAiC;QACrC,SAAS;QACT,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;QACV,WAAW;IACb;IAEA,OAAO,MAAM,CAAC,OAAO,IAAI;AAC3B;AAGO,SAAS,0BACd,SAAiB,EACjB,YAAoB,EACpB,mBAA2B;IAE3B,IAAI,QAAQ;IAEZ,oBAAoB;IACpB,IAAI,YAAY,QAAQ,SAAS;SAC5B,IAAI,YAAY,OAAO,SAAS;SAChC,SAAS;IAEd,uBAAuB;IACvB,IAAI,iBAAiB,sBAAsB,SAAS;SAC/C,IAAI,iBAAiB,qBAAqB,SAAS;SACnD,SAAS;IAEd,cAAc;IACd,IAAI,sBAAsB,GAAG,SAAS;SACjC,IAAI,sBAAsB,GAAG,SAAS;IAE3C,IAAI,SAAS,GAAG,OAAO;IACvB,IAAI,SAAS,GAAG,OAAO;IACvB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-blue-600 text-white hover:bg-blue-700\",\n        destructive: \"bg-red-600 text-white hover:bg-red-700\",\n        outline: \"border border-gray-300 bg-white hover:bg-gray-50\",\n        secondary: \"bg-gray-100 text-gray-900 hover:bg-gray-200\",\n        ghost: \"hover:bg-gray-100\",\n        link: \"text-blue-600 underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,uQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/components/layout/navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { cn } from '@/lib/utils'\nimport { Button } from '@/components/ui/button'\nimport { \n  LayoutDashboard, \n  FileText, \n  Users, \n  Building2, \n  Package, \n  AlertTriangle,\n  Settings,\n  LogOut\n} from 'lucide-react'\n\nconst navigation = [\n  {\n    name: 'Dashboard',\n    href: '/',\n    icon: LayoutDashboard,\n  },\n  {\n    name: 'Deal Registration',\n    href: '/deals/new',\n    icon: FileText,\n  },\n  {\n    name: 'All Deals',\n    href: '/deals',\n    icon: FileText,\n  },\n  {\n    name: 'Conflicts',\n    href: '/conflicts',\n    icon: AlertTriangle,\n  },\n  {\n    name: 'Resellers',\n    href: '/resellers',\n    icon: Users,\n  },\n  {\n    name: 'End Users',\n    href: '/end-users',\n    icon: Building2,\n  },\n  {\n    name: 'Products',\n    href: '/products',\n    icon: Package,\n  },\n  {\n    name: 'Settings',\n    href: '/settings',\n    icon: Settings,\n  },\n]\n\nexport function Navigation() {\n  const pathname = usePathname()\n\n  return (\n    <div className=\"flex h-full w-64 flex-col bg-gray-900\">\n      <div className=\"flex h-16 items-center px-6\">\n        <h1 className=\"text-xl font-bold text-white\">Deal Registration</h1>\n      </div>\n      \n      <nav className=\"flex-1 space-y-1 px-3 py-4\">\n        {navigation.map((item) => {\n          const isActive = pathname === item.href || \n            (item.href !== '/' && pathname.startsWith(item.href))\n          \n          return (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={cn(\n                'group flex items-center rounded-md px-3 py-2 text-sm font-medium transition-colors',\n                isActive\n                  ? 'bg-gray-800 text-white'\n                  : 'text-gray-300 hover:bg-gray-700 hover:text-white'\n              )}\n            >\n              <item.icon\n                className={cn(\n                  'mr-3 h-5 w-5 flex-shrink-0',\n                  isActive ? 'text-white' : 'text-gray-400 group-hover:text-white'\n                )}\n              />\n              {item.name}\n            </Link>\n          )\n        })}\n      </nav>\n\n      <div className=\"border-t border-gray-700 p-4\">\n        <Button\n          variant=\"ghost\"\n          className=\"w-full justify-start text-gray-300 hover:bg-gray-700 hover:text-white\"\n        >\n          <LogOut className=\"mr-3 h-5 w-5\" />\n          Sign Out\n        </Button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAiBA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,MAAM,+NAAA,CAAA,kBAAe;IACvB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,iNAAA,CAAA,WAAQ;IAChB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,iNAAA,CAAA,WAAQ;IAChB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,2NAAA,CAAA,gBAAa;IACrB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;IACb;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,mNAAA,CAAA,YAAS;IACjB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,2MAAA,CAAA,UAAO;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;IAChB;CACD;AAEM,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAG,WAAU;8BAA+B;;;;;;;;;;;0BAG/C,6LAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC;oBACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACpC,KAAK,IAAI,KAAK,OAAO,SAAS,UAAU,CAAC,KAAK,IAAI;oBAErD,qBACE,6LAAC,+JAAA,CAAA,UAAI;wBAEH,MAAM,KAAK,IAAI;wBACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sFACA,WACI,2BACA;;0CAGN,6LAAC,KAAK,IAAI;gCACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8BACA,WAAW,eAAe;;;;;;4BAG7B,KAAK,IAAI;;uBAfL,KAAK,IAAI;;;;;gBAkBpB;;;;;;0BAGF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,WAAU;;sCAEV,6LAAC,6MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;;;;;;AAM7C;GAhDgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 433, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mQACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default: \"border-transparent bg-blue-600 text-white hover:bg-blue-700\",\n        secondary: \"border-transparent bg-gray-100 text-gray-900 hover:bg-gray-200\",\n        destructive: \"border-transparent bg-red-600 text-white hover:bg-red-700\",\n        outline: \"text-gray-900 border-gray-300\",\n        success: \"border-transparent bg-green-100 text-green-800 hover:bg-green-200\",\n        warning: \"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200\",\n        error: \"border-transparent bg-red-100 text-red-800 hover:bg-red-200\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,8KACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 520, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { Bell, Search, User } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\n\ninterface HeaderProps {\n  title: string\n  subtitle?: string\n}\n\nexport function Header({ title, subtitle }: HeaderProps) {\n  return (\n    <header className=\"border-b bg-white px-6 py-4\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">{title}</h1>\n          {subtitle && (\n            <p className=\"text-sm text-gray-600\">{subtitle}</p>\n          )}\n        </div>\n        \n        <div className=\"flex items-center space-x-4\">\n          {/* Search */}\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400\" />\n            <Input\n              placeholder=\"Search deals, resellers...\"\n              className=\"w-64 pl-10\"\n            />\n          </div>\n          \n          {/* Notifications */}\n          <Button variant=\"ghost\" size=\"icon\" className=\"relative\">\n            <Bell className=\"h-5 w-5\" />\n            <Badge \n              variant=\"destructive\" \n              className=\"absolute -right-1 -top-1 h-5 w-5 rounded-full p-0 text-xs\"\n            >\n              3\n            </Badge>\n          </Button>\n          \n          {/* User Menu */}\n          <Button variant=\"ghost\" size=\"icon\">\n            <User className=\"h-5 w-5\" />\n          </Button>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;AALA;;;;;;AAYO,SAAS,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAe;IACrD,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAAoC;;;;;;wBACjD,0BACC,6LAAC;4BAAE,WAAU;sCAAyB;;;;;;;;;;;;8BAI1C,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,WAAU;;;;;;;;;;;;sCAKd,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAO,WAAU;;8CAC5C,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,SAAQ;oCACR,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;sCAC3B,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B;KAxCgB", "debugId": null}}, {"offset": {"line": 665, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/components/layout/main-layout.tsx"], "sourcesContent": ["import { Navigation } from './navigation'\nimport { Header } from './header'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n  title: string\n  subtitle?: string\n}\n\nexport function MainLayout({ children, title, subtitle }: MainLayoutProps) {\n  return (\n    <div className=\"flex h-screen bg-gray-100\">\n      <Navigation />\n      \n      <div className=\"flex flex-1 flex-col overflow-hidden\">\n        <Header title={title} subtitle={subtitle} />\n        \n        <main className=\"flex-1 overflow-auto p-6\">\n          {children}\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQO,SAAS,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAmB;IACvE,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6IAAA,CAAA,aAAU;;;;;0BAEX,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAO,UAAU;;;;;;kCAEhC,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;KAdgB", "debugId": null}}, {"offset": {"line": 727, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border border-gray-200 bg-white text-gray-900 shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-gray-600\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sEACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 830, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/app/settings/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { MainLayout } from '@/components/layout/main-layout'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport { Settings, Users, Shield, Database, Bell, Mail } from 'lucide-react'\n\nexport default function SettingsPage() {\n  const [activeTab, setActiveTab] = useState('general')\n\n  const tabs = [\n    { id: 'general', label: 'General', icon: Settings },\n    { id: 'users', label: 'Users & Permissions', icon: Users },\n    { id: 'security', label: 'Security', icon: Shield },\n    { id: 'database', label: 'Database', icon: Database },\n    { id: 'notifications', label: 'Notifications', icon: Bell },\n  ]\n\n  const renderGeneralSettings = () => (\n    <div className=\"space-y-6\">\n      <Card>\n        <CardHeader>\n          <CardTitle>Application Settings</CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div>\n            <label className=\"block text-sm font-medium mb-2\">Application Name</label>\n            <Input defaultValue=\"Deal Registration System\" />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium mb-2\">Company Name</label>\n            <Input defaultValue=\"Your Company\" />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium mb-2\">Support Email</label>\n            <Input defaultValue=\"<EMAIL>\" type=\"email\" />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium mb-2\">Default Territory</label>\n            <Input defaultValue=\"Global\" />\n          </div>\n        </CardContent>\n      </Card>\n\n      <Card>\n        <CardHeader>\n          <CardTitle>Deal Registration Rules</CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h4 className=\"font-medium\">Auto-assign deals</h4>\n              <p className=\"text-sm text-gray-600\">Automatically assign deals based on territory rules</p>\n            </div>\n            <Button variant=\"outline\" size=\"sm\">Configure</Button>\n          </div>\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h4 className=\"font-medium\">Conflict detection</h4>\n              <p className=\"text-sm text-gray-600\">Enable automatic conflict detection for overlapping deals</p>\n            </div>\n            <Badge variant=\"success\">Enabled</Badge>\n          </div>\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h4 className=\"font-medium\">Approval workflow</h4>\n              <p className=\"text-sm text-gray-600\">Require manager approval for high-value deals</p>\n            </div>\n            <Button variant=\"outline\" size=\"sm\">Configure</Button>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderUsersSettings = () => (\n    <div className=\"space-y-6\">\n      <Card>\n        <CardHeader>\n          <CardTitle>Staff Users</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between p-4 border rounded-lg\">\n              <div>\n                <h4 className=\"font-medium\">System Admin</h4>\n                <p className=\"text-sm text-gray-600\"><EMAIL></p>\n              </div>\n              <Badge>Admin</Badge>\n            </div>\n            <div className=\"flex items-center justify-between p-4 border rounded-lg\">\n              <div>\n                <h4 className=\"font-medium\">Deal Manager</h4>\n                <p className=\"text-sm text-gray-600\"><EMAIL></p>\n              </div>\n              <Badge variant=\"secondary\">Manager</Badge>\n            </div>\n            <div className=\"flex items-center justify-between p-4 border rounded-lg\">\n              <div>\n                <h4 className=\"font-medium\">Deal Staff</h4>\n                <p className=\"text-sm text-gray-600\"><EMAIL></p>\n              </div>\n              <Badge variant=\"outline\">Staff</Badge>\n            </div>\n          </div>\n          <Button className=\"w-full mt-4\">Add New User</Button>\n        </CardContent>\n      </Card>\n\n      <Card>\n        <CardHeader>\n          <CardTitle>Role Permissions</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            <div>\n              <h4 className=\"font-medium mb-2\">Admin</h4>\n              <p className=\"text-sm text-gray-600\">Full system access, user management, settings configuration</p>\n            </div>\n            <div>\n              <h4 className=\"font-medium mb-2\">Manager</h4>\n              <p className=\"text-sm text-gray-600\">Deal assignment, conflict resolution, reporting</p>\n            </div>\n            <div>\n              <h4 className=\"font-medium mb-2\">Staff</h4>\n              <p className=\"text-sm text-gray-600\">View deals, basic conflict management</p>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderSecuritySettings = () => (\n    <div className=\"space-y-6\">\n      <Card>\n        <CardHeader>\n          <CardTitle>Authentication</CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h4 className=\"font-medium\">Two-Factor Authentication</h4>\n              <p className=\"text-sm text-gray-600\">Require 2FA for all users</p>\n            </div>\n            <Badge variant=\"warning\">Disabled</Badge>\n          </div>\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h4 className=\"font-medium\">Session Timeout</h4>\n              <p className=\"text-sm text-gray-600\">Auto-logout after inactivity</p>\n            </div>\n            <span className=\"text-sm\">8 hours</span>\n          </div>\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h4 className=\"font-medium\">Password Policy</h4>\n              <p className=\"text-sm text-gray-600\">Minimum password requirements</p>\n            </div>\n            <Button variant=\"outline\" size=\"sm\">Configure</Button>\n          </div>\n        </CardContent>\n      </Card>\n\n      <Card>\n        <CardHeader>\n          <CardTitle>Data Protection</CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h4 className=\"font-medium\">Row Level Security</h4>\n              <p className=\"text-sm text-gray-600\">Database-level access control</p>\n            </div>\n            <Badge variant=\"success\">Enabled</Badge>\n          </div>\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h4 className=\"font-medium\">Audit Logging</h4>\n              <p className=\"text-sm text-gray-600\">Track all system changes</p>\n            </div>\n            <Badge variant=\"success\">Enabled</Badge>\n          </div>\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h4 className=\"font-medium\">Data Encryption</h4>\n              <p className=\"text-sm text-gray-600\">Encrypt sensitive data at rest</p>\n            </div>\n            <Badge variant=\"success\">Enabled</Badge>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderDatabaseSettings = () => (\n    <div className=\"space-y-6\">\n      <Card>\n        <CardHeader>\n          <CardTitle>Database Status</CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <span>Connection Status</span>\n            <Badge variant=\"success\">Connected</Badge>\n          </div>\n          <div className=\"flex items-center justify-between\">\n            <span>Database Version</span>\n            <span className=\"text-sm\">PostgreSQL 15.3</span>\n          </div>\n          <div className=\"flex items-center justify-between\">\n            <span>Total Tables</span>\n            <span className=\"text-sm\">9</span>\n          </div>\n          <div className=\"flex items-center justify-between\">\n            <span>Last Backup</span>\n            <span className=\"text-sm\">2 hours ago</span>\n          </div>\n        </CardContent>\n      </Card>\n\n      <Card>\n        <CardHeader>\n          <CardTitle>Maintenance</CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <Button variant=\"outline\" className=\"w-full\">Run Database Cleanup</Button>\n          <Button variant=\"outline\" className=\"w-full\">Rebuild Indexes</Button>\n          <Button variant=\"outline\" className=\"w-full\">Export Data</Button>\n          <Button variant=\"destructive\" className=\"w-full\">Reset Sample Data</Button>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderNotificationsSettings = () => (\n    <div className=\"space-y-6\">\n      <Card>\n        <CardHeader>\n          <CardTitle>Email Notifications</CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h4 className=\"font-medium\">New Deal Submissions</h4>\n              <p className=\"text-sm text-gray-600\">Notify when new deals are registered</p>\n            </div>\n            <Badge variant=\"success\">Enabled</Badge>\n          </div>\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h4 className=\"font-medium\">Conflict Alerts</h4>\n              <p className=\"text-sm text-gray-600\">Notify when conflicts are detected</p>\n            </div>\n            <Badge variant=\"success\">Enabled</Badge>\n          </div>\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h4 className=\"font-medium\">Assignment Updates</h4>\n              <p className=\"text-sm text-gray-600\">Notify when deals are assigned</p>\n            </div>\n            <Badge variant=\"warning\">Disabled</Badge>\n          </div>\n        </CardContent>\n      </Card>\n\n      <Card>\n        <CardHeader>\n          <CardTitle>System Alerts</CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h4 className=\"font-medium\">Database Errors</h4>\n              <p className=\"text-sm text-gray-600\">Critical system notifications</p>\n            </div>\n            <Badge variant=\"success\">Enabled</Badge>\n          </div>\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h4 className=\"font-medium\">Performance Warnings</h4>\n              <p className=\"text-sm text-gray-600\">System performance alerts</p>\n            </div>\n            <Badge variant=\"success\">Enabled</Badge>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'general': return renderGeneralSettings()\n      case 'users': return renderUsersSettings()\n      case 'security': return renderSecuritySettings()\n      case 'database': return renderDatabaseSettings()\n      case 'notifications': return renderNotificationsSettings()\n      default: return renderGeneralSettings()\n    }\n  }\n\n  return (\n    <MainLayout title=\"Settings\" subtitle=\"Configure system preferences and options\">\n      <div className=\"flex gap-6\">\n        {/* Sidebar */}\n        <div className=\"w-64 space-y-2\">\n          {tabs.map((tab) => {\n            const Icon = tab.icon\n            return (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id)}\n                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${\n                  activeTab === tab.id\n                    ? 'bg-blue-100 text-blue-700'\n                    : 'text-gray-600 hover:bg-gray-100'\n                }`}\n              >\n                <Icon className=\"h-5 w-5\" />\n                <span className=\"text-sm font-medium\">{tab.label}</span>\n              </button>\n            )\n          })}\n        </div>\n\n        {/* Content */}\n        <div className=\"flex-1\">\n          {renderTabContent()}\n        </div>\n      </div>\n    </MainLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,OAAO;QACX;YAAE,IAAI;YAAW,OAAO;YAAW,MAAM,6MAAA,CAAA,WAAQ;QAAC;QAClD;YAAE,IAAI;YAAS,OAAO;YAAuB,MAAM,uMAAA,CAAA,QAAK;QAAC;QACzD;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,yMAAA,CAAA,SAAM;QAAC;QAClD;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,6MAAA,CAAA,WAAQ;QAAC;QACpD;YAAE,IAAI;YAAiB,OAAO;YAAiB,MAAM,qMAAA,CAAA,OAAI;QAAC;KAC3D;IAED,MAAM,wBAAwB,kBAC5B,6LAAC;YAAI,WAAU;;8BACb,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAiC;;;;;;sDAClD,6LAAC,oIAAA,CAAA,QAAK;4CAAC,cAAa;;;;;;;;;;;;8CAEtB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAiC;;;;;;sDAClD,6LAAC,oIAAA,CAAA,QAAK;4CAAC,cAAa;;;;;;;;;;;;8CAEtB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAiC;;;;;;sDAClD,6LAAC,oIAAA,CAAA,QAAK;4CAAC,cAAa;4CAAsB,MAAK;;;;;;;;;;;;8CAEjD,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAiC;;;;;;sDAClD,6LAAC,oIAAA,CAAA,QAAK;4CAAC,cAAa;;;;;;;;;;;;;;;;;;;;;;;;8BAK1B,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAc;;;;;;8DAC5B,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;8CAEtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAc;;;;;;8DAC5B,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;;;;;;;8CAE3B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAc;;;;;;8DAC5B,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAO9C,MAAM,sBAAsB,kBAC1B,6LAAC;YAAI,WAAU;;8BACb,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,6LAAC,mIAAA,CAAA,cAAW;;8CACV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAc;;;;;;sEAC5B,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAEvC,6LAAC,oIAAA,CAAA,QAAK;8DAAC;;;;;;;;;;;;sDAET,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAc;;;;;;sEAC5B,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAEvC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAY;;;;;;;;;;;;sDAE7B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAc;;;;;;sEAC5B,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAEvC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;;;;;;;;;;;;;8CAG7B,6LAAC,qIAAA,CAAA,SAAM;oCAAC,WAAU;8CAAc;;;;;;;;;;;;;;;;;;8BAIpC,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmB;;;;;;0DACjC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmB;;;;;;0DACjC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmB;;;;;;0DACjC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQjD,MAAM,yBAAyB,kBAC7B,6LAAC;YAAI,WAAU;;8BACb,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAc;;;;;;8DAC5B,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;;;;;;;8CAE3B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAc;;;;;;8DAC5B,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;8CAE5B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAc;;;;;;8DAC5B,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;8BAK1C,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAc;;;;;;8DAC5B,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;;;;;;;8CAE3B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAc;;;;;;8DAC5B,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;;;;;;;8CAE3B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAc;;;;;;8DAC5B,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOnC,MAAM,yBAAyB,kBAC7B,6LAAC;YAAI,WAAU;;8BACb,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;;;;;;;8CAE3B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;8CAE5B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;8CAE5B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;8BAKhC,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;8CAAS;;;;;;8CAC7C,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;8CAAS;;;;;;8CAC7C,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;8CAAS;;;;;;8CAC7C,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAc,WAAU;8CAAS;;;;;;;;;;;;;;;;;;;;;;;;IAMzD,MAAM,8BAA8B,kBAClC,6LAAC;YAAI,WAAU;;8BACb,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAc;;;;;;8DAC5B,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;;;;;;;8CAE3B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAc;;;;;;8DAC5B,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;;;;;;;8CAE3B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAc;;;;;;8DAC5B,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;8BAK/B,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAc;;;;;;8DAC5B,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;;;;;;;8CAE3B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAc;;;;;;8DAC5B,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOnC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAiB,OAAO;YAC7B;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC,iJAAA,CAAA,aAAU;QAAC,OAAM;QAAW,UAAS;kBACpC,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC;wBACT,MAAM,OAAO,IAAI,IAAI;wBACrB,qBACE,6LAAC;4BAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4BAClC,WAAW,CAAC,oFAAoF,EAC9F,cAAc,IAAI,EAAE,GAChB,8BACA,mCACJ;;8CAEF,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;8CAAuB,IAAI,KAAK;;;;;;;2BAT3C,IAAI,EAAE;;;;;oBAYjB;;;;;;8BAIF,6LAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX;GArUwB;KAAA", "debugId": null}}]}