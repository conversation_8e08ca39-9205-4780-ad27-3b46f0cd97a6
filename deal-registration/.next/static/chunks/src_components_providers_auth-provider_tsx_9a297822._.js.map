{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/components/providers/auth-provider.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { createClientComponentClient } from '@supabase/auth-helpers-nextjs'\nimport { useRouter } from 'next/navigation'\nimport type { User } from '@supabase/auth-helpers-nextjs'\n\ntype AuthContextType = {\n  user: User | null\n  loading: boolean\n  signOut: () => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType>({\n  user: null,\n  loading: true,\n  signOut: async () => {},\n})\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n  const router = useRouter()\n  const supabase = createClientComponentClient()\n\n  useEffect(() => {\n    const getUser = async () => {\n      const { data: { user } } = await supabase.auth.getUser()\n      setUser(user)\n      setLoading(false)\n    }\n\n    getUser()\n\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setUser(session?.user ?? null)\n        setLoading(false)\n        \n        if (event === 'SIGNED_IN') {\n          router.push('/')\n        } else if (event === 'SIGNED_OUT') {\n          router.push('/auth/login')\n        }\n        \n        router.refresh()\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [supabase, router])\n\n  const signOut = async () => {\n    await supabase.auth.signOut()\n  }\n\n  const value = {\n    user,\n    loading,\n    signOut,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAaA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAmB;IACjD,MAAM;IACN,SAAS;IACT,SAAS,WAAa;AACxB;AAEO,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAQN,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,2KAAA,CAAA,8BAA2B,AAAD;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;kDAAU;oBACd,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;oBACtD,QAAQ;oBACR,WAAW;gBACb;;YAEA;YAEA,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB;0CAChE,OAAO,OAAO;oBACZ,QAAQ,SAAS,QAAQ;oBACzB,WAAW;oBAEX,IAAI,UAAU,aAAa;wBACzB,OAAO,IAAI,CAAC;oBACd,OAAO,IAAI,UAAU,cAAc;wBACjC,OAAO,IAAI,CAAC;oBACd;oBAEA,OAAO,OAAO;gBAChB;;YAGF;0CAAO,IAAM,aAAa,WAAW;;QACvC;iCAAG;QAAC;QAAU;KAAO;IAErB,MAAM,UAAU;QACd,MAAM,SAAS,IAAI,CAAC,OAAO;IAC7B;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;IAhDgB;;QAGC,qIAAA,CAAA,YAAS;;;KAHV", "debugId": null}}]}