{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-GB', {\n    style: 'currency',\n    currency: 'GBP',\n  }).format(amount)\n}\n\n// Date formatting utilities\nexport function formatDate(date: string | Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(new Date(date))\n}\n\nexport function formatDateTime(date: string | Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n\n\n\n// String utilities\nexport function capitalizeFirst(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1)\n}\n\nexport function slugify(str: string): string {\n  return str\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '')\n}\n\n// Fuzzy matching for duplicate detection\nexport function calculateSimilarity(str1: string, str2: string): number {\n  const longer = str1.length > str2.length ? str1 : str2\n  const shorter = str1.length > str2.length ? str2 : str1\n  \n  if (longer.length === 0) return 1.0\n  \n  const distance = levenshteinDistance(longer, shorter)\n  return (longer.length - distance) / longer.length\n}\n\nfunction levenshteinDistance(str1: string, str2: string): number {\n  const matrix = []\n  \n  for (let i = 0; i <= str2.length; i++) {\n    matrix[i] = [i]\n  }\n  \n  for (let j = 0; j <= str1.length; j++) {\n    matrix[0][j] = j\n  }\n  \n  for (let i = 1; i <= str2.length; i++) {\n    for (let j = 1; j <= str1.length; j++) {\n      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {\n        matrix[i][j] = matrix[i - 1][j - 1]\n      } else {\n        matrix[i][j] = Math.min(\n          matrix[i - 1][j - 1] + 1,\n          matrix[i][j - 1] + 1,\n          matrix[i - 1][j] + 1\n        )\n      }\n    }\n  }\n  \n  return matrix[str2.length][str1.length]\n}\n\n// Company name normalization for better matching\nexport function normalizeCompanyName(name: string): string {\n  return name\n    .toLowerCase()\n    .replace(/\\b(inc|corp|corporation|ltd|limited|llc|co)\\b\\.?/g, '')\n    .replace(/[^\\w\\s]/g, '')\n    .replace(/\\s+/g, ' ')\n    .trim()\n}\n\n// Territory overlap detection\nexport function checkTerritoryOverlap(territory1: string, territory2: string): boolean {\n  const t1 = territory1.toLowerCase().trim()\n  const t2 = territory2.toLowerCase().trim()\n  \n  // Exact match\n  if (t1 === t2) return true\n  \n  // Check for common territory patterns\n  const patterns = [\n    /^(north|south|east|west)\\s*(america|us|usa|united states)$/,\n    /^(northeast|northwest|southeast|southwest)\\s*(region|territory)?$/,\n    /^(global|worldwide|international)$/,\n    /^(enterprise|commercial|federal)$/,\n  ]\n  \n  for (const pattern of patterns) {\n    if (pattern.test(t1) && pattern.test(t2)) return true\n  }\n  \n  return false\n}\n\n// Deal value comparison with tolerance\nexport function isDealValueSimilar(value1: number, value2: number, tolerance = 0.1): boolean {\n  const diff = Math.abs(value1 - value2)\n  const avg = (value1 + value2) / 2\n  return diff / avg <= tolerance\n}\n\n// Time window checking\nexport function isWithinTimeWindow(date1: string | Date, date2: string | Date, windowDays = 90): boolean {\n  const d1 = new Date(date1)\n  const d2 = new Date(date2)\n  const diffTime = Math.abs(d2.getTime() - d1.getTime())\n  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))\n  return diffDays <= windowDays\n}\n\n// Validation utilities\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function isValidUUID(uuid: string): boolean {\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i\n  return uuidRegex.test(uuid)\n}\n\n// Array utilities\nexport function groupBy<T, K extends keyof any>(array: T[], key: (item: T) => K): Record<K, T[]> {\n  return array.reduce((groups, item) => {\n    const group = key(item)\n    groups[group] = groups[group] || []\n    groups[group].push(item)\n    return groups\n  }, {} as Record<K, T[]>)\n}\n\nexport function sortBy<T>(array: T[], key: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] {\n  return [...array].sort((a, b) => {\n    const aVal = a[key]\n    const bVal = b[key]\n    \n    if (aVal < bVal) return direction === 'asc' ? -1 : 1\n    if (aVal > bVal) return direction === 'asc' ? 1 : -1\n    return 0\n  })\n}\n\n// Debounce utility for search\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null\n  \n  return (...args: Parameters<T>) => {\n    if (timeout) clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\n// Error handling\nexport function getErrorMessage(error: unknown): string {\n  if (error instanceof Error) return error.message\n  if (typeof error === 'string') return error\n  return 'An unknown error occurred'\n}\n\n// Status badge colors\nexport function getStatusColor(status: string): string {\n  const colors: Record<string, string> = {\n    pending: 'bg-yellow-100 text-yellow-800',\n    assigned: 'bg-green-100 text-green-800',\n    disputed: 'bg-red-100 text-red-800',\n    approved: 'bg-blue-100 text-blue-800',\n    rejected: 'bg-gray-100 text-gray-800',\n    active: 'bg-green-100 text-green-800',\n    inactive: 'bg-gray-100 text-gray-800',\n    resolved: 'bg-green-100 text-green-800',\n    dismissed: 'bg-gray-100 text-gray-800',\n  }\n  \n  return colors[status] || 'bg-gray-100 text-gray-800'\n}\n\n// Priority calculation for conflicts\nexport function calculateConflictPriority(\n  dealValue: number,\n  conflictType: string,\n  daysSinceSubmission: number\n): 'high' | 'medium' | 'low' {\n  let score = 0\n  \n  // Deal value weight\n  if (dealValue > 100000) score += 3\n  else if (dealValue > 50000) score += 2\n  else score += 1\n  \n  // Conflict type weight\n  if (conflictType === 'duplicate_end_user') score += 3\n  else if (conflictType === 'territory_overlap') score += 2\n  else score += 1\n  \n  // Time weight\n  if (daysSinceSubmission > 7) score += 2\n  else if (daysSinceSubmission > 3) score += 1\n  \n  if (score >= 7) return 'high'\n  if (score >= 4) return 'medium'\n  return 'low'\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,eAAe,IAAmB;IAChD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAKO,SAAS,gBAAgB,GAAW;IACzC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD;AAEO,SAAS,QAAQ,GAAW;IACjC,OAAO,IACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAGO,SAAS,oBAAoB,IAAY,EAAE,IAAY;IAC5D,MAAM,SAAS,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,OAAO;IAClD,MAAM,UAAU,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,OAAO;IAEnD,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;IAEhC,MAAM,WAAW,oBAAoB,QAAQ;IAC7C,OAAO,CAAC,OAAO,MAAM,GAAG,QAAQ,IAAI,OAAO,MAAM;AACnD;AAEA,SAAS,oBAAoB,IAAY,EAAE,IAAY;IACrD,MAAM,SAAS,EAAE;IAEjB,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,MAAM,EAAE,IAAK;QACrC,MAAM,CAAC,EAAE,GAAG;YAAC;SAAE;IACjB;IAEA,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,MAAM,EAAE,IAAK;QACrC,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG;IACjB;IAEA,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,MAAM,EAAE,IAAK;QACrC,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,MAAM,EAAE,IAAK;YACrC,IAAI,KAAK,MAAM,CAAC,IAAI,OAAO,KAAK,MAAM,CAAC,IAAI,IAAI;gBAC7C,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE;YACrC,OAAO;gBACL,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,GAAG,CACrB,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,GAAG,GACvB,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,GACnB,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG;YAEvB;QACF;IACF;IAEA,OAAO,MAAM,CAAC,KAAK,MAAM,CAAC,CAAC,KAAK,MAAM,CAAC;AACzC;AAGO,SAAS,qBAAqB,IAAY;IAC/C,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,qDAAqD,IAC7D,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,QAAQ,KAChB,IAAI;AACT;AAGO,SAAS,sBAAsB,UAAkB,EAAE,UAAkB;IAC1E,MAAM,KAAK,WAAW,WAAW,GAAG,IAAI;IACxC,MAAM,KAAK,WAAW,WAAW,GAAG,IAAI;IAExC,cAAc;IACd,IAAI,OAAO,IAAI,OAAO;IAEtB,sCAAsC;IACtC,MAAM,WAAW;QACf;QACA;QACA;QACA;KACD;IAED,KAAK,MAAM,WAAW,SAAU;QAC9B,IAAI,QAAQ,IAAI,CAAC,OAAO,QAAQ,IAAI,CAAC,KAAK,OAAO;IACnD;IAEA,OAAO;AACT;AAGO,SAAS,mBAAmB,MAAc,EAAE,MAAc,EAAE,YAAY,GAAG;IAChF,MAAM,OAAO,KAAK,GAAG,CAAC,SAAS;IAC/B,MAAM,MAAM,CAAC,SAAS,MAAM,IAAI;IAChC,OAAO,OAAO,OAAO;AACvB;AAGO,SAAS,mBAAmB,KAAoB,EAAE,KAAoB,EAAE,aAAa,EAAE;IAC5F,MAAM,KAAK,IAAI,KAAK;IACpB,MAAM,KAAK,IAAI,KAAK;IACpB,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,OAAO,KAAK,GAAG,OAAO;IACnD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAC1D,OAAO,YAAY;AACrB;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,YAAY,IAAY;IACtC,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB;AAGO,SAAS,QAAgC,KAAU,EAAE,GAAmB;IAC7E,OAAO,MAAM,MAAM,CAAC,CAAC,QAAQ;QAC3B,MAAM,QAAQ,IAAI;QAClB,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,EAAE;QACnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;QACnB,OAAO;IACT,GAAG,CAAC;AACN;AAEO,SAAS,OAAU,KAAU,EAAE,GAAY,EAAE,YAA4B,KAAK;IACnF,OAAO;WAAI;KAAM,CAAC,IAAI,CAAC,CAAC,GAAG;QACzB,MAAM,OAAO,CAAC,CAAC,IAAI;QACnB,MAAM,OAAO,CAAC,CAAC,IAAI;QAEnB,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,CAAC,IAAI;QACnD,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,IAAI,CAAC;QACnD,OAAO;IACT;AACF;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO,CAAC,GAAG;QACT,IAAI,SAAS,aAAa;QAC1B,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,gBAAgB,KAAc;IAC5C,IAAI,iBAAiB,OAAO,OAAO,MAAM,OAAO;IAChD,IAAI,OAAO,UAAU,UAAU,OAAO;IACtC,OAAO;AACT;AAGO,SAAS,eAAe,MAAc;IAC3C,MAAM,SAAiC;QACrC,SAAS;QACT,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;QACV,WAAW;IACb;IAEA,OAAO,MAAM,CAAC,OAAO,IAAI;AAC3B;AAGO,SAAS,0BACd,SAAiB,EACjB,YAAoB,EACpB,mBAA2B;IAE3B,IAAI,QAAQ;IAEZ,oBAAoB;IACpB,IAAI,YAAY,QAAQ,SAAS;SAC5B,IAAI,YAAY,OAAO,SAAS;SAChC,SAAS;IAEd,uBAAuB;IACvB,IAAI,iBAAiB,sBAAsB,SAAS;SAC/C,IAAI,iBAAiB,qBAAqB,SAAS;SACnD,SAAS;IAEd,cAAc;IACd,IAAI,sBAAsB,GAAG,SAAS;SACjC,IAAI,sBAAsB,GAAG,SAAS;IAE3C,IAAI,SAAS,GAAG,OAAO;IACvB,IAAI,SAAS,GAAG,OAAO;IACvB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-blue-600 text-white hover:bg-blue-700\",\n        destructive: \"bg-red-600 text-white hover:bg-red-700\",\n        outline: \"border border-gray-300 bg-white hover:bg-gray-50\",\n        secondary: \"bg-gray-100 text-gray-900 hover:bg-gray-200\",\n        ghost: \"hover:bg-gray-100\",\n        link: \"text-blue-600 underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,uQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/components/layout/navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { cn } from '@/lib/utils'\nimport { Button } from '@/components/ui/button'\nimport { useAuth } from '@/components/providers/auth-provider'\nimport {\n  LayoutDashboard,\n  FileText,\n  Users,\n  Building2,\n  Package,\n  AlertTriangle,\n  Settings,\n  LogOut\n} from 'lucide-react'\n\nconst navigation = [\n  {\n    name: 'Dashboard',\n    href: '/',\n    icon: LayoutDashboard,\n  },\n  {\n    name: 'Deal Registration',\n    href: '/deals/new',\n    icon: FileText,\n  },\n  {\n    name: 'All Deals',\n    href: '/deals',\n    icon: FileText,\n  },\n  {\n    name: 'Conflicts',\n    href: '/conflicts',\n    icon: AlertTriangle,\n  },\n  {\n    name: 'Resellers',\n    href: '/resellers',\n    icon: Users,\n  },\n  {\n    name: 'End Users',\n    href: '/end-users',\n    icon: Building2,\n  },\n  {\n    name: 'Products',\n    href: '/products',\n    icon: Package,\n  },\n  {\n    name: 'Settings',\n    href: '/settings',\n    icon: Settings,\n  },\n]\n\nexport function Navigation() {\n  const pathname = usePathname()\n  const { signOut } = useAuth()\n\n  return (\n    <div className=\"flex h-full w-64 flex-col bg-gray-900\">\n      <div className=\"flex h-16 items-center px-6\">\n        <h1 className=\"text-xl font-bold text-white\">Deal Registration</h1>\n      </div>\n      \n      <nav className=\"flex-1 space-y-1 px-3 py-4\">\n        {navigation.map((item) => {\n          const isActive = pathname === item.href || \n            (item.href !== '/' && pathname.startsWith(item.href))\n          \n          return (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={cn(\n                'group flex items-center rounded-md px-3 py-2 text-sm font-medium transition-colors',\n                isActive\n                  ? 'bg-gray-800 text-white'\n                  : 'text-gray-300 hover:bg-gray-700 hover:text-white'\n              )}\n            >\n              <item.icon\n                className={cn(\n                  'mr-3 h-5 w-5 flex-shrink-0',\n                  isActive ? 'text-white' : 'text-gray-400 group-hover:text-white'\n                )}\n              />\n              {item.name}\n            </Link>\n          )\n        })}\n      </nav>\n\n      <div className=\"border-t border-gray-700 p-4\">\n        <Button\n          variant=\"ghost\"\n          className=\"w-full justify-start text-gray-300 hover:bg-gray-700 hover:text-white\"\n          onClick={signOut}\n        >\n          <LogOut className=\"mr-3 h-5 w-5\" />\n          Sign Out\n        </Button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAkBA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,MAAM,+NAAA,CAAA,kBAAe;IACvB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,iNAAA,CAAA,WAAQ;IAChB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,iNAAA,CAAA,WAAQ;IAChB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,2NAAA,CAAA,gBAAa;IACrB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;IACb;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,mNAAA,CAAA,YAAS;IACjB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,2MAAA,CAAA,UAAO;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;IAChB;CACD;AAEM,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,sJAAA,CAAA,UAAO,AAAD;IAE1B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAG,WAAU;8BAA+B;;;;;;;;;;;0BAG/C,6LAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC;oBACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACpC,KAAK,IAAI,KAAK,OAAO,SAAS,UAAU,CAAC,KAAK,IAAI;oBAErD,qBACE,6LAAC,+JAAA,CAAA,UAAI;wBAEH,MAAM,KAAK,IAAI;wBACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sFACA,WACI,2BACA;;0CAGN,6LAAC,KAAK,IAAI;gCACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8BACA,WAAW,eAAe;;;;;;4BAG7B,KAAK,IAAI;;uBAfL,KAAK,IAAI;;;;;gBAkBpB;;;;;;0BAGF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,WAAU;oBACV,SAAS;;sCAET,6LAAC,6MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;;;;;;AAM7C;GAlDgB;;QACG,qIAAA,CAAA,cAAW;QACR,sJAAA,CAAA,UAAO;;;KAFb", "debugId": null}}, {"offset": {"line": 438, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mQACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 474, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default: \"border-transparent bg-blue-600 text-white hover:bg-blue-700\",\n        secondary: \"border-transparent bg-gray-100 text-gray-900 hover:bg-gray-200\",\n        destructive: \"border-transparent bg-red-600 text-white hover:bg-red-700\",\n        outline: \"text-gray-900 border-gray-300\",\n        success: \"border-transparent bg-green-100 text-green-800 hover:bg-green-200\",\n        warning: \"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200\",\n        error: \"border-transparent bg-red-100 text-red-800 hover:bg-red-200\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,8KACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { Bell, Search, User } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\n\ninterface HeaderProps {\n  title: string\n  subtitle?: string\n}\n\nexport function Header({ title, subtitle }: HeaderProps) {\n  return (\n    <header className=\"border-b bg-white px-6 py-4\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">{title}</h1>\n          {subtitle && (\n            <p className=\"text-sm text-gray-600\">{subtitle}</p>\n          )}\n        </div>\n        \n        <div className=\"flex items-center space-x-4\">\n          {/* Search */}\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400\" />\n            <Input\n              placeholder=\"Search deals, resellers...\"\n              className=\"w-64 pl-10\"\n            />\n          </div>\n          \n          {/* Notifications */}\n          <Button variant=\"ghost\" size=\"icon\" className=\"relative\">\n            <Bell className=\"h-5 w-5\" />\n            <Badge \n              variant=\"destructive\" \n              className=\"absolute -right-1 -top-1 h-5 w-5 rounded-full p-0 text-xs\"\n            >\n              3\n            </Badge>\n          </Button>\n          \n          {/* User Menu */}\n          <Button variant=\"ghost\" size=\"icon\">\n            <User className=\"h-5 w-5\" />\n          </Button>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;AALA;;;;;;AAYO,SAAS,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAe;IACrD,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAAoC;;;;;;wBACjD,0BACC,6LAAC;4BAAE,WAAU;sCAAyB;;;;;;;;;;;;8BAI1C,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,WAAU;;;;;;;;;;;;sCAKd,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAO,WAAU;;8CAC5C,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,SAAQ;oCACR,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;sCAC3B,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B;KAxCgB", "debugId": null}}, {"offset": {"line": 670, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/components/layout/main-layout.tsx"], "sourcesContent": ["import { Navigation } from './navigation'\nimport { Header } from './header'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n  title: string\n  subtitle?: string\n}\n\nexport function MainLayout({ children, title, subtitle }: MainLayoutProps) {\n  return (\n    <div className=\"flex h-screen bg-gray-100\">\n      <Navigation />\n      \n      <div className=\"flex flex-1 flex-col overflow-hidden\">\n        <Header title={title} subtitle={subtitle} />\n        \n        <main className=\"flex-1 overflow-auto p-6\">\n          {children}\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQO,SAAS,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAmB;IACvE,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6IAAA,CAAA,aAAU;;;;;0BAEX,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAO,UAAU;;;;;;kCAEhC,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;KAdgB", "debugId": null}}, {"offset": {"line": 732, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border border-gray-200 bg-white text-gray-900 shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-gray-600\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sEACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 835, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/lib/types.ts"], "sourcesContent": ["import { z } from 'zod'\n\n// Enums\nexport const ResellerTier = z.enum(['gold', 'silver', 'bronze'])\nexport const UserStatus = z.enum(['active', 'inactive'])\nexport const DealStatus = z.enum(['pending', 'assigned', 'disputed', 'approved', 'rejected'])\nexport const ConflictType = z.enum(['duplicate_end_user', 'territory_overlap', 'timing_conflict'])\nexport const ResolutionStatus = z.enum(['pending', 'resolved', 'dismissed'])\nexport const StaffRole = z.enum(['admin', 'manager', 'staff'])\n\n// Base schemas\nexport const ResellerSchema = z.object({\n  id: z.string().uuid().optional(),\n  name: z.string().min(1, 'Reseller name is required'),\n  email: z.string().email('Valid email is required'),\n  territory: z.string().min(1, 'Territory is required'),\n  tier: ResellerTier,\n  status: UserStatus.default('active'),\n  created_at: z.string().optional(),\n  updated_at: z.string().optional(),\n})\n\nexport const EndUserSchema = z.object({\n  id: z.string().uuid().optional(),\n  company_name: z.string().min(1, 'Company name is required'),\n  contact_name: z.string().min(1, 'Contact name is required'),\n  contact_email: z.string().email('Valid email is required'),\n  territory: z.string().min(1, 'Territory is required'),\n  created_at: z.string().optional(),\n  updated_at: z.string().optional(),\n})\n\nexport const ProductSchema = z.object({\n  id: z.string().uuid().optional(),\n  name: z.string().min(1, 'Product name is required'),\n  category: z.string().min(1, 'Category is required'),\n  list_price: z.number().positive('Price must be positive'),\n  created_at: z.string().optional(),\n  updated_at: z.string().optional(),\n})\n\nexport const DealProductSchema = z.object({\n  id: z.string().uuid().optional(),\n  deal_id: z.string().uuid().optional(),\n  product_id: z.string().uuid(),\n  quantity: z.number().int().positive('Quantity must be positive'),\n  price: z.number().positive('Price must be positive'),\n  created_at: z.string().optional(),\n})\n\nexport const DealSchema = z.object({\n  id: z.string().uuid().optional(),\n  reseller_id: z.string().uuid(),\n  end_user_id: z.string().uuid(),\n  assigned_reseller_id: z.string().uuid().nullable().optional(),\n  status: DealStatus.default('pending'),\n  total_value: z.number().positive('Total value must be positive'),\n  submission_date: z.string().optional(),\n  assignment_date: z.string().nullable().optional(),\n  created_at: z.string().optional(),\n  updated_at: z.string().optional(),\n})\n\nexport const DealConflictSchema = z.object({\n  id: z.string().uuid().optional(),\n  deal_id: z.string().uuid(),\n  competing_deal_id: z.string().uuid(),\n  conflict_type: ConflictType,\n  resolution_status: ResolutionStatus.default('pending'),\n  assigned_to_staff: z.string().uuid().nullable().optional(),\n  created_at: z.string().optional(),\n  updated_at: z.string().optional(),\n})\n\nexport const StaffUserSchema = z.object({\n  id: z.string().uuid().optional(),\n  email: z.string().email('Valid email is required'),\n  name: z.string().min(1, 'Name is required'),\n  role: StaffRole.default('staff'),\n  created_at: z.string().optional(),\n  updated_at: z.string().optional(),\n})\n\n// Form schemas for creating/updating\nexport const CreateDealSchema = z.object({\n  reseller_id: z.string().uuid('Please select a reseller'),\n  end_user: z.object({\n    id: z.string().uuid().optional(),\n    company_name: z.string().min(1, 'Company name is required'),\n    contact_name: z.string().min(1, 'Contact name is required'),\n    contact_email: z.string().email('Valid email is required'),\n    territory: z.string().min(1, 'Territory is required'),\n  }),\n  products: z.array(z.object({\n    product_id: z.string().uuid('Please select a product'),\n    quantity: z.number().int().positive('Quantity must be positive'),\n    price: z.number().positive('Price must be positive'),\n  })).min(1, 'At least one product is required'),\n})\n\nexport const AssignDealSchema = z.object({\n  deal_id: z.string().uuid(),\n  assigned_reseller_id: z.string().uuid(),\n  reason: z.string().optional(),\n})\n\n// Type exports\nexport type Reseller = z.infer<typeof ResellerSchema>\nexport type EndUser = z.infer<typeof EndUserSchema>\nexport type Product = z.infer<typeof ProductSchema>\nexport type Deal = z.infer<typeof DealSchema>\nexport type DealProduct = z.infer<typeof DealProductSchema>\nexport type DealConflict = z.infer<typeof DealConflictSchema>\nexport type StaffUser = z.infer<typeof StaffUserSchema>\nexport type CreateDeal = z.infer<typeof CreateDealSchema>\nexport type AssignDeal = z.infer<typeof AssignDealSchema>\n\n// Extended types with relationships\nexport type DealWithRelations = Deal & {\n  reseller: Reseller\n  end_user: EndUser\n  assigned_reseller?: Reseller | null\n  products: (DealProduct & { product: Product })[]\n  conflicts: (DealConflict & { competing_deal: Deal })[]\n}\n\nexport type ConflictWithRelations = DealConflict & {\n  deal: DealWithRelations\n  competing_deal: DealWithRelations\n  assigned_staff?: StaffUser | null\n}\n\n// API Response types\nexport type ApiResponse<T> = {\n  data: T | null\n  error: string | null\n  success: boolean\n}\n\nexport type PaginatedResponse<T> = ApiResponse<{\n  items: T[]\n  total: number\n  page: number\n  limit: number\n  totalPages: number\n}>\n\n// Filter and search types\nexport type DealFilters = {\n  status?: string[]\n  reseller_id?: string\n  territory?: string\n  date_from?: string\n  date_to?: string\n  has_conflicts?: boolean\n}\n\nexport type ConflictFilters = {\n  resolution_status?: string[]\n  conflict_type?: string[]\n  assigned_to_staff?: string\n}\n\n// Dashboard metrics\nexport type DashboardMetrics = {\n  total_deals: number\n  pending_deals: number\n  disputed_deals: number\n  assigned_deals: number\n  total_conflicts: number\n  pending_conflicts: number\n  total_value: number\n  avg_resolution_time: number\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;;AAGO,MAAM,eAAe,gLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAAC;IAAQ;IAAU;CAAS;AACxD,MAAM,aAAa,gLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAAC;IAAU;CAAW;AAChD,MAAM,aAAa,gLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAAC;IAAW;IAAY;IAAY;IAAY;CAAW;AACrF,MAAM,eAAe,gLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAAC;IAAsB;IAAqB;CAAkB;AAC1F,MAAM,mBAAmB,gLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAAC;IAAW;IAAY;CAAY;AACpE,MAAM,YAAY,gLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAAC;IAAS;IAAW;CAAQ;AAGtD,MAAM,iBAAiB,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,IAAI,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IAC9B,MAAM,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,WAAW,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,MAAM;IACN,QAAQ,WAAW,OAAO,CAAC;IAC3B,YAAY,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACjC;AAEO,MAAM,gBAAgB,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,IAAI,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IAC9B,cAAc,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAChC,cAAc,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAChC,eAAe,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IAChC,WAAW,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,YAAY,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACjC;AAEO,MAAM,gBAAgB,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,IAAI,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IAC9B,MAAM,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,YAAY,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAChC,YAAY,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACjC;AAEO,MAAM,oBAAoB,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,IAAI,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IAC9B,SAAS,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IACnC,YAAY,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI;IAC3B,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC;IACpC,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC3B,YAAY,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACjC;AAEO,MAAM,aAAa,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,IAAI,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IAC9B,aAAa,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI;IAC5B,aAAa,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI;IAC5B,sBAAsB,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ,GAAG,QAAQ;IAC3D,QAAQ,WAAW,OAAO,CAAC;IAC3B,aAAa,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC,iBAAiB,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACpC,iBAAiB,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC/C,YAAY,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACjC;AAEO,MAAM,qBAAqB,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,IAAI,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IAC9B,SAAS,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI;IACxB,mBAAmB,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI;IAClC,eAAe;IACf,mBAAmB,iBAAiB,OAAO,CAAC;IAC5C,mBAAmB,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ,GAAG,QAAQ;IACxD,YAAY,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACjC;AAEO,MAAM,kBAAkB,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,IAAI,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IAC9B,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,MAAM,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,MAAM,UAAU,OAAO,CAAC;IACxB,YAAY,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACjC;AAGO,MAAM,mBAAmB,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,aAAa,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC;IAC7B,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACjB,IAAI,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;QAC9B,cAAc,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAChC,cAAc,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAChC,eAAe,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;QAChC,WAAW,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B;IACA,UAAU,gLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACzB,YAAY,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC;QAC5B,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC;QACpC,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC7B,IAAI,GAAG,CAAC,GAAG;AACb;AAEO,MAAM,mBAAmB,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,SAAS,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI;IACxB,sBAAsB,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI;IACrC,QAAQ,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC7B", "debugId": null}}, {"offset": {"line": 979, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/app/deals/new/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { MainLayout } from '@/components/layout/main-layout'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport { useForm, useFieldArray } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { CreateDealSchema, type CreateDeal, type Reseller, type Product } from '@/lib/types'\nimport { formatCurrency } from '@/lib/utils'\nimport { Plus, Trash2, AlertTriangle, CheckCircle } from 'lucide-react'\n\ninterface ConflictAlert {\n  type: string\n  severity: 'high' | 'medium' | 'low'\n  message: string\n}\n\nexport default function NewDealPage() {\n  const router = useRouter()\n  const [resellers, setResellers] = useState<Reseller[]>([])\n  const [products, setProducts] = useState<Product[]>([])\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [conflicts, setConflicts] = useState<ConflictAlert[]>([])\n  const [showConflicts, setShowConflicts] = useState(false)\n\n  const {\n    register,\n    control,\n    handleSubmit,\n    watch,\n    setValue,\n    formState: { errors }\n  } = useForm<CreateDeal>({\n    resolver: zodResolver(CreateDealSchema),\n    defaultValues: {\n      products: [{ product_id: '', quantity: 1, price: 0 }]\n    }\n  })\n\n  const { fields, append, remove } = useFieldArray({\n    control,\n    name: 'products'\n  })\n\n  const watchedProducts = watch('products')\n\n  // Load resellers and products\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        const [resellersRes, productsRes] = await Promise.all([\n          fetch('/api/resellers'),\n          fetch('/api/products')\n        ])\n\n        if (resellersRes.ok) {\n          const resellersData = await resellersRes.json()\n          setResellers(resellersData.data.items)\n        }\n\n        if (productsRes.ok) {\n          const productsData = await productsRes.json()\n          setProducts(productsData.data.items)\n        }\n      } catch (error) {\n        console.error('Error loading data:', error)\n      }\n    }\n\n    loadData()\n  }, [])\n\n  // Calculate total value\n  const totalValue = watchedProducts?.reduce((sum, product) => {\n    return sum + (product.quantity * product.price)\n  }, 0) || 0\n\n  // Auto-fill product price when product is selected\n  const handleProductChange = (index: number, productId: string) => {\n    const selectedProduct = products.find(p => p.id === productId)\n    if (selectedProduct) {\n      setValue(`products.${index}.price`, selectedProduct.list_price)\n    }\n  }\n\n  const onSubmit = async (data: CreateDeal) => {\n    setIsSubmitting(true)\n    setConflicts([])\n\n    try {\n      const response = await fetch('/api/deals', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      })\n\n      const result = await response.json()\n\n      if (response.ok) {\n        // Check for conflicts\n        if (result.data.conflicts?.hasConflicts) {\n          const conflictAlerts: ConflictAlert[] = result.data.conflicts.conflicts.map((conflict: any) => ({\n            type: conflict.type,\n            severity: conflict.severity,\n            message: conflict.reason\n          }))\n          \n          setConflicts(conflictAlerts)\n          setShowConflicts(true)\n        } else {\n          // No conflicts, redirect to deal details\n          router.push(`/deals/${result.data.deal.id}`)\n        }\n      } else {\n        console.error('Error creating deal:', result.error)\n        // Handle error (show toast, etc.)\n      }\n    } catch (error) {\n      console.error('Error submitting deal:', error)\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  const handleConflictAcknowledge = () => {\n    setShowConflicts(false)\n    router.push('/deals')\n  }\n\n  return (\n    <MainLayout \n      title=\"Register New Deal\" \n      subtitle=\"Submit a new deal registration for review and assignment\"\n    >\n      <div className=\"max-w-4xl mx-auto\">\n        {showConflicts && conflicts.length > 0 && (\n          <Card className=\"mb-6 border-orange-200 bg-orange-50\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center text-orange-800\">\n                <AlertTriangle className=\"mr-2 h-5 w-5\" />\n                Conflicts Detected\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                {conflicts.map((conflict, index) => (\n                  <div key={index} className=\"flex items-start space-x-3\">\n                    <Badge \n                      variant={conflict.severity === 'high' ? 'error' : 'warning'}\n                      className=\"mt-0.5\"\n                    >\n                      {conflict.severity}\n                    </Badge>\n                    <div>\n                      <p className=\"text-sm font-medium text-orange-800\">\n                        {conflict.type.replace('_', ' ').toUpperCase()}\n                      </p>\n                      <p className=\"text-sm text-orange-700\">{conflict.message}</p>\n                    </div>\n                  </div>\n                ))}\n              </div>\n              <div className=\"mt-4 flex space-x-3\">\n                <Button onClick={handleConflictAcknowledge} variant=\"outline\">\n                  Continue to Deals List\n                </Button>\n                <Button onClick={() => setShowConflicts(false)}>\n                  Edit Deal\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        )}\n\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n          {/* Reseller Selection */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Reseller Information</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium mb-2\">\n                    Submitting Reseller *\n                  </label>\n                  <select\n                    {...register('reseller_id')}\n                    className=\"w-full p-2 border rounded-md\"\n                  >\n                    <option value=\"\">Select a reseller...</option>\n                    {resellers.map((reseller) => (\n                      <option key={reseller.id} value={reseller.id}>\n                        {reseller.name} ({reseller.territory})\n                      </option>\n                    ))}\n                  </select>\n                  {errors.reseller_id && (\n                    <p className=\"text-red-500 text-sm mt-1\">{errors.reseller_id.message}</p>\n                  )}\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* End User Information */}\n          <Card>\n            <CardHeader>\n              <CardTitle>End User Information</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium mb-2\">\n                    Company Name *\n                  </label>\n                  <Input\n                    {...register('end_user.company_name')}\n                    placeholder=\"Enter company name\"\n                  />\n                  {errors.end_user?.company_name && (\n                    <p className=\"text-red-500 text-sm mt-1\">{errors.end_user.company_name.message}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium mb-2\">\n                    Contact Name *\n                  </label>\n                  <Input\n                    {...register('end_user.contact_name')}\n                    placeholder=\"Enter contact name\"\n                  />\n                  {errors.end_user?.contact_name && (\n                    <p className=\"text-red-500 text-sm mt-1\">{errors.end_user.contact_name.message}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium mb-2\">\n                    Contact Email *\n                  </label>\n                  <Input\n                    {...register('end_user.contact_email')}\n                    type=\"email\"\n                    placeholder=\"Enter contact email\"\n                  />\n                  {errors.end_user?.contact_email && (\n                    <p className=\"text-red-500 text-sm mt-1\">{errors.end_user.contact_email.message}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium mb-2\">\n                    Territory *\n                  </label>\n                  <Input\n                    {...register('end_user.territory')}\n                    placeholder=\"Enter territory\"\n                  />\n                  {errors.end_user?.territory && (\n                    <p className=\"text-red-500 text-sm mt-1\">{errors.end_user.territory.message}</p>\n                  )}\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Products */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center justify-between\">\n                Products & Pricing\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => append({ product_id: '', quantity: 1, price: 0 })}\n                >\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  Add Product\n                </Button>\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {fields.map((field, index) => (\n                  <div key={field.id} className=\"grid grid-cols-1 md:grid-cols-4 gap-4 p-4 border rounded-lg\">\n                    <div>\n                      <label className=\"block text-sm font-medium mb-2\">\n                        Product *\n                      </label>\n                      <select\n                        {...register(`products.${index}.product_id`)}\n                        onChange={(e) => handleProductChange(index, e.target.value)}\n                        className=\"w-full p-2 border rounded-md\"\n                      >\n                        <option value=\"\">Select product...</option>\n                        {products.map((product) => (\n                          <option key={product.id} value={product.id}>\n                            {product.name}\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium mb-2\">\n                        Quantity *\n                      </label>\n                      <Input\n                        {...register(`products.${index}.quantity`, { valueAsNumber: true })}\n                        type=\"number\"\n                        min=\"1\"\n                      />\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium mb-2\">\n                        Price *\n                      </label>\n                      <Input\n                        {...register(`products.${index}.price`, { valueAsNumber: true })}\n                        type=\"number\"\n                        min=\"0\"\n                        step=\"0.01\"\n                      />\n                    </div>\n\n                    <div className=\"flex items-end\">\n                      {fields.length > 1 && (\n                        <Button\n                          type=\"button\"\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => remove(index)}\n                        >\n                          <Trash2 className=\"h-4 w-4\" />\n                        </Button>\n                      )}\n                    </div>\n                  </div>\n                ))}\n\n                <div className=\"border-t pt-4\">\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-lg font-medium\">Total Value:</span>\n                    <span className=\"text-xl font-bold\">{formatCurrency(totalValue)}</span>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Submit */}\n          <div className=\"flex justify-end space-x-4\">\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={() => router.back()}\n            >\n              Cancel\n            </Button>\n            <Button\n              type=\"submit\"\n              disabled={isSubmitting}\n            >\n              {isSubmitting ? 'Submitting...' : 'Submit Deal Registration'}\n            </Button>\n          </div>\n        </form>\n      </div>\n    </MainLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;AAbA;;;;;;;;;;;;;AAqBe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAC9D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,EACJ,QAAQ,EACR,OAAO,EACP,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAc;QACtB,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE,sHAAA,CAAA,mBAAgB;QACtC,eAAe;YACb,UAAU;gBAAC;oBAAE,YAAY;oBAAI,UAAU;oBAAG,OAAO;gBAAE;aAAE;QACvD;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,gBAAa,AAAD,EAAE;QAC/C;QACA,MAAM;IACR;IAEA,MAAM,kBAAkB,MAAM;IAE9B,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;kDAAW;oBACf,IAAI;wBACF,MAAM,CAAC,cAAc,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;4BACpD,MAAM;4BACN,MAAM;yBACP;wBAED,IAAI,aAAa,EAAE,EAAE;4BACnB,MAAM,gBAAgB,MAAM,aAAa,IAAI;4BAC7C,aAAa,cAAc,IAAI,CAAC,KAAK;wBACvC;wBAEA,IAAI,YAAY,EAAE,EAAE;4BAClB,MAAM,eAAe,MAAM,YAAY,IAAI;4BAC3C,YAAY,aAAa,IAAI,CAAC,KAAK;wBACrC;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,uBAAuB;oBACvC;gBACF;;YAEA;QACF;gCAAG,EAAE;IAEL,wBAAwB;IACxB,MAAM,aAAa,iBAAiB,OAAO,CAAC,KAAK;QAC/C,OAAO,MAAO,QAAQ,QAAQ,GAAG,QAAQ,KAAK;IAChD,GAAG,MAAM;IAET,mDAAmD;IACnD,MAAM,sBAAsB,CAAC,OAAe;QAC1C,MAAM,kBAAkB,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACpD,IAAI,iBAAiB;YACnB,SAAS,CAAC,SAAS,EAAE,MAAM,MAAM,CAAC,EAAE,gBAAgB,UAAU;QAChE;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,gBAAgB;QAChB,aAAa,EAAE;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,cAAc;gBACzC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,SAAS,EAAE,EAAE;gBACf,sBAAsB;gBACtB,IAAI,OAAO,IAAI,CAAC,SAAS,EAAE,cAAc;oBACvC,MAAM,iBAAkC,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,WAAkB,CAAC;4BAC9F,MAAM,SAAS,IAAI;4BACnB,UAAU,SAAS,QAAQ;4BAC3B,SAAS,SAAS,MAAM;wBAC1B,CAAC;oBAED,aAAa;oBACb,iBAAiB;gBACnB,OAAO;oBACL,yCAAyC;oBACzC,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;gBAC7C;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC,wBAAwB,OAAO,KAAK;YAClD,kCAAkC;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,4BAA4B;QAChC,iBAAiB;QACjB,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC,iJAAA,CAAA,aAAU;QACT,OAAM;QACN,UAAS;kBAET,cAAA,6LAAC;YAAI,WAAU;;gBACZ,iBAAiB,UAAU,MAAM,GAAG,mBACnC,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAI9C,6LAAC,mIAAA,CAAA,cAAW;;8CACV,6LAAC;oCAAI,WAAU;8CACZ,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,6LAAC;4CAAgB,WAAU;;8DACzB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,SAAS,SAAS,QAAQ,KAAK,SAAS,UAAU;oDAClD,WAAU;8DAET,SAAS,QAAQ;;;;;;8DAEpB,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEACV,SAAS,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;sEAE9C,6LAAC;4DAAE,WAAU;sEAA2B,SAAS,OAAO;;;;;;;;;;;;;2CAXlD;;;;;;;;;;8CAgBd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS;4CAA2B,SAAQ;sDAAU;;;;;;sDAG9D,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS,IAAM,iBAAiB;sDAAQ;;;;;;;;;;;;;;;;;;;;;;;;8BAQxD,6LAAC;oBAAK,UAAU,aAAa;oBAAW,WAAU;;sCAEhD,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAiC;;;;;;8DAGlD,6LAAC;oDACE,GAAG,SAAS,cAAc;oDAC3B,WAAU;;sEAEV,6LAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC;gEAAyB,OAAO,SAAS,EAAE;;oEACzC,SAAS,IAAI;oEAAC;oEAAG,SAAS,SAAS;oEAAC;;+DAD1B,SAAS,EAAE;;;;;;;;;;;gDAK3B,OAAO,WAAW,kBACjB,6LAAC;oDAAE,WAAU;8DAA6B,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ9E,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAiC;;;;;;kEAGlD,6LAAC,oIAAA,CAAA,QAAK;wDACH,GAAG,SAAS,wBAAwB;wDACrC,aAAY;;;;;;oDAEb,OAAO,QAAQ,EAAE,8BAChB,6LAAC;wDAAE,WAAU;kEAA6B,OAAO,QAAQ,CAAC,YAAY,CAAC,OAAO;;;;;;;;;;;;0DAIlF,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAiC;;;;;;kEAGlD,6LAAC,oIAAA,CAAA,QAAK;wDACH,GAAG,SAAS,wBAAwB;wDACrC,aAAY;;;;;;oDAEb,OAAO,QAAQ,EAAE,8BAChB,6LAAC;wDAAE,WAAU;kEAA6B,OAAO,QAAQ,CAAC,YAAY,CAAC,OAAO;;;;;;;;;;;;0DAIlF,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAiC;;;;;;kEAGlD,6LAAC,oIAAA,CAAA,QAAK;wDACH,GAAG,SAAS,yBAAyB;wDACtC,MAAK;wDACL,aAAY;;;;;;oDAEb,OAAO,QAAQ,EAAE,+BAChB,6LAAC;wDAAE,WAAU;kEAA6B,OAAO,QAAQ,CAAC,aAAa,CAAC,OAAO;;;;;;;;;;;;0DAInF,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAiC;;;;;;kEAGlD,6LAAC,oIAAA,CAAA,QAAK;wDACH,GAAG,SAAS,qBAAqB;wDAClC,aAAY;;;;;;oDAEb,OAAO,QAAQ,EAAE,2BAChB,6LAAC;wDAAE,WAAU;kEAA6B,OAAO,QAAQ,CAAC,SAAS,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQrF,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;4CAAoC;0DAEvD,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,OAAO;wDAAE,YAAY;wDAAI,UAAU;wDAAG,OAAO;oDAAE;;kEAE9D,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;8CAKvC,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;4CACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC;oDAAmB,WAAU;;sEAC5B,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAAiC;;;;;;8EAGlD,6LAAC;oEACE,GAAG,SAAS,CAAC,SAAS,EAAE,MAAM,WAAW,CAAC,CAAC;oEAC5C,UAAU,CAAC,IAAM,oBAAoB,OAAO,EAAE,MAAM,CAAC,KAAK;oEAC1D,WAAU;;sFAEV,6LAAC;4EAAO,OAAM;sFAAG;;;;;;wEAChB,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;gFAAwB,OAAO,QAAQ,EAAE;0FACvC,QAAQ,IAAI;+EADF,QAAQ,EAAE;;;;;;;;;;;;;;;;;sEAO7B,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAAiC;;;;;;8EAGlD,6LAAC,oIAAA,CAAA,QAAK;oEACH,GAAG,SAAS,CAAC,SAAS,EAAE,MAAM,SAAS,CAAC,EAAE;wEAAE,eAAe;oEAAK,EAAE;oEACnE,MAAK;oEACL,KAAI;;;;;;;;;;;;sEAIR,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAAiC;;;;;;8EAGlD,6LAAC,oIAAA,CAAA,QAAK;oEACH,GAAG,SAAS,CAAC,SAAS,EAAE,MAAM,MAAM,CAAC,EAAE;wEAAE,eAAe;oEAAK,EAAE;oEAChE,MAAK;oEACL,KAAI;oEACJ,MAAK;;;;;;;;;;;;sEAIT,6LAAC;4DAAI,WAAU;sEACZ,OAAO,MAAM,GAAG,mBACf,6LAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,OAAO;0EAEtB,cAAA,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;;;;;;;mDAlDhB,MAAM,EAAE;;;;;0DAyDpB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAsB;;;;;;sEACtC,6LAAC;4DAAK,WAAU;sEAAqB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ9D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS,IAAM,OAAO,IAAI;8CAC3B;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAU;8CAET,eAAe,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhD;GAvWwB;;QACP,qIAAA,CAAA,YAAS;QAcpB,iKAAA,CAAA,UAAO;QAOwB,iKAAA,CAAA,gBAAa;;;KAtB1B", "debugId": null}}]}