{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Date formatting utilities\nexport function formatDate(date: string | Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(new Date(date))\n}\n\nexport function formatDateTime(date: string | Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount)\n}\n\n// String utilities\nexport function capitalizeFirst(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1)\n}\n\nexport function slugify(str: string): string {\n  return str\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '')\n}\n\n// Fuzzy matching for duplicate detection\nexport function calculateSimilarity(str1: string, str2: string): number {\n  const longer = str1.length > str2.length ? str1 : str2\n  const shorter = str1.length > str2.length ? str2 : str1\n  \n  if (longer.length === 0) return 1.0\n  \n  const distance = levenshteinDistance(longer, shorter)\n  return (longer.length - distance) / longer.length\n}\n\nfunction levenshteinDistance(str1: string, str2: string): number {\n  const matrix = []\n  \n  for (let i = 0; i <= str2.length; i++) {\n    matrix[i] = [i]\n  }\n  \n  for (let j = 0; j <= str1.length; j++) {\n    matrix[0][j] = j\n  }\n  \n  for (let i = 1; i <= str2.length; i++) {\n    for (let j = 1; j <= str1.length; j++) {\n      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {\n        matrix[i][j] = matrix[i - 1][j - 1]\n      } else {\n        matrix[i][j] = Math.min(\n          matrix[i - 1][j - 1] + 1,\n          matrix[i][j - 1] + 1,\n          matrix[i - 1][j] + 1\n        )\n      }\n    }\n  }\n  \n  return matrix[str2.length][str1.length]\n}\n\n// Company name normalization for better matching\nexport function normalizeCompanyName(name: string): string {\n  return name\n    .toLowerCase()\n    .replace(/\\b(inc|corp|corporation|ltd|limited|llc|co)\\b\\.?/g, '')\n    .replace(/[^\\w\\s]/g, '')\n    .replace(/\\s+/g, ' ')\n    .trim()\n}\n\n// Territory overlap detection\nexport function checkTerritoryOverlap(territory1: string, territory2: string): boolean {\n  const t1 = territory1.toLowerCase().trim()\n  const t2 = territory2.toLowerCase().trim()\n  \n  // Exact match\n  if (t1 === t2) return true\n  \n  // Check for common territory patterns\n  const patterns = [\n    /^(north|south|east|west)\\s*(america|us|usa|united states)$/,\n    /^(northeast|northwest|southeast|southwest)\\s*(region|territory)?$/,\n    /^(global|worldwide|international)$/,\n    /^(enterprise|commercial|federal)$/,\n  ]\n  \n  for (const pattern of patterns) {\n    if (pattern.test(t1) && pattern.test(t2)) return true\n  }\n  \n  return false\n}\n\n// Deal value comparison with tolerance\nexport function isDealValueSimilar(value1: number, value2: number, tolerance = 0.1): boolean {\n  const diff = Math.abs(value1 - value2)\n  const avg = (value1 + value2) / 2\n  return diff / avg <= tolerance\n}\n\n// Time window checking\nexport function isWithinTimeWindow(date1: string | Date, date2: string | Date, windowDays = 90): boolean {\n  const d1 = new Date(date1)\n  const d2 = new Date(date2)\n  const diffTime = Math.abs(d2.getTime() - d1.getTime())\n  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))\n  return diffDays <= windowDays\n}\n\n// Validation utilities\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function isValidUUID(uuid: string): boolean {\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i\n  return uuidRegex.test(uuid)\n}\n\n// Array utilities\nexport function groupBy<T, K extends keyof any>(array: T[], key: (item: T) => K): Record<K, T[]> {\n  return array.reduce((groups, item) => {\n    const group = key(item)\n    groups[group] = groups[group] || []\n    groups[group].push(item)\n    return groups\n  }, {} as Record<K, T[]>)\n}\n\nexport function sortBy<T>(array: T[], key: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] {\n  return [...array].sort((a, b) => {\n    const aVal = a[key]\n    const bVal = b[key]\n    \n    if (aVal < bVal) return direction === 'asc' ? -1 : 1\n    if (aVal > bVal) return direction === 'asc' ? 1 : -1\n    return 0\n  })\n}\n\n// Debounce utility for search\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null\n  \n  return (...args: Parameters<T>) => {\n    if (timeout) clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\n// Error handling\nexport function getErrorMessage(error: unknown): string {\n  if (error instanceof Error) return error.message\n  if (typeof error === 'string') return error\n  return 'An unknown error occurred'\n}\n\n// Status badge colors\nexport function getStatusColor(status: string): string {\n  const colors: Record<string, string> = {\n    pending: 'bg-yellow-100 text-yellow-800',\n    assigned: 'bg-green-100 text-green-800',\n    disputed: 'bg-red-100 text-red-800',\n    approved: 'bg-blue-100 text-blue-800',\n    rejected: 'bg-gray-100 text-gray-800',\n    active: 'bg-green-100 text-green-800',\n    inactive: 'bg-gray-100 text-gray-800',\n    resolved: 'bg-green-100 text-green-800',\n    dismissed: 'bg-gray-100 text-gray-800',\n  }\n  \n  return colors[status] || 'bg-gray-100 text-gray-800'\n}\n\n// Priority calculation for conflicts\nexport function calculateConflictPriority(\n  dealValue: number,\n  conflictType: string,\n  daysSinceSubmission: number\n): 'high' | 'medium' | 'low' {\n  let score = 0\n  \n  // Deal value weight\n  if (dealValue > 100000) score += 3\n  else if (dealValue > 50000) score += 2\n  else score += 1\n  \n  // Conflict type weight\n  if (conflictType === 'duplicate_end_user') score += 3\n  else if (conflictType === 'territory_overlap') score += 2\n  else score += 1\n  \n  // Time weight\n  if (daysSinceSubmission > 7) score += 2\n  else if (daysSinceSubmission > 3) score += 1\n  \n  if (score >= 7) return 'high'\n  if (score >= 4) return 'medium'\n  return 'low'\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,eAAe,IAAmB;IAChD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,gBAAgB,GAAW;IACzC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD;AAEO,SAAS,QAAQ,GAAW;IACjC,OAAO,IACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAGO,SAAS,oBAAoB,IAAY,EAAE,IAAY;IAC5D,MAAM,SAAS,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,OAAO;IAClD,MAAM,UAAU,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,OAAO;IAEnD,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;IAEhC,MAAM,WAAW,oBAAoB,QAAQ;IAC7C,OAAO,CAAC,OAAO,MAAM,GAAG,QAAQ,IAAI,OAAO,MAAM;AACnD;AAEA,SAAS,oBAAoB,IAAY,EAAE,IAAY;IACrD,MAAM,SAAS,EAAE;IAEjB,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,MAAM,EAAE,IAAK;QACrC,MAAM,CAAC,EAAE,GAAG;YAAC;SAAE;IACjB;IAEA,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,MAAM,EAAE,IAAK;QACrC,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG;IACjB;IAEA,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,MAAM,EAAE,IAAK;QACrC,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,MAAM,EAAE,IAAK;YACrC,IAAI,KAAK,MAAM,CAAC,IAAI,OAAO,KAAK,MAAM,CAAC,IAAI,IAAI;gBAC7C,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE;YACrC,OAAO;gBACL,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,GAAG,CACrB,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,GAAG,GACvB,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,GACnB,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG;YAEvB;QACF;IACF;IAEA,OAAO,MAAM,CAAC,KAAK,MAAM,CAAC,CAAC,KAAK,MAAM,CAAC;AACzC;AAGO,SAAS,qBAAqB,IAAY;IAC/C,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,qDAAqD,IAC7D,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,QAAQ,KAChB,IAAI;AACT;AAGO,SAAS,sBAAsB,UAAkB,EAAE,UAAkB;IAC1E,MAAM,KAAK,WAAW,WAAW,GAAG,IAAI;IACxC,MAAM,KAAK,WAAW,WAAW,GAAG,IAAI;IAExC,cAAc;IACd,IAAI,OAAO,IAAI,OAAO;IAEtB,sCAAsC;IACtC,MAAM,WAAW;QACf;QACA;QACA;QACA;KACD;IAED,KAAK,MAAM,WAAW,SAAU;QAC9B,IAAI,QAAQ,IAAI,CAAC,OAAO,QAAQ,IAAI,CAAC,KAAK,OAAO;IACnD;IAEA,OAAO;AACT;AAGO,SAAS,mBAAmB,MAAc,EAAE,MAAc,EAAE,YAAY,GAAG;IAChF,MAAM,OAAO,KAAK,GAAG,CAAC,SAAS;IAC/B,MAAM,MAAM,CAAC,SAAS,MAAM,IAAI;IAChC,OAAO,OAAO,OAAO;AACvB;AAGO,SAAS,mBAAmB,KAAoB,EAAE,KAAoB,EAAE,aAAa,EAAE;IAC5F,MAAM,KAAK,IAAI,KAAK;IACpB,MAAM,KAAK,IAAI,KAAK;IACpB,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,OAAO,KAAK,GAAG,OAAO;IACnD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAC1D,OAAO,YAAY;AACrB;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,YAAY,IAAY;IACtC,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB;AAGO,SAAS,QAAgC,KAAU,EAAE,GAAmB;IAC7E,OAAO,MAAM,MAAM,CAAC,CAAC,QAAQ;QAC3B,MAAM,QAAQ,IAAI;QAClB,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,EAAE;QACnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;QACnB,OAAO;IACT,GAAG,CAAC;AACN;AAEO,SAAS,OAAU,KAAU,EAAE,GAAY,EAAE,YAA4B,KAAK;IACnF,OAAO;WAAI;KAAM,CAAC,IAAI,CAAC,CAAC,GAAG;QACzB,MAAM,OAAO,CAAC,CAAC,IAAI;QACnB,MAAM,OAAO,CAAC,CAAC,IAAI;QAEnB,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,CAAC,IAAI;QACnD,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,IAAI,CAAC;QACnD,OAAO;IACT;AACF;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO,CAAC,GAAG;QACT,IAAI,SAAS,aAAa;QAC1B,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,gBAAgB,KAAc;IAC5C,IAAI,iBAAiB,OAAO,OAAO,MAAM,OAAO;IAChD,IAAI,OAAO,UAAU,UAAU,OAAO;IACtC,OAAO;AACT;AAGO,SAAS,eAAe,MAAc;IAC3C,MAAM,SAAiC;QACrC,SAAS;QACT,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;QACV,WAAW;IACb;IAEA,OAAO,MAAM,CAAC,OAAO,IAAI;AAC3B;AAGO,SAAS,0BACd,SAAiB,EACjB,YAAoB,EACpB,mBAA2B;IAE3B,IAAI,QAAQ;IAEZ,oBAAoB;IACpB,IAAI,YAAY,QAAQ,SAAS;SAC5B,IAAI,YAAY,OAAO,SAAS;SAChC,SAAS;IAEd,uBAAuB;IACvB,IAAI,iBAAiB,sBAAsB,SAAS;SAC/C,IAAI,iBAAiB,qBAAqB,SAAS;SACnD,SAAS;IAEd,cAAc;IACd,IAAI,sBAAsB,GAAG,SAAS;SACjC,IAAI,sBAAsB,GAAG,SAAS;IAE3C,IAAI,SAAS,GAAG,OAAO;IACvB,IAAI,SAAS,GAAG,OAAO;IACvB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-blue-600 text-white hover:bg-blue-700\",\n        destructive: \"bg-red-600 text-white hover:bg-red-700\",\n        outline: \"border border-gray-300 bg-white hover:bg-gray-50\",\n        secondary: \"bg-gray-100 text-gray-900 hover:bg-gray-200\",\n        ghost: \"hover:bg-gray-100\",\n        link: \"text-blue-600 underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,uQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/components/layout/navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { cn } from '@/lib/utils'\nimport { Button } from '@/components/ui/button'\nimport { \n  LayoutDashboard, \n  FileText, \n  Users, \n  Building2, \n  Package, \n  AlertTriangle,\n  Settings,\n  LogOut\n} from 'lucide-react'\n\nconst navigation = [\n  {\n    name: 'Dashboard',\n    href: '/',\n    icon: LayoutDashboard,\n  },\n  {\n    name: 'Deal Registration',\n    href: '/deals/new',\n    icon: FileText,\n  },\n  {\n    name: 'All Deals',\n    href: '/deals',\n    icon: FileText,\n  },\n  {\n    name: 'Conflicts',\n    href: '/conflicts',\n    icon: AlertTriangle,\n  },\n  {\n    name: 'Resellers',\n    href: '/resellers',\n    icon: Users,\n  },\n  {\n    name: 'End Users',\n    href: '/end-users',\n    icon: Building2,\n  },\n  {\n    name: 'Products',\n    href: '/products',\n    icon: Package,\n  },\n  {\n    name: 'Settings',\n    href: '/settings',\n    icon: Settings,\n  },\n]\n\nexport function Navigation() {\n  const pathname = usePathname()\n\n  return (\n    <div className=\"flex h-full w-64 flex-col bg-gray-900\">\n      <div className=\"flex h-16 items-center px-6\">\n        <h1 className=\"text-xl font-bold text-white\">Deal Registration</h1>\n      </div>\n      \n      <nav className=\"flex-1 space-y-1 px-3 py-4\">\n        {navigation.map((item) => {\n          const isActive = pathname === item.href || \n            (item.href !== '/' && pathname.startsWith(item.href))\n          \n          return (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={cn(\n                'group flex items-center rounded-md px-3 py-2 text-sm font-medium transition-colors',\n                isActive\n                  ? 'bg-gray-800 text-white'\n                  : 'text-gray-300 hover:bg-gray-700 hover:text-white'\n              )}\n            >\n              <item.icon\n                className={cn(\n                  'mr-3 h-5 w-5 flex-shrink-0',\n                  isActive ? 'text-white' : 'text-gray-400 group-hover:text-white'\n                )}\n              />\n              {item.name}\n            </Link>\n          )\n        })}\n      </nav>\n\n      <div className=\"border-t border-gray-700 p-4\">\n        <Button\n          variant=\"ghost\"\n          className=\"w-full justify-start text-gray-300 hover:bg-gray-700 hover:text-white\"\n        >\n          <LogOut className=\"mr-3 h-5 w-5\" />\n          Sign Out\n        </Button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAiBA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,MAAM,+NAAA,CAAA,kBAAe;IACvB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,iNAAA,CAAA,WAAQ;IAChB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,iNAAA,CAAA,WAAQ;IAChB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,2NAAA,CAAA,gBAAa;IACrB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;IACb;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,mNAAA,CAAA,YAAS;IACjB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,2MAAA,CAAA,UAAO;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;IAChB;CACD;AAEM,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAG,WAAU;8BAA+B;;;;;;;;;;;0BAG/C,6LAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC;oBACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACpC,KAAK,IAAI,KAAK,OAAO,SAAS,UAAU,CAAC,KAAK,IAAI;oBAErD,qBACE,6LAAC,+JAAA,CAAA,UAAI;wBAEH,MAAM,KAAK,IAAI;wBACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sFACA,WACI,2BACA;;0CAGN,6LAAC,KAAK,IAAI;gCACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8BACA,WAAW,eAAe;;;;;;4BAG7B,KAAK,IAAI;;uBAfL,KAAK,IAAI;;;;;gBAkBpB;;;;;;0BAGF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,WAAU;;sCAEV,6LAAC,6MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;;;;;;AAM7C;GAhDgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 433, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mQACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default: \"border-transparent bg-blue-600 text-white hover:bg-blue-700\",\n        secondary: \"border-transparent bg-gray-100 text-gray-900 hover:bg-gray-200\",\n        destructive: \"border-transparent bg-red-600 text-white hover:bg-red-700\",\n        outline: \"text-gray-900 border-gray-300\",\n        success: \"border-transparent bg-green-100 text-green-800 hover:bg-green-200\",\n        warning: \"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200\",\n        error: \"border-transparent bg-red-100 text-red-800 hover:bg-red-200\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,8KACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 520, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { Bell, Search, User } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\n\ninterface HeaderProps {\n  title: string\n  subtitle?: string\n}\n\nexport function Header({ title, subtitle }: HeaderProps) {\n  return (\n    <header className=\"border-b bg-white px-6 py-4\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">{title}</h1>\n          {subtitle && (\n            <p className=\"text-sm text-gray-600\">{subtitle}</p>\n          )}\n        </div>\n        \n        <div className=\"flex items-center space-x-4\">\n          {/* Search */}\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400\" />\n            <Input\n              placeholder=\"Search deals, resellers...\"\n              className=\"w-64 pl-10\"\n            />\n          </div>\n          \n          {/* Notifications */}\n          <Button variant=\"ghost\" size=\"icon\" className=\"relative\">\n            <Bell className=\"h-5 w-5\" />\n            <Badge \n              variant=\"destructive\" \n              className=\"absolute -right-1 -top-1 h-5 w-5 rounded-full p-0 text-xs\"\n            >\n              3\n            </Badge>\n          </Button>\n          \n          {/* User Menu */}\n          <Button variant=\"ghost\" size=\"icon\">\n            <User className=\"h-5 w-5\" />\n          </Button>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;AALA;;;;;;AAYO,SAAS,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAe;IACrD,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAAoC;;;;;;wBACjD,0BACC,6LAAC;4BAAE,WAAU;sCAAyB;;;;;;;;;;;;8BAI1C,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,WAAU;;;;;;;;;;;;sCAKd,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAO,WAAU;;8CAC5C,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,SAAQ;oCACR,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;sCAC3B,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B;KAxCgB", "debugId": null}}, {"offset": {"line": 665, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/components/layout/main-layout.tsx"], "sourcesContent": ["import { Navigation } from './navigation'\nimport { Header } from './header'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n  title: string\n  subtitle?: string\n}\n\nexport function MainLayout({ children, title, subtitle }: MainLayoutProps) {\n  return (\n    <div className=\"flex h-screen bg-gray-100\">\n      <Navigation />\n      \n      <div className=\"flex flex-1 flex-col overflow-hidden\">\n        <Header title={title} subtitle={subtitle} />\n        \n        <main className=\"flex-1 overflow-auto p-6\">\n          {children}\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQO,SAAS,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAmB;IACvE,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6IAAA,CAAA,aAAU;;;;;0BAEX,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAO,UAAU;;;;;;kCAEhC,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;KAdgB", "debugId": null}}, {"offset": {"line": 727, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border border-gray-200 bg-white text-gray-900 shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-gray-600\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sEACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 830, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/deal%20registration/deal-registration/src/app/conflicts/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { MainLayout } from '@/components/layout/main-layout'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { formatCurrency, formatDate, capitalizeFirst } from '@/lib/utils'\nimport { AlertTriangle, Users, MapPin, Clock, CheckCircle, X } from 'lucide-react'\n\ninterface Conflict {\n  id: string\n  conflict_type: string\n  resolution_status: string\n  created_at: string\n  deal: {\n    id: string\n    total_value: number\n    reseller: {\n      name: string\n      territory: string\n    }\n    end_user: {\n      company_name: string\n      territory: string\n    }\n  }\n  competing_deal: {\n    id: string\n    total_value: number\n    reseller: {\n      name: string\n      territory: string\n    }\n    end_user: {\n      company_name: string\n      territory: string\n    }\n  }\n}\n\nexport default function ConflictsPage() {\n  const [conflicts, setConflicts] = useState<Conflict[]>([])\n  const [loading, setLoading] = useState(true)\n  const [statusFilter, setStatusFilter] = useState('pending')\n  const [typeFilter, setTypeFilter] = useState('')\n\n  const loadConflicts = async () => {\n    try {\n      setLoading(true)\n      const params = new URLSearchParams({\n        limit: '50'\n      })\n\n      if (statusFilter) {\n        params.append('resolution_status', statusFilter)\n      }\n\n      if (typeFilter) {\n        params.append('conflict_type', typeFilter)\n      }\n\n      const response = await fetch(`/api/conflicts?${params}`)\n      if (response.ok) {\n        const result = await response.json()\n        setConflicts(result.data.items)\n      }\n    } catch (error) {\n      console.error('Error loading conflicts:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  useEffect(() => {\n    loadConflicts()\n  }, [statusFilter, typeFilter])\n\n  const handleResolveConflict = async (conflictId: string, dealId: string, assignedResellerId: string) => {\n    try {\n      // Assign the deal\n      const assignResponse = await fetch(`/api/deals/${dealId}/assign`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          assigned_reseller_id: assignedResellerId,\n          reason: 'Conflict resolution'\n        }),\n      })\n\n      if (assignResponse.ok) {\n        // Update conflict status\n        const conflictResponse = await fetch('/api/conflicts', {\n          method: 'PATCH',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            conflict_id: conflictId,\n            resolution_status: 'resolved'\n          }),\n        })\n\n        if (conflictResponse.ok) {\n          loadConflicts() // Reload the list\n        }\n      }\n    } catch (error) {\n      console.error('Error resolving conflict:', error)\n    }\n  }\n\n  const handleDismissConflict = async (conflictId: string) => {\n    try {\n      const response = await fetch('/api/conflicts', {\n        method: 'PATCH',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          conflict_id: conflictId,\n          resolution_status: 'dismissed'\n        }),\n      })\n\n      if (response.ok) {\n        loadConflicts() // Reload the list\n      }\n    } catch (error) {\n      console.error('Error dismissing conflict:', error)\n    }\n  }\n\n  const getConflictIcon = (type: string) => {\n    switch (type) {\n      case 'duplicate_end_user':\n        return <Users className=\"h-5 w-5\" />\n      case 'territory_overlap':\n        return <MapPin className=\"h-5 w-5\" />\n      case 'timing_conflict':\n        return <Clock className=\"h-5 w-5\" />\n      default:\n        return <AlertTriangle className=\"h-5 w-5\" />\n    }\n  }\n\n  const getConflictColor = (type: string) => {\n    switch (type) {\n      case 'duplicate_end_user':\n        return 'text-red-600 bg-red-100'\n      case 'territory_overlap':\n        return 'text-orange-600 bg-orange-100'\n      case 'timing_conflict':\n        return 'text-yellow-600 bg-yellow-100'\n      default:\n        return 'text-gray-600 bg-gray-100'\n    }\n  }\n\n  const getPriorityLevel = (conflict: Conflict) => {\n    const dealValue = Math.max(conflict.deal.total_value, conflict.competing_deal.total_value)\n    const daysSinceCreated = Math.floor(\n      (new Date().getTime() - new Date(conflict.created_at).getTime()) / (1000 * 60 * 60 * 24)\n    )\n\n    if (dealValue > 100000 || daysSinceCreated > 7) return 'high'\n    if (dealValue > 50000 || daysSinceCreated > 3) return 'medium'\n    return 'low'\n  }\n\n  return (\n    <MainLayout \n      title=\"Deal Conflicts\" \n      subtitle=\"Resolve conflicts and assign deals to appropriate resellers\"\n    >\n      <div className=\"space-y-6\">\n        {/* Filters */}\n        <div className=\"flex space-x-4\">\n          <select\n            value={statusFilter}\n            onChange={(e) => setStatusFilter(e.target.value)}\n            className=\"px-3 py-2 border rounded-md\"\n          >\n            <option value=\"\">All Status</option>\n            <option value=\"pending\">Pending</option>\n            <option value=\"resolved\">Resolved</option>\n            <option value=\"dismissed\">Dismissed</option>\n          </select>\n\n          <select\n            value={typeFilter}\n            onChange={(e) => setTypeFilter(e.target.value)}\n            className=\"px-3 py-2 border rounded-md\"\n          >\n            <option value=\"\">All Types</option>\n            <option value=\"duplicate_end_user\">Duplicate End User</option>\n            <option value=\"territory_overlap\">Territory Overlap</option>\n            <option value=\"timing_conflict\">Timing Conflict</option>\n          </select>\n        </div>\n\n        {/* Conflicts List */}\n        <div className=\"space-y-4\">\n          {loading ? (\n            <Card>\n              <CardContent className=\"text-center py-8\">\n                Loading conflicts...\n              </CardContent>\n            </Card>\n          ) : conflicts.length === 0 ? (\n            <Card>\n              <CardContent className=\"text-center py-8 text-gray-500\">\n                No conflicts found matching your criteria\n              </CardContent>\n            </Card>\n          ) : (\n            conflicts.map((conflict) => {\n              const priority = getPriorityLevel(conflict)\n              return (\n                <Card key={conflict.id} className=\"border-l-4 border-l-orange-400\">\n                  <CardHeader>\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className={`p-2 rounded-full ${getConflictColor(conflict.conflict_type)}`}>\n                          {getConflictIcon(conflict.conflict_type)}\n                        </div>\n                        <div>\n                          <CardTitle className=\"text-lg\">\n                            {capitalizeFirst(conflict.conflict_type.replace('_', ' '))}\n                          </CardTitle>\n                          <p className=\"text-sm text-gray-600\">\n                            {conflict.deal.end_user.company_name}\n                          </p>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <Badge \n                          variant={priority === 'high' ? 'error' : priority === 'medium' ? 'warning' : 'secondary'}\n                        >\n                          {priority} priority\n                        </Badge>\n                        <Badge variant={conflict.resolution_status === 'pending' ? 'warning' : 'success'}>\n                          {conflict.resolution_status}\n                        </Badge>\n                      </div>\n                    </div>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                      {/* Deal 1 */}\n                      <div className=\"border rounded-lg p-4\">\n                        <h4 className=\"font-semibold mb-3 text-blue-600\">Deal #1</h4>\n                        <div className=\"space-y-2 text-sm\">\n                          <div>\n                            <span className=\"font-medium\">End User:</span> {conflict.deal.end_user.company_name}\n                          </div>\n                          <div>\n                            <span className=\"font-medium\">Reseller:</span> {conflict.deal.reseller.name}\n                          </div>\n                          <div>\n                            <span className=\"font-medium\">Territory:</span> {conflict.deal.reseller.territory}\n                          </div>\n                          <div>\n                            <span className=\"font-medium\">Value:</span> {formatCurrency(conflict.deal.total_value)}\n                          </div>\n                        </div>\n                        {conflict.resolution_status === 'pending' && (\n                          <Button\n                            className=\"w-full mt-3\"\n                            size=\"sm\"\n                            onClick={() => handleResolveConflict(\n                              conflict.id,\n                              conflict.deal.id,\n                              conflict.deal.reseller.id\n                            )}\n                          >\n                            <CheckCircle className=\"h-4 w-4 mr-2\" />\n                            Assign to This Reseller\n                          </Button>\n                        )}\n                      </div>\n\n                      {/* Deal 2 */}\n                      <div className=\"border rounded-lg p-4\">\n                        <h4 className=\"font-semibold mb-3 text-green-600\">Deal #2</h4>\n                        <div className=\"space-y-2 text-sm\">\n                          <div>\n                            <span className=\"font-medium\">End User:</span> {conflict.competing_deal.end_user.company_name}\n                          </div>\n                          <div>\n                            <span className=\"font-medium\">Reseller:</span> {conflict.competing_deal.reseller.name}\n                          </div>\n                          <div>\n                            <span className=\"font-medium\">Territory:</span> {conflict.competing_deal.reseller.territory}\n                          </div>\n                          <div>\n                            <span className=\"font-medium\">Value:</span> {formatCurrency(conflict.competing_deal.total_value)}\n                          </div>\n                        </div>\n                        {conflict.resolution_status === 'pending' && (\n                          <Button\n                            className=\"w-full mt-3\"\n                            size=\"sm\"\n                            onClick={() => handleResolveConflict(\n                              conflict.id,\n                              conflict.competing_deal.id,\n                              conflict.competing_deal.reseller.id\n                            )}\n                          >\n                            <CheckCircle className=\"h-4 w-4 mr-2\" />\n                            Assign to This Reseller\n                          </Button>\n                        )}\n                      </div>\n                    </div>\n\n                    {conflict.resolution_status === 'pending' && (\n                      <div className=\"flex justify-center mt-4\">\n                        <Button\n                          variant=\"outline\"\n                          onClick={() => handleDismissConflict(conflict.id)}\n                        >\n                          <X className=\"h-4 w-4 mr-2\" />\n                          Dismiss Conflict\n                        </Button>\n                      </div>\n                    )}\n\n                    <div className=\"mt-4 text-xs text-gray-500\">\n                      Conflict detected on {formatDate(conflict.created_at)}\n                    </div>\n                  </CardContent>\n                </Card>\n              )\n            })\n          )}\n        </div>\n      </div>\n    </MainLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAyCe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,IAAI,gBAAgB;gBACjC,OAAO;YACT;YAEA,IAAI,cAAc;gBAChB,OAAO,MAAM,CAAC,qBAAqB;YACrC;YAEA,IAAI,YAAY;gBACd,OAAO,MAAM,CAAC,iBAAiB;YACjC;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,eAAe,EAAE,QAAQ;YACvD,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,aAAa,OAAO,IAAI,CAAC,KAAK;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG;QAAC;QAAc;KAAW;IAE7B,MAAM,wBAAwB,OAAO,YAAoB,QAAgB;QACvE,IAAI;YACF,kBAAkB;YAClB,MAAM,iBAAiB,MAAM,MAAM,CAAC,WAAW,EAAE,OAAO,OAAO,CAAC,EAAE;gBAChE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,sBAAsB;oBACtB,QAAQ;gBACV;YACF;YAEA,IAAI,eAAe,EAAE,EAAE;gBACrB,yBAAyB;gBACzB,MAAM,mBAAmB,MAAM,MAAM,kBAAkB;oBACrD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,aAAa;wBACb,mBAAmB;oBACrB;gBACF;gBAEA,IAAI,iBAAiB,EAAE,EAAE;oBACvB,gBAAgB,kBAAkB;;gBACpC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,wBAAwB,OAAO;QACnC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,kBAAkB;gBAC7C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,aAAa;oBACb,mBAAmB;gBACrB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,gBAAgB,kBAAkB;;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,6MAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;QACpC;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,YAAY,KAAK,GAAG,CAAC,SAAS,IAAI,CAAC,WAAW,EAAE,SAAS,cAAc,CAAC,WAAW;QACzF,MAAM,mBAAmB,KAAK,KAAK,CACjC,CAAC,IAAI,OAAO,OAAO,KAAK,IAAI,KAAK,SAAS,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;QAGzF,IAAI,YAAY,UAAU,mBAAmB,GAAG,OAAO;QACvD,IAAI,YAAY,SAAS,mBAAmB,GAAG,OAAO;QACtD,OAAO;IACT;IAEA,qBACE,6LAAC,iJAAA,CAAA,aAAU;QACT,OAAM;QACN,UAAS;kBAET,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4BAC/C,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAG;;;;;;8CACjB,6LAAC;oCAAO,OAAM;8CAAU;;;;;;8CACxB,6LAAC;oCAAO,OAAM;8CAAW;;;;;;8CACzB,6LAAC;oCAAO,OAAM;8CAAY;;;;;;;;;;;;sCAG5B,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC7C,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAG;;;;;;8CACjB,6LAAC;oCAAO,OAAM;8CAAqB;;;;;;8CACnC,6LAAC;oCAAO,OAAM;8CAAoB;;;;;;8CAClC,6LAAC;oCAAO,OAAM;8CAAkB;;;;;;;;;;;;;;;;;;8BAKpC,6LAAC;oBAAI,WAAU;8BACZ,wBACC,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCAAmB;;;;;;;;;;+BAI1C,UAAU,MAAM,KAAK,kBACvB,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCAAiC;;;;;;;;;;+BAK1D,UAAU,GAAG,CAAC,CAAC;wBACb,MAAM,WAAW,iBAAiB;wBAClC,qBACE,6LAAC,mIAAA,CAAA,OAAI;4BAAmB,WAAU;;8CAChC,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,CAAC,iBAAiB,EAAE,iBAAiB,SAAS,aAAa,GAAG;kEAC3E,gBAAgB,SAAS,aAAa;;;;;;kEAEzC,6LAAC;;0EACC,6LAAC,mIAAA,CAAA,YAAS;gEAAC,WAAU;0EAClB,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,SAAS,aAAa,CAAC,OAAO,CAAC,KAAK;;;;;;0EAEvD,6LAAC;gEAAE,WAAU;0EACV,SAAS,IAAI,CAAC,QAAQ,CAAC,YAAY;;;;;;;;;;;;;;;;;;0DAI1C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDACJ,SAAS,aAAa,SAAS,UAAU,aAAa,WAAW,YAAY;;4DAE5E;4DAAS;;;;;;;kEAEZ,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAS,SAAS,iBAAiB,KAAK,YAAY,YAAY;kEACpE,SAAS,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;8CAKnC,6LAAC,mIAAA,CAAA,cAAW;;sDACV,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;sFACC,6LAAC;4EAAK,WAAU;sFAAc;;;;;;wEAAgB;wEAAE,SAAS,IAAI,CAAC,QAAQ,CAAC,YAAY;;;;;;;8EAErF,6LAAC;;sFACC,6LAAC;4EAAK,WAAU;sFAAc;;;;;;wEAAgB;wEAAE,SAAS,IAAI,CAAC,QAAQ,CAAC,IAAI;;;;;;;8EAE7E,6LAAC;;sFACC,6LAAC;4EAAK,WAAU;sFAAc;;;;;;wEAAiB;wEAAE,SAAS,IAAI,CAAC,QAAQ,CAAC,SAAS;;;;;;;8EAEnF,6LAAC;;sFACC,6LAAC;4EAAK,WAAU;sFAAc;;;;;;wEAAa;wEAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,IAAI,CAAC,WAAW;;;;;;;;;;;;;wDAGxF,SAAS,iBAAiB,KAAK,2BAC9B,6LAAC,qIAAA,CAAA,SAAM;4DACL,WAAU;4DACV,MAAK;4DACL,SAAS,IAAM,sBACb,SAAS,EAAE,EACX,SAAS,IAAI,CAAC,EAAE,EAChB,SAAS,IAAI,CAAC,QAAQ,CAAC,EAAE;;8EAG3B,6LAAC,8NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;8DAO9C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;sFACC,6LAAC;4EAAK,WAAU;sFAAc;;;;;;wEAAgB;wEAAE,SAAS,cAAc,CAAC,QAAQ,CAAC,YAAY;;;;;;;8EAE/F,6LAAC;;sFACC,6LAAC;4EAAK,WAAU;sFAAc;;;;;;wEAAgB;wEAAE,SAAS,cAAc,CAAC,QAAQ,CAAC,IAAI;;;;;;;8EAEvF,6LAAC;;sFACC,6LAAC;4EAAK,WAAU;sFAAc;;;;;;wEAAiB;wEAAE,SAAS,cAAc,CAAC,QAAQ,CAAC,SAAS;;;;;;;8EAE7F,6LAAC;;sFACC,6LAAC;4EAAK,WAAU;sFAAc;;;;;;wEAAa;wEAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,cAAc,CAAC,WAAW;;;;;;;;;;;;;wDAGlG,SAAS,iBAAiB,KAAK,2BAC9B,6LAAC,qIAAA,CAAA,SAAM;4DACL,WAAU;4DACV,MAAK;4DACL,SAAS,IAAM,sBACb,SAAS,EAAE,EACX,SAAS,cAAc,CAAC,EAAE,EAC1B,SAAS,cAAc,CAAC,QAAQ,CAAC,EAAE;;8EAGrC,6LAAC,8NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;wCAO/C,SAAS,iBAAiB,KAAK,2BAC9B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS,IAAM,sBAAsB,SAAS,EAAE;;kEAEhD,6LAAC,+LAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;sDAMpC,6LAAC;4CAAI,WAAU;;gDAA6B;gDACpB,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,SAAS,UAAU;;;;;;;;;;;;;;2BA9G/C,SAAS,EAAE;;;;;oBAmH1B;;;;;;;;;;;;;;;;;AAMZ;GA7SwB;KAAA", "debugId": null}}]}