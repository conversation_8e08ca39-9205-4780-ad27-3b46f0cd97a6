{"name": "@supabase/auth-helpers-nextjs", "version": "0.10.0", "description": "A collection of framework specific Auth utilities for working with Supabase.", "main": "dist/index.js", "types": "dist/index.d.ts", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/supabase/auth-helpers.git"}, "keywords": ["Supabase", "<PERSON><PERSON>", "Nextjs"], "author": "Supabase", "license": "MIT", "bugs": {"url": "https://github.com/supabase/auth-helpers/issues"}, "homepage": "https://github.com/supabase/auth-helpers/tree/main/packages/nextjs#readme", "devDependencies": {"@supabase/supabase-js": "2.42.0", "@types/set-cookie-parser": "^2.4.3", "next": "^13.5.1", "react": "^18.2.0", "react-dom": "^18.2.0", "rimraf": "^4.4.1", "tslib": "^2.6.2", "tsup": "^6.7.0", "config": "0.1.0", "tsconfig": "0.1.1"}, "dependencies": {"set-cookie-parser": "^2.6.0", "@supabase/auth-helpers-shared": "0.7.0"}, "peerDependencies": {"@supabase/supabase-js": "^2.39.8"}, "scripts": {"lint": "tsc", "build": "tsup", "clean:all": "rimraf dist node_modules"}}