{"version": 3, "sources": ["../src/index.ts", "../src/clientComponentClient.ts", "../src/pagesBrowserClient.ts", "../src/pagesServerClient.ts", "../src/middlewareClient.ts", "../src/serverComponentClient.ts", "../src/routeHandlerClient.ts", "../src/serverActionClient.ts", "../src/deprecated.ts"], "sourcesContent": ["// Types\nexport type { Session, User, SupabaseClient } from '@supabase/supabase-js';\n\nexport { createPagesBrowserClient } from './pagesBrowserClient';\nexport { createPagesServerClient } from './pagesServerClient';\nexport { createMiddlewareClient } from './middlewareClient';\nexport { createClientComponentClient } from './clientComponentClient';\nexport { createServerComponentClient } from './serverComponentClient';\nexport { createRouteHandlerClient } from './routeHandlerClient';\nexport { createServerActionClient } from './serverActionClient';\n\n// Deprecated Functions\nexport {\n\tcreateBrowserSupabaseClient,\n\tcreateServerSupabaseClient,\n\tcreateMiddlewareSupabaseClient\n} from './deprecated';\n", "import {\n\tBrowser<PERSON>ookieA<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er,\n\tCookieOptionsWithName,\n\tSupabaseClientOptionsWithoutAuth,\n\tcreateSupabaseClient\n} from '@supabase/auth-helpers-shared';\n\nimport type { SupabaseClient } from '@supabase/supabase-js';\nimport type { GenericSchema } from '@supabase/supabase-js/dist/module/lib/types';\n\n// can't type this properly as `Database`, `SchemaName` and `Schema` are only available within `createClientComponentClient` function\nlet supabase: any;\n\nexport function createClientComponentClient<\n\tDatabase = any,\n\tSchemaName extends string & keyof Database = 'public' extends keyof Database\n\t\t? 'public'\n\t\t: string & keyof Database,\n\tSchema extends GenericSchema = Database[SchemaName] extends GenericSchema\n\t\t? Database[SchemaName]\n\t\t: any\n>({\n\tsupabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL,\n\tsupabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\n\toptions,\n\tcookieOptions,\n\tisSingleton = true\n}: {\n\tsupabaseUrl?: string;\n\tsupabaseKey?: string;\n\toptions?: SupabaseClientOptionsWithoutAuth<SchemaName>;\n\tcookieOptions?: CookieOptionsWithName;\n\tisSingleton?: boolean;\n} = {}): SupabaseClient<Database, SchemaName, Schema> {\n\tif (!supabaseUrl || !supabaseKey) {\n\t\tthrow new Error(\n\t\t\t'either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!'\n\t\t);\n\t}\n\n\tconst createNewClient = () =>\n\t\tcreateSupabaseClient<Database, SchemaName, Schema>(supabaseUrl, supabaseKey, {\n\t\t\t...options,\n\t\t\tglobal: {\n\t\t\t\t...options?.global,\n\t\t\t\theaders: {\n\t\t\t\t\t...options?.global?.headers,\n\t\t\t\t\t'X-Client-Info': `${PACKAGE_NAME}@${PACKAGE_VERSION}`\n\t\t\t\t}\n\t\t\t},\n\t\t\tauth: {\n\t\t\t\tstorage: new BrowserCookieAuthStorageAdapter(cookieOptions)\n\t\t\t}\n\t\t});\n\n\tif (isSingleton) {\n\t\t// The `Singleton` pattern is the default to simplify the instantiation\n\t\t// of a Supabase client across Client Components.\n\t\tconst _supabase = supabase ?? createNewClient();\n\t\t// For SSG and SSR always create a new Supabase client\n\t\tif (typeof window === 'undefined') return _supabase;\n\t\t// Create the Supabase client once in the client\n\t\tif (!supabase) supabase = _supabase;\n\t\treturn supabase;\n\t}\n\n\t// This allows for multiple Supabase clients, which may be required when using\n\t// multiple schemas. The user will be responsible for ensuring a single\n\t// instance of Supabase is used across Client Components, for each schema.\n\treturn createNewClient();\n}\n", "import { createClientComponentClient } from './clientComponentClient';\n\nexport const createPagesBrowserClient = createClientComponentClient;\n", "import {\n\tCookieAuthStorageAdapter,\n\tCookieOptions,\n\tCookieOptionsWithName,\n\tcreateSupabaseClient,\n\tDefaultCookieOptions,\n\tparseCookies,\n\tserializeCookie,\n\tSupabaseClientOptionsWithoutAuth\n} from '@supabase/auth-helpers-shared';\nimport { GetServerSidePropsContext, NextApiRequest, NextApiResponse } from 'next';\nimport { splitCookiesString } from 'set-cookie-parser';\n\nimport type { GenericSchema } from '@supabase/supabase-js/dist/module/lib/types';\nimport type { SupabaseClient } from '@supabase/supabase-js';\n\nclass NextServerAuthStorageAdapter extends CookieAuthStorageAdapter {\n\tconstructor(\n\t\tprivate readonly context:\n\t\t\t| GetServerSidePropsContext\n\t\t\t| { req: NextApiRequest; res: NextApiResponse },\n\t\tcookieOptions?: CookieOptions\n\t) {\n\t\tsuper(cookieOptions);\n\t}\n\n\tprotected getCookie(name: string): string | null | undefined {\n\t\tconst setCookie = splitCookiesString(\n\t\t\tthis.context.res?.getHeader('set-cookie')?.toString() ?? ''\n\t\t)\n\t\t\t.map((c) => parseCookies(c)[name])\n\t\t\t.find((c) => !!c);\n\n\t\tconst value = setCookie ?? this.context.req?.cookies[name];\n\t\treturn value;\n\t}\n\tprotected setCookie(name: string, value: string): void {\n\t\tthis._setCookie(name, value);\n\t}\n\tprotected deleteCookie(name: string): void {\n\t\tthis._setCookie(name, '', {\n\t\t\tmaxAge: 0\n\t\t});\n\t}\n\n\tprivate _setCookie(name: string, value: string, options?: DefaultCookieOptions) {\n\t\tconst setCookies = splitCookiesString(\n\t\t\tthis.context.res.getHeader('set-cookie')?.toString() ?? ''\n\t\t).filter((c) => !(name in parseCookies(c)));\n\n\t\tconst cookieStr = serializeCookie(name, value, {\n\t\t\t...this.cookieOptions,\n\t\t\t...options,\n\t\t\t// Allow supabase-js on the client to read the cookie as well\n\t\t\thttpOnly: false\n\t\t});\n\n\t\tthis.context.res.setHeader('set-cookie', [...setCookies, cookieStr]);\n\t}\n}\n\nexport function createPagesServerClient<\n\tDatabase = any,\n\tSchemaName extends string & keyof Database = 'public' extends keyof Database\n\t\t? 'public'\n\t\t: string & keyof Database,\n\tSchema extends GenericSchema = Database[SchemaName] extends GenericSchema\n\t\t? Database[SchemaName]\n\t\t: any\n>(\n\tcontext: GetServerSidePropsContext | { req: NextApiRequest; res: NextApiResponse },\n\t{\n\t\tsupabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL,\n\t\tsupabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\n\t\toptions,\n\t\tcookieOptions\n\t}: {\n\t\tsupabaseUrl?: string;\n\t\tsupabaseKey?: string;\n\t\toptions?: SupabaseClientOptionsWithoutAuth<SchemaName>;\n\t\tcookieOptions?: CookieOptionsWithName;\n\t} = {}\n): SupabaseClient<Database, SchemaName, Schema> {\n\tif (!supabaseUrl || !supabaseKey) {\n\t\tthrow new Error(\n\t\t\t'either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!'\n\t\t);\n\t}\n\n\treturn createSupabaseClient<Database, SchemaName, Schema>(supabaseUrl, supabaseKey, {\n\t\t...options,\n\t\tglobal: {\n\t\t\t...options?.global,\n\t\t\theaders: {\n\t\t\t\t...options?.global?.headers,\n\t\t\t\t'X-Client-Info': `${PACKAGE_NAME}@${PACKAGE_VERSION}`\n\t\t\t}\n\t\t},\n\t\tauth: {\n\t\t\tstorage: new NextServerAuthStorageAdapter(context, cookieOptions)\n\t\t}\n\t});\n}\n", "import {\n\tCookieAuthStorageAdapter,\n\t<PERSON>ieOptions,\n\tCookieOptionsWithName,\n\tcreateSupabaseClient,\n\tDefaultCookieOptions,\n\tparseCookies,\n\tserializeCookie,\n\tSupabaseClientOptionsWithoutAuth\n} from '@supabase/auth-helpers-shared';\nimport { NextResponse } from 'next/server';\nimport { splitCookiesString } from 'set-cookie-parser';\n\nimport type { NextRequest } from 'next/server';\nimport type { GenericSchema } from '@supabase/supabase-js/dist/module/lib/types';\nimport type { SupabaseClient } from '@supabase/supabase-js';\n\nclass NextMiddlewareAuthStorageAdapter extends CookieAuthStorageAdapter {\n\tconstructor(\n\t\tprivate readonly context: { req: NextRequest; res: NextResponse },\n\t\tcookieOptions?: CookieOptions\n\t) {\n\t\tsuper(cookieOptions);\n\t}\n\n\tprotected getCookie(name: string): string | null | undefined {\n\t\tconst setCookie = splitCookiesString(\n\t\t\tthis.context.res.headers.get('set-cookie')?.toString() ?? ''\n\t\t)\n\t\t\t.map((c) => parseCookies(c)[name])\n\t\t\t.find((c) => !!c);\n\n\t\tif (setCookie) {\n\t\t\treturn setCookie;\n\t\t}\n\n\t\tconst cookies = parseCookies(this.context.req.headers.get('cookie') ?? '');\n\t\treturn cookies[name];\n\t}\n\tprotected setCookie(name: string, value: string): void {\n\t\tthis._setCookie(name, value);\n\t}\n\tprotected deleteCookie(name: string): void {\n\t\tthis._setCookie(name, '', {\n\t\t\tmaxAge: 0\n\t\t});\n\t}\n\n\tprivate _setCookie(name: string, value: string, options?: DefaultCookieOptions) {\n\t\tconst newSessionStr = serializeCookie(name, value, {\n\t\t\t...this.cookieOptions,\n\t\t\t...options,\n\t\t\t// Allow supabase-js on the client to read the cookie as well\n\t\t\thttpOnly: false\n\t\t});\n\n\t\tif (this.context.res.headers) {\n\t\t\tthis.context.res.headers.append('set-cookie', newSessionStr);\n\t\t}\n\t}\n}\n\nexport function createMiddlewareClient<\n\tDatabase = any,\n\tSchemaName extends string & keyof Database = 'public' extends keyof Database\n\t\t? 'public'\n\t\t: string & keyof Database,\n\tSchema extends GenericSchema = Database[SchemaName] extends GenericSchema\n\t\t? Database[SchemaName]\n\t\t: any\n>(\n\tcontext: { req: NextRequest; res: NextResponse },\n\t{\n\t\tsupabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL,\n\t\tsupabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\n\t\toptions,\n\t\tcookieOptions\n\t}: {\n\t\tsupabaseUrl?: string;\n\t\tsupabaseKey?: string;\n\t\toptions?: SupabaseClientOptionsWithoutAuth<SchemaName>;\n\t\tcookieOptions?: CookieOptionsWithName;\n\t} = {}\n): SupabaseClient<Database, SchemaName, Schema> {\n\tif (!supabaseUrl || !supabaseKey) {\n\t\tthrow new Error(\n\t\t\t'either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!'\n\t\t);\n\t}\n\n\treturn createSupabaseClient<Database, SchemaName, Schema>(supabaseUrl, supabaseKey, {\n\t\t...options,\n\t\tglobal: {\n\t\t\t...options?.global,\n\t\t\theaders: {\n\t\t\t\t...options?.global?.headers,\n\t\t\t\t'X-Client-Info': `${PACKAGE_NAME}@${PACKAGE_VERSION}`\n\t\t\t}\n\t\t},\n\t\tauth: {\n\t\t\tstorage: new NextMiddlewareAuthStorageAdapter(context, cookieOptions)\n\t\t}\n\t});\n}\n", "import {\n\tCookieAuthStorageAdapter,\n\t<PERSON>ieOptions,\n\tCookieOptionsWithName,\n\tSupabaseClientOptionsWithoutAuth,\n\tcreateSupabaseClient\n} from '@supabase/auth-helpers-shared';\nimport { cookies } from 'next/headers';\n\nimport type { SupabaseClient } from '@supabase/supabase-js';\nimport type { GenericSchema } from '@supabase/supabase-js/dist/module/lib/types';\n\nclass NextServerComponentAuthStorageAdapter extends CookieAuthStorageAdapter {\n\treadonly isServer = true;\n\n\tconstructor(\n\t\tprivate readonly context: {\n\t\t\tcookies: () => ReturnType<typeof cookies>;\n\t\t},\n\t\tcookieOptions?: CookieOptions\n\t) {\n\t\tsuper(cookieOptions);\n\t}\n\n\tprotected getCookie(name: string): string | null | undefined {\n\t\tconst nextCookies = this.context.cookies();\n\t\treturn nextCookies.get(name)?.value;\n\t}\n\tprotected setCookie(name: string, value: string): void {\n\t\t// Server Components cannot set cookies. Must use Middleware, Server Action or Route Handler\n\t\t// https://github.com/vercel/next.js/discussions/41745#discussioncomment-5198848\n\t}\n\tprotected deleteCookie(name: string): void {\n\t\t// Server Components cannot set cookies. Must use Middleware, Server Action or Route Handler\n\t\t// https://github.com/vercel/next.js/discussions/41745#discussioncomment-5198848\n\t}\n}\n\nexport function createServerComponentClient<\n\tDatabase = any,\n\tSchemaName extends string & keyof Database = 'public' extends keyof Database\n\t\t? 'public'\n\t\t: string & keyof Database,\n\tSchema extends GenericSchema = Database[SchemaName] extends GenericSchema\n\t\t? Database[SchemaName]\n\t\t: any\n>(\n\tcontext: {\n\t\tcookies: () => ReturnType<typeof cookies>;\n\t},\n\t{\n\t\tsupabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL,\n\t\tsupabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\n\t\toptions,\n\t\tcookieOptions\n\t}: {\n\t\tsupabaseUrl?: string;\n\t\tsupabaseKey?: string;\n\t\toptions?: SupabaseClientOptionsWithoutAuth<SchemaName>;\n\t\tcookieOptions?: CookieOptionsWithName;\n\t} = {}\n): SupabaseClient<Database, SchemaName, Schema> {\n\tif (!supabaseUrl || !supabaseKey) {\n\t\tthrow new Error(\n\t\t\t'either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!'\n\t\t);\n\t}\n\n\treturn createSupabaseClient<Database, SchemaName, Schema>(supabaseUrl, supabaseKey, {\n\t\t...options,\n\t\tglobal: {\n\t\t\t...options?.global,\n\t\t\theaders: {\n\t\t\t\t...options?.global?.headers,\n\t\t\t\t'X-Client-Info': `${PACKAGE_NAME}@${PACKAGE_VERSION}`\n\t\t\t}\n\t\t},\n\t\tauth: {\n\t\t\tstorage: new NextServerComponentAuthStorageAdapter(context, cookieOptions)\n\t\t}\n\t});\n}\n", "import {\n\tCookieAuthStorageAdapter,\n\t<PERSON>ieOptions,\n\tCookieOptionsWithName,\n\tSupabaseClientOptionsWithoutAuth,\n\tcreateSupabaseClient\n} from '@supabase/auth-helpers-shared';\nimport { cookies } from 'next/headers';\n\nimport type { GenericSchema } from '@supabase/supabase-js/dist/module/lib/types';\nimport type { SupabaseClient } from '@supabase/supabase-js';\n\nclass NextRouteHandlerAuthStorageAdapter extends CookieAuthStorageAdapter {\n\tconstructor(\n\t\tprivate readonly context: {\n\t\t\tcookies: () => ReturnType<typeof cookies>;\n\t\t},\n\t\tcookieOptions?: CookieOptions\n\t) {\n\t\tsuper(cookieOptions);\n\t}\n\n\tprotected getCookie(name: string): string | null | undefined {\n\t\tconst nextCookies = this.context.cookies();\n\t\treturn nextCookies.get(name)?.value;\n\t}\n\tprotected setCookie(name: string, value: string): void {\n\t\tconst nextCookies = this.context.cookies();\n\t\tnextCookies.set(name, value, this.cookieOptions);\n\t}\n\tprotected deleteCookie(name: string): void {\n\t\tconst nextCookies = this.context.cookies();\n\t\tnextCookies.set(name, '', {\n\t\t\t...this.cookieOptions,\n\t\t\tmaxAge: 0\n\t\t});\n\t}\n}\n\nexport function createRouteHandlerClient<\n\tDatabase = any,\n\tSchemaName extends string & keyof Database = 'public' extends keyof Database\n\t\t? 'public'\n\t\t: string & keyof Database,\n\tSchema extends GenericSchema = Database[SchemaName] extends GenericSchema\n\t\t? Database[SchemaName]\n\t\t: any\n>(\n\tcontext: {\n\t\tcookies: () => ReturnType<typeof cookies>;\n\t},\n\t{\n\t\tsupabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL,\n\t\tsupabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\n\t\toptions,\n\t\tcookieOptions\n\t}: {\n\t\tsupabaseUrl?: string;\n\t\tsupabaseKey?: string;\n\t\toptions?: SupabaseClientOptionsWithoutAuth<SchemaName>;\n\t\tcookieOptions?: CookieOptionsWithName;\n\t} = {}\n): SupabaseClient<Database, SchemaName, Schema> {\n\tif (!supabaseUrl || !supabaseKey) {\n\t\tthrow new Error(\n\t\t\t'either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!'\n\t\t);\n\t}\n\n\treturn createSupabaseClient<Database, SchemaName, Schema>(supabaseUrl, supabaseKey, {\n\t\t...options,\n\t\tglobal: {\n\t\t\t...options?.global,\n\t\t\theaders: {\n\t\t\t\t...options?.global?.headers,\n\t\t\t\t'X-Client-Info': `${PACKAGE_NAME}@${PACKAGE_VERSION}`\n\t\t\t}\n\t\t},\n\t\tauth: {\n\t\t\tstorage: new NextRouteHandlerAuthStorageAdapter(context, cookieOptions)\n\t\t}\n\t});\n}\n", "import { createRouteHandlerClient } from './routeHandlerClient';\n\nexport const createServerActionClient = createRouteHandlerClient;\n", "import {\n\tSupabaseClientOptionsWithoutAuth,\n\tCookieOptionsWithName\n} from '@supabase/auth-helpers-shared';\nimport { NextResponse } from 'next/server';\nimport { createPagesBrowserClient } from './pagesBrowserClient';\nimport { createPagesServerClient } from './pagesServerClient';\nimport { createMiddlewareClient } from './middlewareClient';\nimport { createClientComponentClient } from './clientComponentClient';\nimport { createServerComponentClient } from './serverComponentClient';\nimport { createRouteHandlerClient } from './routeHandlerClient';\nimport { headers, cookies } from 'next/headers';\n\nimport type { GetServerSidePropsContext, NextApiRequest, NextApiResponse } from 'next';\nimport type { NextRequest } from 'next/server';\nimport type { GenericSchema } from '@supabase/supabase-js/dist/module/lib/types';\n\n/**\n * @deprecated utilize the `createPagesBrowserClient` function instead\n */\nexport function createBrowserSupabaseClient<\n\tDatabase = any,\n\tSchemaName extends string & keyof Database = 'public' extends keyof Database\n\t\t? 'public'\n\t\t: string & keyof Database,\n\tSchema extends GenericSchema = Database[SchemaName] extends GenericSchema\n\t\t? Database[SchemaName]\n\t\t: any\n>({\n\tsupabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL,\n\tsupabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\n\toptions,\n\tcookieOptions\n}: {\n\tsupabaseUrl?: string;\n\tsupabaseKey?: string;\n\toptions?: SupabaseClientOptionsWithoutAuth<SchemaName>;\n\tcookieOptions?: CookieOptionsWithName;\n} = {}) {\n\tconsole.warn(\n\t\t'Please utilize the `createPagesBrowserClient` function instead of the deprecated `createBrowserSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages'\n\t);\n\treturn createPagesBrowserClient<Database, SchemaName, Schema>({\n\t\tsupabaseUrl,\n\t\tsupabaseKey,\n\t\toptions,\n\t\tcookieOptions\n\t});\n}\n\n/**\n * @deprecated utilize the `createPagesServerClient` function instead\n */\nexport function createServerSupabaseClient<\n\tDatabase = any,\n\tSchemaName extends string & keyof Database = 'public' extends keyof Database\n\t\t? 'public'\n\t\t: string & keyof Database,\n\tSchema extends GenericSchema = Database[SchemaName] extends GenericSchema\n\t\t? Database[SchemaName]\n\t\t: any\n>(\n\tcontext: GetServerSidePropsContext | { req: NextApiRequest; res: NextApiResponse },\n\t{\n\t\tsupabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL,\n\t\tsupabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\n\t\toptions,\n\t\tcookieOptions\n\t}: {\n\t\tsupabaseUrl?: string;\n\t\tsupabaseKey?: string;\n\t\toptions?: SupabaseClientOptionsWithoutAuth<SchemaName>;\n\t\tcookieOptions?: CookieOptionsWithName;\n\t} = {}\n) {\n\tconsole.warn(\n\t\t'Please utilize the `createPagesServerClient` function instead of the deprecated `createServerSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages'\n\t);\n\treturn createPagesServerClient<Database, SchemaName, Schema>(context, {\n\t\tsupabaseUrl,\n\t\tsupabaseKey,\n\t\toptions,\n\t\tcookieOptions\n\t});\n}\n\n/**\n * @deprecated utilize the `createMiddlewareClient` function instead\n */\nexport function createMiddlewareSupabaseClient<\n\tDatabase = any,\n\tSchemaName extends string & keyof Database = 'public' extends keyof Database\n\t\t? 'public'\n\t\t: string & keyof Database,\n\tSchema extends GenericSchema = Database[SchemaName] extends GenericSchema\n\t\t? Database[SchemaName]\n\t\t: any\n>(\n\tcontext: { req: NextRequest; res: NextResponse },\n\t{\n\t\tsupabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL,\n\t\tsupabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\n\t\toptions,\n\t\tcookieOptions\n\t}: {\n\t\tsupabaseUrl?: string;\n\t\tsupabaseKey?: string;\n\t\toptions?: SupabaseClientOptionsWithoutAuth<SchemaName>;\n\t\tcookieOptions?: CookieOptionsWithName;\n\t} = {}\n) {\n\tconsole.warn(\n\t\t'Please utilize the `createMiddlewareClient` function instead of the deprecated `createMiddlewareSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs#middleware'\n\t);\n\n\treturn createMiddlewareClient<Database, SchemaName, Schema>(context, {\n\t\tsupabaseUrl,\n\t\tsupabaseKey,\n\t\toptions,\n\t\tcookieOptions\n\t});\n}\n\n/**\n * @deprecated utilize the `createClientComponentClient` function instead\n */\nexport function createClientComponentSupabaseClient<\n\tDatabase = any,\n\tSchemaName extends string & keyof Database = 'public' extends keyof Database\n\t\t? 'public'\n\t\t: string & keyof Database,\n\tSchema extends GenericSchema = Database[SchemaName] extends GenericSchema\n\t\t? Database[SchemaName]\n\t\t: any\n>({\n\tsupabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL,\n\tsupabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\n\toptions,\n\tcookieOptions\n}: {\n\tsupabaseUrl?: string;\n\tsupabaseKey?: string;\n\toptions?: SupabaseClientOptionsWithoutAuth<SchemaName>;\n\tcookieOptions?: CookieOptionsWithName;\n} = {}) {\n\tconsole.warn(\n\t\t'Please utilize the `createClientComponentClient` function instead of the deprecated `createClientComponentSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs#client-component'\n\t);\n\n\treturn createClientComponentClient<Database, SchemaName, Schema>({\n\t\tsupabaseUrl,\n\t\tsupabaseKey,\n\t\toptions,\n\t\tcookieOptions\n\t});\n}\n\n/**\n * @deprecated utilize the `createServerComponentClient` function instead\n */\nexport function createServerComponentSupabaseClient<\n\tDatabase = any,\n\tSchemaName extends string & keyof Database = 'public' extends keyof Database\n\t\t? 'public'\n\t\t: string & keyof Database,\n\tSchema extends GenericSchema = Database[SchemaName] extends GenericSchema\n\t\t? Database[SchemaName]\n\t\t: any\n>(\n\tcontext: {\n\t\theaders: () => ReturnType<typeof headers>;\n\t\tcookies: () => ReturnType<typeof cookies>;\n\t},\n\t{\n\t\tsupabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL,\n\t\tsupabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\n\t\toptions,\n\t\tcookieOptions\n\t}: {\n\t\tsupabaseUrl?: string;\n\t\tsupabaseKey?: string;\n\t\toptions?: SupabaseClientOptionsWithoutAuth<SchemaName>;\n\t\tcookieOptions?: CookieOptionsWithName;\n\t} = {}\n) {\n\tconsole.warn(\n\t\t'Please utilize the `createServerComponentClient` function instead of the deprecated `createServerComponentSupabaseClient` function. Additionally, this function no longer requires the `headers` function as a parameter. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs#server-component'\n\t);\n\n\treturn createServerComponentClient<Database, SchemaName, Schema>(\n\t\t{ cookies: context.cookies },\n\t\t{\n\t\t\tsupabaseUrl,\n\t\t\tsupabaseKey,\n\t\t\toptions,\n\t\t\tcookieOptions\n\t\t}\n\t);\n}\n\n/**\n * @deprecated utilize the `createRouteHandlerClient` function instead\n */\nexport function createRouteHandlerSupabaseClient<\n\tDatabase = any,\n\tSchemaName extends string & keyof Database = 'public' extends keyof Database\n\t\t? 'public'\n\t\t: string & keyof Database,\n\tSchema extends GenericSchema = Database[SchemaName] extends GenericSchema\n\t\t? Database[SchemaName]\n\t\t: any\n>(\n\tcontext: { headers: () => ReturnType<typeof headers>; cookies: () => ReturnType<typeof cookies> },\n\t{\n\t\tsupabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL,\n\t\tsupabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\n\t\toptions,\n\t\tcookieOptions\n\t}: {\n\t\tsupabaseUrl?: string;\n\t\tsupabaseKey?: string;\n\t\toptions?: SupabaseClientOptionsWithoutAuth<SchemaName>;\n\t\tcookieOptions?: CookieOptionsWithName;\n\t} = {}\n) {\n\tconsole.warn(\n\t\t'Please utilize the `createRouteHandlerClient` function instead of the deprecated `createRouteHandlerSupabaseClient` function. Additionally, this function no longer requires the `headers` function as a parameter. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs#route-handler'\n\t);\n\n\treturn createRouteHandlerClient<Database, SchemaName, Schema>(\n\t\t{ cookies: context.cookies },\n\t\t{\n\t\t\tsupabaseUrl,\n\t\t\tsupabaseKey,\n\t\t\toptions,\n\t\t\tcookieOptions\n\t\t}\n\t);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,iCAKO;AAMP,IAAI;AAEG,SAAS,4BAQd;AAAA,EACD,cAAc,QAAQ,IAAI;AAAA,EAC1B,cAAc,QAAQ,IAAI;AAAA,EAC1B;AAAA,EACA;AAAA,EACA,cAAc;AACf,IAMI,CAAC,GAAiD;AACrD,MAAI,CAAC,eAAe,CAAC,aAAa;AACjC,UAAM,IAAI;AAAA,MACT;AAAA,IACD;AAAA,EACD;AAEA,QAAM,kBAAkB,MAAG;AAxC5B;AAyCE,gEAAmD,aAAa,aAAa;AAAA,MAC5E,GAAG;AAAA,MACH,QAAQ;AAAA,QACP,GAAG,mCAAS;AAAA,QACZ,SAAS;AAAA,UACR,IAAG,wCAAS,WAAT,mBAAiB;AAAA,UACpB,iBAAiB,GAAG,mCAAgB;AAAA,QACrC;AAAA,MACD;AAAA,MACA,MAAM;AAAA,QACL,SAAS,IAAI,2DAAgC,aAAa;AAAA,MAC3D;AAAA,IACD,CAAC;AAAA;AAEF,MAAI,aAAa;AAGhB,UAAM,YAAY,YAAY,gBAAgB;AAE9C,QAAI,OAAO,WAAW;AAAa,aAAO;AAE1C,QAAI,CAAC;AAAU,iBAAW;AAC1B,WAAO;AAAA,EACR;AAKA,SAAO,gBAAgB;AACxB;;;ACpEO,IAAM,2BAA2B;;;ACFxC,IAAAA,8BASO;AAEP,+BAAmC;AAKnC,IAAM,+BAAN,cAA2C,qDAAyB;AAAA,EACnE,YACkB,SAGjB,eACC;AACD,UAAM,aAAa;AALF;AAAA,EAMlB;AAAA,EAEU,UAAU,MAAyC;AA1B9D;AA2BE,UAAM,gBAAY;AAAA,QACjB,gBAAK,QAAQ,QAAb,mBAAkB,UAAU,kBAA5B,mBAA2C,eAAc;AAAA,IAC1D,EACE,IAAI,CAAC,UAAM,0CAAa,CAAC,EAAE,IAAI,CAAC,EAChC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;AAEjB,UAAM,QAAQ,eAAa,UAAK,QAAQ,QAAb,mBAAkB,QAAQ;AACrD,WAAO;AAAA,EACR;AAAA,EACU,UAAU,MAAc,OAAqB;AACtD,SAAK,WAAW,MAAM,KAAK;AAAA,EAC5B;AAAA,EACU,aAAa,MAAoB;AAC1C,SAAK,WAAW,MAAM,IAAI;AAAA,MACzB,QAAQ;AAAA,IACT,CAAC;AAAA,EACF;AAAA,EAEQ,WAAW,MAAc,OAAe,SAAgC;AA7CjF;AA8CE,UAAM,iBAAa;AAAA,QAClB,UAAK,QAAQ,IAAI,UAAU,YAAY,MAAvC,mBAA0C,eAAc;AAAA,IACzD,EAAE,OAAO,CAAC,MAAM,EAAE,YAAQ,0CAAa,CAAC,EAAE;AAE1C,UAAM,gBAAY,6CAAgB,MAAM,OAAO;AAAA,MAC9C,GAAG,KAAK;AAAA,MACR,GAAG;AAAA;AAAA,MAEH,UAAU;AAAA,IACX,CAAC;AAED,SAAK,QAAQ,IAAI,UAAU,cAAc,CAAC,GAAG,YAAY,SAAS,CAAC;AAAA,EACpE;AACD;AAEO,SAAS,wBASf,SACA;AAAA,EACC,cAAc,QAAQ,IAAI;AAAA,EAC1B,cAAc,QAAQ,IAAI;AAAA,EAC1B;AAAA,EACA;AACD,IAKI,CAAC,GAC0C;AAlFhD;AAmFC,MAAI,CAAC,eAAe,CAAC,aAAa;AACjC,UAAM,IAAI;AAAA,MACT;AAAA,IACD;AAAA,EACD;AAEA,aAAO,kDAAmD,aAAa,aAAa;AAAA,IACnF,GAAG;AAAA,IACH,QAAQ;AAAA,MACP,GAAG,mCAAS;AAAA,MACZ,SAAS;AAAA,QACR,IAAG,wCAAS,WAAT,mBAAiB;AAAA,QACpB,iBAAiB,GAAG,mCAAgB;AAAA,MACrC;AAAA,IACD;AAAA,IACA,MAAM;AAAA,MACL,SAAS,IAAI,6BAA6B,SAAS,aAAa;AAAA,IACjE;AAAA,EACD,CAAC;AACF;;;ACtGA,IAAAC,8BASO;AAEP,IAAAC,4BAAmC;AAMnC,IAAM,mCAAN,cAA+C,qDAAyB;AAAA,EACvE,YACkB,SACjB,eACC;AACD,UAAM,aAAa;AAHF;AAAA,EAIlB;AAAA,EAEU,UAAU,MAAyC;AAzB9D;AA0BE,UAAM,gBAAY;AAAA,QACjB,UAAK,QAAQ,IAAI,QAAQ,IAAI,YAAY,MAAzC,mBAA4C,eAAc;AAAA,IAC3D,EACE,IAAI,CAAC,UAAM,0CAAa,CAAC,EAAE,IAAI,CAAC,EAChC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;AAEjB,QAAI,WAAW;AACd,aAAO;AAAA,IACR;AAEA,UAAM,cAAU,0CAAa,KAAK,QAAQ,IAAI,QAAQ,IAAI,QAAQ,KAAK,EAAE;AACzE,WAAO,QAAQ,IAAI;AAAA,EACpB;AAAA,EACU,UAAU,MAAc,OAAqB;AACtD,SAAK,WAAW,MAAM,KAAK;AAAA,EAC5B;AAAA,EACU,aAAa,MAAoB;AAC1C,SAAK,WAAW,MAAM,IAAI;AAAA,MACzB,QAAQ;AAAA,IACT,CAAC;AAAA,EACF;AAAA,EAEQ,WAAW,MAAc,OAAe,SAAgC;AAC/E,UAAM,oBAAgB,6CAAgB,MAAM,OAAO;AAAA,MAClD,GAAG,KAAK;AAAA,MACR,GAAG;AAAA;AAAA,MAEH,UAAU;AAAA,IACX,CAAC;AAED,QAAI,KAAK,QAAQ,IAAI,SAAS;AAC7B,WAAK,QAAQ,IAAI,QAAQ,OAAO,cAAc,aAAa;AAAA,IAC5D;AAAA,EACD;AACD;AAEO,SAAS,uBASf,SACA;AAAA,EACC,cAAc,QAAQ,IAAI;AAAA,EAC1B,cAAc,QAAQ,IAAI;AAAA,EAC1B;AAAA,EACA;AACD,IAKI,CAAC,GAC0C;AAnFhD;AAoFC,MAAI,CAAC,eAAe,CAAC,aAAa;AACjC,UAAM,IAAI;AAAA,MACT;AAAA,IACD;AAAA,EACD;AAEA,aAAO,kDAAmD,aAAa,aAAa;AAAA,IACnF,GAAG;AAAA,IACH,QAAQ;AAAA,MACP,GAAG,mCAAS;AAAA,MACZ,SAAS;AAAA,QACR,IAAG,wCAAS,WAAT,mBAAiB;AAAA,QACpB,iBAAiB,GAAG,mCAAgB;AAAA,MACrC;AAAA,IACD;AAAA,IACA,MAAM;AAAA,MACL,SAAS,IAAI,iCAAiC,SAAS,aAAa;AAAA,IACrE;AAAA,EACD,CAAC;AACF;;;ACvGA,IAAAC,8BAMO;AAMP,IAAM,wCAAN,cAAoD,qDAAyB;AAAA,EAG5E,YACkB,SAGjB,eACC;AACD,UAAM,aAAa;AALF;AAHlB,SAAS,WAAW;AAAA,EASpB;AAAA,EAEU,UAAU,MAAyC;AAxB9D;AAyBE,UAAM,cAAc,KAAK,QAAQ,QAAQ;AACzC,YAAO,iBAAY,IAAI,IAAI,MAApB,mBAAuB;AAAA,EAC/B;AAAA,EACU,UAAU,MAAc,OAAqB;AAAA,EAGvD;AAAA,EACU,aAAa,MAAoB;AAAA,EAG3C;AACD;AAEO,SAAS,4BASf,SAGA;AAAA,EACC,cAAc,QAAQ,IAAI;AAAA,EAC1B,cAAc,QAAQ,IAAI;AAAA,EAC1B;AAAA,EACA;AACD,IAKI,CAAC,GAC0C;AA7DhD;AA8DC,MAAI,CAAC,eAAe,CAAC,aAAa;AACjC,UAAM,IAAI;AAAA,MACT;AAAA,IACD;AAAA,EACD;AAEA,aAAO,kDAAmD,aAAa,aAAa;AAAA,IACnF,GAAG;AAAA,IACH,QAAQ;AAAA,MACP,GAAG,mCAAS;AAAA,MACZ,SAAS;AAAA,QACR,IAAG,wCAAS,WAAT,mBAAiB;AAAA,QACpB,iBAAiB,GAAG,mCAAgB;AAAA,MACrC;AAAA,IACD;AAAA,IACA,MAAM;AAAA,MACL,SAAS,IAAI,sCAAsC,SAAS,aAAa;AAAA,IAC1E;AAAA,EACD,CAAC;AACF;;;ACjFA,IAAAC,8BAMO;AAMP,IAAM,qCAAN,cAAiD,qDAAyB;AAAA,EACzE,YACkB,SAGjB,eACC;AACD,UAAM,aAAa;AALF;AAAA,EAMlB;AAAA,EAEU,UAAU,MAAyC;AAtB9D;AAuBE,UAAM,cAAc,KAAK,QAAQ,QAAQ;AACzC,YAAO,iBAAY,IAAI,IAAI,MAApB,mBAAuB;AAAA,EAC/B;AAAA,EACU,UAAU,MAAc,OAAqB;AACtD,UAAM,cAAc,KAAK,QAAQ,QAAQ;AACzC,gBAAY,IAAI,MAAM,OAAO,KAAK,aAAa;AAAA,EAChD;AAAA,EACU,aAAa,MAAoB;AAC1C,UAAM,cAAc,KAAK,QAAQ,QAAQ;AACzC,gBAAY,IAAI,MAAM,IAAI;AAAA,MACzB,GAAG,KAAK;AAAA,MACR,QAAQ;AAAA,IACT,CAAC;AAAA,EACF;AACD;AAEO,SAAS,yBASf,SAGA;AAAA,EACC,cAAc,QAAQ,IAAI;AAAA,EAC1B,cAAc,QAAQ,IAAI;AAAA,EAC1B;AAAA,EACA;AACD,IAKI,CAAC,GAC0C;AA9DhD;AA+DC,MAAI,CAAC,eAAe,CAAC,aAAa;AACjC,UAAM,IAAI;AAAA,MACT;AAAA,IACD;AAAA,EACD;AAEA,aAAO,kDAAmD,aAAa,aAAa;AAAA,IACnF,GAAG;AAAA,IACH,QAAQ;AAAA,MACP,GAAG,mCAAS;AAAA,MACZ,SAAS;AAAA,QACR,IAAG,wCAAS,WAAT,mBAAiB;AAAA,QACpB,iBAAiB,GAAG,mCAAgB;AAAA,MACrC;AAAA,IACD;AAAA,IACA,MAAM;AAAA,MACL,SAAS,IAAI,mCAAmC,SAAS,aAAa;AAAA,IACvE;AAAA,EACD,CAAC;AACF;;;AChFO,IAAM,2BAA2B;;;ACkBjC,SAAS,4BAQd;AAAA,EACD,cAAc,QAAQ,IAAI;AAAA,EAC1B,cAAc,QAAQ,IAAI;AAAA,EAC1B;AAAA,EACA;AACD,IAKI,CAAC,GAAG;AACP,UAAQ;AAAA,IACP;AAAA,EACD;AACA,SAAO,yBAAuD;AAAA,IAC7D;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD,CAAC;AACF;AAKO,SAAS,2BASf,SACA;AAAA,EACC,cAAc,QAAQ,IAAI;AAAA,EAC1B,cAAc,QAAQ,IAAI;AAAA,EAC1B;AAAA,EACA;AACD,IAKI,CAAC,GACJ;AACD,UAAQ;AAAA,IACP;AAAA,EACD;AACA,SAAO,wBAAsD,SAAS;AAAA,IACrE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD,CAAC;AACF;AAKO,SAAS,+BASf,SACA;AAAA,EACC,cAAc,QAAQ,IAAI;AAAA,EAC1B,cAAc,QAAQ,IAAI;AAAA,EAC1B;AAAA,EACA;AACD,IAKI,CAAC,GACJ;AACD,UAAQ;AAAA,IACP;AAAA,EACD;AAEA,SAAO,uBAAqD,SAAS;AAAA,IACpE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD,CAAC;AACF;", "names": ["import_auth_helpers_shared", "import_auth_helpers_shared", "import_set_cookie_parser", "import_auth_helpers_shared", "import_auth_helpers_shared"]}